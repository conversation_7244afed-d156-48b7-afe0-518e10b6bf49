from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.utils import timezone
from lead_management.models import Lead
from rest_framework import mixins, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import (
    Notification,
    NotificationLevel,
    NotificationPriority,
    NotificationRecipient,
    NotificationType,
)
from .pagination import NotificationPagination
from .serializers import (
    ActivitySerializer,
    NotificationPrioritySerializer,
    NotificationSerializer,
    NotificationTypeSerializer,
    UnreadCountSerializer,
)


class CaseActivityViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    ViewSet for listing case activities.
    """

    serializer_class = ActivitySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = NotificationPagination

    def get_queryset(self):
        case_id = self.kwargs.get("case_id")
        # Base queryset with organization and case filtering
        queryset = Notification.objects.filter(
            case_id=case_id,
            is_activity=True,
            organization__in=self.request.user.organizations.all(),
            level=NotificationLevel.CASE,  # Only get case-level activities
        ).order_by("-created_at")

        # Apply date filters if provided
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        activity_type = self.request.query_params.get("type")
        search_query = self.request.query_params.get("search_query")

        if start_date:
            queryset = queryset.filter(created_at__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__date__lte=end_date)
        if activity_type:
            queryset = queryset.filter(notification_type=activity_type)
        if search_query:
            queryset = queryset.filter(Q(title__icontains=search_query) | Q(message__icontains=search_query))

        return queryset.distinct()


class OrganizationActivityViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    ViewSet for listing organization activities.
    """

    serializer_class = ActivitySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = NotificationPagination

    def get_queryset(self):
        queryset = Notification.objects.filter(
            organization__in=self.request.user.organizations.first(),
            is_activity=True,
            level=NotificationLevel.ORGANIZATION,
        ).order_by("-created_at")

        # Apply filters
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        activity_type = self.request.query_params.get("type")

        if start_date:
            queryset = queryset.filter(created_at__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__date__lte=end_date)
        if activity_type:
            queryset = queryset.filter(notification_type=activity_type)

        return queryset


class NotificationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user notifications.
    """

    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = NotificationPagination  # Use our custom pagination class
    http_method_names = ["get", "post"]  # Only allow GET and POST methods

    def get_queryset(self):
        # Get the raw query parameters for debugging
        is_read = self.request.query_params.get("is_read")
        is_archived = self.request.query_params.get("is_archived")
        priority = self.request.query_params.get("priority")
        requires_action = self.request.query_params.get("requires_action")
        notification_type = self.request.query_params.get("notification_type", None)
        search_query = self.request.query_params.get("search_query", None)

        # Start with base queryset
        queryset = Notification.objects.filter(
            recipients__user=self.request.user,
            is_activity=False,
        )

        # Handle notification type filtering
        if notification_type:
            # Check if it's a comma-separated list
            if "," in notification_type:
                notification_types = notification_type.split(",")
                queryset = queryset.filter(notification_type__in=notification_types)
            else:
                queryset = queryset.filter(notification_type=notification_type)
        else:
            # Default to incoming_message notifications when no notification_type is specified
            queryset = queryset.filter(notification_type=NotificationType.INCOMING_MESSAGE)

        # Apply recipient filters
        if is_read is not None:
            queryset = queryset.filter(recipients__is_read=is_read.lower() == "true")
        if is_archived is not None:
            queryset = queryset.filter(recipients__is_archived=is_archived.lower() == "true")

        # Apply other filters
        if priority:
            queryset = queryset.filter(priority=priority)
        if requires_action is not None:
            requires_action_bool = requires_action.lower() == "true"
            queryset = queryset.filter(requires_action=requires_action_bool)
        if search_query:
            queryset = queryset.filter(Q(title__icontains=search_query) | Q(message__icontains=search_query))
        # Add distinct and order by
        queryset = queryset.order_by("-created_at").distinct()

        # print(f"DEBUG: Final queryset count: {queryset.count()}")
        # print(f"DEBUG: SQL Query: {queryset.query}")

        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override list method to include unread_count in the response.
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)

            # Add unread_count to the response
            unread_count = NotificationRecipient.objects.filter(
                user=request.user, notification__is_activity=False, is_read=False, is_archived=False
            ).count()

            response.data["unread_count"] = unread_count
            return response

        serializer = self.get_serializer(queryset, many=True)

        # For non-paginated responses (unlikely but handling it)
        unread_count = NotificationRecipient.objects.filter(
            user=request.user, notification__is_activity=False, is_read=False, is_archived=False
        ).count()

        return Response({"results": serializer.data, "unread_count": unread_count})

    @action(detail=True, methods=["post"])
    def mark_read(self, request, pk=None):
        notification = self.get_object()
        recipient = NotificationRecipient.objects.get(notification=notification, user=request.user)
        recipient.is_read = True
        recipient.read_at = timezone.now()
        recipient.save()
        return Response({"success": True, "message": "Notification marked as read"})

    @action(detail=True, methods=["post"])
    def mark_unread(self, request, pk=None):
        """
        Mark a notification as unread for the current user.
        """
        notification = self.get_object()
        recipient = NotificationRecipient.objects.get(notification=notification, user=request.user)
        recipient.is_read = False
        recipient.read_at = None
        recipient.save()
        return Response({"success": True, "message": "Notification marked as unread"})

    @action(detail=False, methods=["post"])
    def mark_all_read(self, request):
        NotificationRecipient.objects.filter(user=request.user, notification__is_activity=False, is_read=False).update(
            is_read=True, read_at=timezone.now()
        )
        return Response({"success": True, "message": "All notifications marked as read"})

    @action(detail=True, methods=["post"])
    def archive(self, request, pk=None):
        notification = self.get_object()
        recipient = NotificationRecipient.objects.get(notification=notification, user=request.user)
        recipient.is_archived = True
        recipient.archived_at = timezone.now()
        recipient.save()
        return Response({"success": True, "message": "Notification archived"})

    @action(detail=True, methods=["post"])
    def unarchive(self, request, pk=None):
        notification = self.get_object()
        recipient = NotificationRecipient.objects.get(notification=notification, user=request.user)
        recipient.is_archived = False
        recipient.archived_at = None
        recipient.save()
        return Response({"success": True, "message": "Notification unarchived"})

    @action(detail=False, methods=["post"])
    def archive_all(self, request):
        NotificationRecipient.objects.filter(
            user=request.user, notification__is_activity=False, is_archived=False
        ).update(is_archived=True, archived_at=timezone.now())
        return Response({"success": True, "message": "All notifications archived"})


class UnreadCountView(APIView):
    """
    View for getting unread notification counts.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        unread_count = NotificationRecipient.objects.filter(
            user=request.user, notification__is_activity=False, is_read=False, is_archived=False
        ).count()

        requires_action_count = NotificationRecipient.objects.filter(
            user=request.user,
            notification__is_activity=False,
            notification__requires_action=True,
            is_read=False,
            is_archived=False,
        ).count()

        serializer = UnreadCountSerializer(
            data={"unread_count": unread_count, "requires_action_count": requires_action_count}
        )
        serializer.is_valid()
        return Response(serializer.data)


class NotificationTypesView(APIView):
    """
    View for listing available notification types.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = NotificationTypeSerializer(NotificationType.choices, many=True)
        return Response(serializer.data)


class NotificationPrioritiesView(APIView):
    """
    View for listing available notification priorities.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = NotificationPrioritySerializer(NotificationPriority.choices, many=True)
        return Response(serializer.data)


class NotificationGroupsView(APIView):
    """
    View for listing notification groups with their types.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        notification_groups = [
            {"id": "case", "label": "Case Updates", "types": ["case_update", "case_status_changed"]},
            {
                "id": "task",
                "label": "Tasks",
                "types": ["task_assigned", "task_updated", "task_created", "task_status_changed", "task_tagged"],
            },
            {"id": "note", "label": "Notes", "types": ["note_created", "note_tagged"]},
            {
                "id": "lead",
                "label": "Leads",
                "types": [
                    "lead_status_changed",
                    "lead_assigned",
                    "lead_update",
                    "lead_note_created",
                    "lead_task_created",
                    "lead_task_status_changed",
                    "lead_task_tagged",
                    "lead_communication_added",
                ],
            },
            {"id": "document", "label": "Documents", "types": ["document_uploaded"]},
            {"id": "message", "label": "Messages", "types": ["incoming_message"]},
            {"id": "system", "label": "System", "types": ["system_update", "org_update", "permission_changed"]},
            {"id": "deadline", "label": "Deadlines", "types": ["deadline_reminder"]},
        ]
        return Response(notification_groups)


class LeadActivityViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    ViewSet for listing lead activities.
    """

    serializer_class = ActivitySerializer
    permission_classes = [IsAuthenticated]
    pagination_class = NotificationPagination

    def get_queryset(self):
        lead_id = self.kwargs.get("lead_id")
        # Base queryset with organization and lead filtering
        queryset = Notification.objects.filter(
            content_type=ContentType.objects.get_for_model(Lead),
            object_id=lead_id,
            is_activity=True,
            organization__in=self.request.user.organizations.all(),
            level=NotificationLevel.LEAD,  # Only get lead-level activities
        ).order_by("-created_at")

        # Apply date filters if provided
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        activity_type = self.request.query_params.get("type")
        search_query = self.request.query_params.get("search_query")

        if start_date:
            queryset = queryset.filter(created_at__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__date__lte=end_date)
        if activity_type:
            queryset = queryset.filter(notification_type=activity_type)
        if search_query:
            queryset = queryset.filter(Q(title__icontains=search_query) | Q(message__icontains=search_query))

        return queryset.distinct()
