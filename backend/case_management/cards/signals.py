from django.db.models import Sum
from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver
from django.utils import timezone
from decimal import Decimal, InvalidOperation

from case_management.cards.models import SystemCard
from case_management.cards.services import calculate_case_cost
from case_management.v2.models import (
    Case,
    CaseAttorneyLien,
    CaseCost,
    CaseIncidentDetails,
    CaseMiscellaneousLien,
    ClientBasicDetails,
    ClientInsurance,
    ClientPropertyDamage,
    DefendantInsurance,
    Employer,
    HealthInsurance,
    TreatmentProvider,
)


# Simple functions to update each card type
def create_or_update_card(case, card_type, title, data):
    """Helper function to create or update a card"""
    card, _ = SystemCard.objects.get_or_create(case=case, card_type=card_type, defaults={"title": title})
    card.data = data
    card.save()


# Signal handlers
@receiver(post_save, sender=CaseIncidentDetails)
def handle_incident_update(sender, instance, **kwargs):
    """Handle incident details updates"""
    case = instance.case

    # Update Case Age card
    if instance.incident_date:
        days = (timezone.now().date() - instance.incident_date).days
        create_or_update_card(case=case, card_type="CASE_AGE", title="Case Age", data={"age_days": days})

    # Update Statute card
    if instance.statute_of_limitations:
        days_remaining = (instance.statute_of_limitations - timezone.now().date()).days
        create_or_update_card(
            case=case, card_type="PENDING_STATUTE", title="Pending Statute", data={"days_remaining": days_remaining}
        )

    # Update Police Report Status card
    status_display = dict(instance.DOC_REQUEST_STATUS_CHOICES).get(instance.doc_request_status, "Unknown")
    create_or_update_card(
        case=case,
        card_type="POLICE_REPORT_STATUS",
        title="Police Report Status",
        data={
            "status": instance.doc_request_status,
            "status_display": status_display,
            "last_updated": timezone.now().isoformat(),
        },
    )


@receiver(post_save, sender=DefendantInsurance)
def handle_defendant_insurance_update(sender, instance, created, **kwargs):
    """Handle defendant insurance updates"""
    # print(f"Insurance signal triggered - Created: {created}, Update: {not created}")

    try:
        case = instance.defendant.case
        # print(f"Processing insurance for case {case.id}, defendant {instance.defendant_id}")
        # print("Current Insurance Details:")
        # print(f"- Policy Limits: {instance.policy_limits}")
        # print(f"- No Insurance Flag: {instance.no_insurance}")
        # print(f"- Insurance Company: {instance.insurance_company}")
        # print(f"- Policy Number: {instance.policy_number}")

        # Get all insurances for this case
        insurances = DefendantInsurance.objects.filter(defendant__case=case).select_related(
            "defendant", "insurance_company"
        )

        # print(f"\nAll Insurances for Case {case.id}:")
        # for ins in insurances:
        #     print(f"\nDefendant Insurance {ins.id}:")
        #     print(f"- Defendant: {ins.defendant.first_name} {ins.defendant.last_name}")
        #     print(f"- Policy Limits: {ins.policy_limits}")
        #     print(f"- No Insurance: {ins.no_insurance}")
        #     print(f"- Insurance Company: {ins.insurance_company}")
        #     print(f"- Policy Number: {ins.policy_number}")

        limits_data = []
        for ins in insurances:
            if ins.policy_limits and not ins.no_insurance:
                # print(f"\nAdding insurance data for defendant {ins.defendant_id}")
                # print(f"- Policy Limits Value: {ins.policy_limits}")
                limits_data.append(
                    {
                        "name": f"{ins.defendant.first_name} {ins.defendant.last_name}",
                        "limit": ins.policy_limits,
                        "company": ins.insurance_company.name if ins.insurance_company else None,
                        "policy_number": ins.policy_number,
                    }
                )

        # print(f"\nFinal limits data: {limits_data}")

        # Always try to create/update card if we have any insurance data
        if limits_data:
            create_or_update_card(
                case=case,
                card_type="THIRD_PARTY_POLICY_LIMIT",
                title="Third Party Policy Limits",
                data={"defendants": limits_data},
            )
            # print(f"Card created/updated for case {case.id}")
        else:
            pass
            # print(f"No valid policy limits data found for case {case.id}")

    except Exception as e:
        print(f"Error in insurance signal: {str(e)}")
        import traceback

        print(traceback.format_exc())


@receiver([post_save, post_delete], sender=CaseAttorneyLien)
def handle_attorney_liens(sender, instance, **kwargs):
    """Handle attorney lien updates"""
    case = instance.case
    liens = CaseAttorneyLien.objects.filter(case=case)
    if liens.exists():
        create_or_update_card(
            case=case,
            card_type="ATTORNEY_LIENS",
            title="Attorney Liens",
            data={"count": liens.count(), "total": str(sum((lien.fee_amount or 0) for lien in liens))},
        )


@receiver([post_save, post_delete], sender=CaseMiscellaneousLien)
def handle_misc_liens(sender, instance, **kwargs):
    """Handle miscellaneous lien updates"""
    case = instance.case
    liens = CaseMiscellaneousLien.objects.filter(case=case)
    if liens.exists():
        create_or_update_card(
            case=case,
            card_type="MISC_LIENS",
            title="Miscellaneous Liens",
            data={"count": liens.count(), "total": str(sum(lien.lien_amount for lien in liens))},
        )


@receiver([post_save, post_delete], sender=ClientPropertyDamage)
def handle_property_damage_update(sender, instance, **kwargs):
    """Handle property damage updates"""
    case = instance.case

    # Get the property damage details
    property_damage = ClientPropertyDamage.objects.filter(case=case).first()

    if property_damage:
        data = {
            "total_loss": property_damage.total_loss,
            "damage_type": property_damage.damage,
            "vehicle_info": {
                "make": property_damage.vehicle_make,
                "model": property_damage.vehicle_model,
                "year": property_damage.vehicle_year,
            },
            "estimate": str(property_damage.estimate) if property_damage.estimate else None,
            "final": str(property_damage.final) if property_damage.final else None,
        }
        create_or_update_card(case=case, card_type="PROPERTY_DAMAGE", title="Property Damage", data=data)


def update_lost_wages_card(case):
    """Helper function to update lost wages card"""
    # Get employment status from client basic details
    client_details = ClientBasicDetails.objects.filter(case=case).first()
    is_employed = client_details.employed if client_details else False

    # Get employers and total lost wages
    employers = Employer.objects.filter(case=case)
    total_wages = employers.aggregate(total=Sum("total_lost_wages"))["total"] or 0

    # Collect employer details
    employer_details = []
    for employer in employers:
        if employer.total_lost_wages:
            employer_details.append(
                {
                    "company": employer.company_name,
                    "lost_wages": str(employer.total_lost_wages),
                    "hours_missed": str(employer.hours_missed) if employer.hours_missed else None,
                    "weeks_missed": str(employer.weeks_missed) if employer.weeks_missed else None,
                    "status": employer.lost_wages_status,
                }
            )

    data = {
        "is_employed": is_employed,
        "total_lost_wages": str(total_wages),
        "employer_count": len(employer_details),
        "employers": employer_details,
    }

    create_or_update_card(case=case, card_type="LOST_WAGES", title="Lost Wages", data=data)


@receiver([post_save, post_delete], sender=Employer)
def handle_employer_update(sender, instance, **kwargs):
    """Handle employer updates"""
    update_lost_wages_card(instance.case)


@receiver(post_save, sender=ClientBasicDetails)
def handle_client_employment_update(sender, instance, **kwargs):
    """Handle client employment status updates"""
    update_lost_wages_card(instance.case)

    # udpate case name of case model
    case = instance.case
    case.case_name = f"{instance.first_name} {instance.last_name}"
    case.save()


@receiver([post_save, post_delete], sender=TreatmentProvider)
def handle_treatment_update(sender, instance, **kwargs):
    """Handle medical treatment updates"""
    case = instance.case

    # Get all treatment providers for this case
    providers = TreatmentProvider.objects.filter(case=case).select_related("medical_provider")

    # Calculate total original bills
    total_bills = providers.aggregate(total=Sum("original_bill"))["total"] or 0

    # Collect provider details
    provider_details = []
    for provider in providers:
        provider_details.append(
            {
                "provider_name": provider.medical_provider.company,
                "treatment_status": provider.treatment_status,
                "records_status": provider.records_status,
                "billing_status": provider.billing_status,
                "original_bill": str(provider.original_bill) if provider.original_bill else None,
                "visits": {
                    "first_visit": provider.first_visit.isoformat() if provider.first_visit else None,
                    "last_visit": provider.last_visit.isoformat() if provider.last_visit else None,
                    "total_visits": provider.number_of_visits,
                },
                "billing": {
                    "adjusted_bill": str(provider.adjusted_bill) if provider.adjusted_bill else None,
                    "insurance_paid": str(provider.insurance_paid) if provider.insurance_paid else None,
                    "medpay_pip_paid": str(provider.medpay_pip_paid) if provider.medpay_pip_paid else None,
                    "client_paid": str(provider.client_paid) if provider.client_paid else None,
                    "still_owed": str(provider.still_owed) if provider.still_owed else None,
                },
                "lien_info": {
                    "signed_lien": provider.signed_lien,
                    "filed_lien": provider.filed_lien,
                },
            }
        )

    data = {
        "total_original_bills": str(total_bills),
        "provider_count": len(provider_details),
        "providers": provider_details,
        "last_updated": timezone.now().isoformat(),
    }

    create_or_update_card(case=case, card_type="MEDICAL_TREATMENT", title="Medical Treatment", data=data)


@receiver(post_save, sender=ClientBasicDetails)
def handle_client_age_update(sender, instance, **kwargs):
    """Handle client age updates"""
    case = instance.case
    today = timezone.now().date()
    dob = instance.date_of_birth

    # Skip card creation if date of birth is not set
    if not dob:
        return

    # Calculate years
    years = today.year - dob.year

    # Adjust years if birthday hasn't occurred this year
    if today.month < dob.month or (today.month == dob.month and today.day < dob.day):
        years -= 1

    # Calculate months
    months = today.month - dob.month
    if today.day < dob.day:
        months -= 1
    if months < 0:
        months += 12

    # Calculate remaining days
    if today.day >= dob.day:
        days = today.day - dob.day
    else:
        # Get days in previous month
        if today.month == 1:
            prev_month = 12
            prev_year = today.year - 1
        else:
            prev_month = today.month - 1
            prev_year = today.year
        import calendar

        days_in_prev_month = calendar.monthrange(prev_year, prev_month)[1]
        days = today.day + (days_in_prev_month - dob.day)

    create_or_update_card(
        case=case,
        card_type="CLIENT_AGE",
        title="Client Age",
        data={
            "years": years,
            "months": months,
            "days": days,
            "display": f"{years} year{'s' if years != 1 else ''}, {months} month{'s' if months != 1 else ''}, {days} day{'s' if days != 1 else ''}",
        },
    )


@receiver([post_save, post_delete], sender=HealthInsurance)
def handle_health_insurance_update(sender, instance, **kwargs):
    """Handle health insurance updates"""
    case = instance.case

    # Get all health insurances for the case
    health_insurances = HealthInsurance.objects.filter(case=case).select_related("insurance_company")

    # Prepare health insurance data
    health_insurance_data = []
    has_valid_health_insurance = False
    total_lien_amount = Decimal('0.00')
    
    for insurance in health_insurances:
        if not insurance.no_insurance:  # Only count if no_insurance is False
            has_valid_health_insurance = True
            insurance_info = {
                "insurance_company": insurance.insurance_company.name if insurance.insurance_company else None,
                "policy_number": insurance.policy_number,
                "total_lien": str(insurance.total_lien) if insurance.total_lien else None,
                "adjusted_lien": str(insurance.adjusted_lien) if insurance.adjusted_lien else None,
                "final_amount": str(insurance.final_amount) if insurance.final_amount else None,
            }
            health_insurance_data.append(insurance_info)
            if insurance.final_amount:
                total_lien_amount += insurance.final_amount

    # Create health insurance card
    health_insurance_card_data = {
        "has_health_insurance": has_valid_health_insurance,
        "health_insurance_count": len(health_insurance_data),
        "health_insurances": health_insurance_data,
        "total_lien_amount": str(total_lien_amount),
        "last_updated": timezone.now().isoformat(),
    }

    create_or_update_card(
        case=case, 
        card_type="HEALTH_INSURANCE", 
        title="Health Insurance", 
        data=health_insurance_card_data
    )


@receiver([post_save, post_delete], sender=ClientInsurance)
def handle_pip_medpay_update(sender, instance, **kwargs):
    """Handle PIP/MedPay updates"""
    case = instance.case

    # Get all client insurances to check for PIP/MedPay
    client_insurances = ClientInsurance.objects.filter(case=case).select_related("insurance_company")

    # Check for PIP/MedPay in client insurances
    pip_medpay_data = []
    has_valid_pip = False
    total_pip_amount = Decimal('0.00')
    
    for insurance in client_insurances:
        pip_amount = Decimal('0.00')
        medpay_amount = Decimal('0.00')
        
        # Check PIP
        if insurance.pip and insurance.pip.strip() not in ['', '0', 'n/a', 'N/A']:
            try:
                pip_amount = Decimal(insurance.pip.replace(',', ''))
                total_pip_amount += pip_amount
                has_valid_pip = True
            except (ValueError, TypeError, InvalidOperation):
                pass

        # Check MedPay
        if insurance.medpay and insurance.medpay.strip() not in ['', '0', 'n/a', 'N/A']:
            try:
                medpay_amount = Decimal(insurance.medpay.replace(',', ''))
                total_pip_amount += medpay_amount
                has_valid_pip = True
            except (ValueError, TypeError, InvalidOperation):
                pass

        if pip_amount > 0 or medpay_amount > 0:
            pip_info = {
                "insurance_company": insurance.insurance_company.name if insurance.insurance_company else None,
                "policy_number": insurance.policy_number,
                "pip_amount": str(pip_amount) if pip_amount > 0 else None,
                "medpay_amount": str(medpay_amount) if medpay_amount > 0 else None,
                "claim_number": insurance.claim_number,
            }
            pip_medpay_data.append(pip_info)

    # Create PIP/MedPay card
    pip_medpay_card_data = {
        "has_pip_medpay": has_valid_pip,
        "pip_medpay_count": len(pip_medpay_data),
        "pip_medpay_details": pip_medpay_data,
        "total_amount": str(total_pip_amount),
        "last_updated": timezone.now().isoformat(),
    }

    create_or_update_card(
        case=case, 
        card_type="PIP_MEDPAY", 
        title="PIP/MedPay", 
        data=pip_medpay_card_data
    )


@receiver([post_save, post_delete], sender=ClientInsurance)
def handle_uim_policy_limit_update(sender, instance, **kwargs):
    """Handle UIM/UM Policy Limit updates"""
    case = instance.case

    # Get all client insurances for this case
    insurances = ClientInsurance.objects.filter(case=case).select_related("insurance_company")

    limits_data = []
    for insurance in insurances:
        if insurance.um_uim and not insurance.no_insurance and insurance.um_uim.strip() not in ['', '0', 'n/a', 'N/A']:
            limits_data.append(
                {
                    "insurance_company": insurance.insurance_company.name if insurance.insurance_company else None,
                    "policy_number": insurance.policy_number,
                    "limit": insurance.um_uim.strip(),
                    "claim_number": insurance.claim_number,
                }
            )

        # Always try to create/update card if we have any insurance data
        if limits_data:
            create_or_update_card(
                case=case,
                card_type="FIRST_PARTY_UIM_POLICY_LIMIT",
                title="First Party UIM Policy Limits",
                data={"insurances": limits_data},
            )
        else:
                pass

@receiver([post_save, post_delete], sender=CaseCost)
def handle_case_cost_update(sender, instance: CaseCost, **kwargs):
    """Handle case cost updates"""
    case = instance.case

    # Calculate costs using the service
    cost_data = calculate_case_cost(case)

    data = {
        "pending_amount": str(cost_data["pending_amount"]),
        "paid_amount": str(cost_data["paid_amount"]),
        "total_amount": str(cost_data["total_amount"]),
        "cost_count": CaseCost.objects.filter(case=case, is_void=False).count(),
        "last_updated": timezone.now().isoformat(),
    }

    create_or_update_card(case=case, card_type="CASE_COST", title="Case Cost", data=data)
    
    # After updating case cost, update the total cost card
    update_total_cost_card(case)

def update_total_cost_card(case):
    """
    Calculate and update the total cost card that includes:
    - Case Costs
    - Medical Treatment (Still Owed)
    - Lost Wages
    """
    # Get case costs
    cost_data = calculate_case_cost(case)
    case_costs = float(cost_data["total_amount"] or 0)
    
    # Get medical treatment still owed
    medical_still_owed = TreatmentProvider.objects.filter(case=case).aggregate(
        total=Sum('still_owed')
    )['total'] or 0
    
    # Get lost wages
    lost_wages = Employer.objects.filter(case=case).aggregate(
        total=Sum('total_lost_wages')
    )['total'] or 0
    
    # Calculate total
    total_cost = case_costs + float(medical_still_owed) + float(lost_wages)
    
    # Create tooltip explanation
    tooltip = {
        "calculation_method": "Total Cost = Case Costs + Medical Treatment Still Owed + Lost Wages",
        "components": {
            "case_costs": str(case_costs),
            "medical_still_owed": str(medical_still_owed),
            "lost_wages": str(lost_wages)
        }
    }
    
    # Create or update the card
    SystemCard.objects.update_or_create(
        case=case,
        card_type="TOTAL_COST",
        defaults={
            "title": "Total Case Cost",
            "data": {
                "total_cost": str(total_cost),
                "tooltip": tooltip,
                "last_updated": timezone.now().isoformat(),
            },
            "is_visible": True,
            "order": 1,
        },
    )

# Add signals for medical treatment and lost wages to trigger total cost update
@receiver([post_save, post_delete], sender=TreatmentProvider)
def handle_treatment_total_cost_update(sender, instance, **kwargs):
    """Update total cost when medical treatment changes"""
    update_total_cost_card(instance.case)

@receiver([post_save, post_delete], sender=Employer)
def handle_lost_wages_total_cost_update(sender, instance, **kwargs):
    """Update total cost when lost wages change"""
    update_total_cost_card(instance.case)
