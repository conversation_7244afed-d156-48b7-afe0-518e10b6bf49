from django.db import migrations

def forwards_func(apps, schema_editor):
    # Get the SystemCard model
    SystemCard = apps.get_model("case_management_cards", "SystemCard")
    # Get the CardConfiguration model
    CardConfiguration = apps.get_model("case_management_cards", "CardConfiguration")
    
    # Update all existing card configurations to include the new card type
    organizations = apps.get_model("users", "Organization").objects.all()
    for org in organizations:
        CardConfiguration.objects.get_or_create(
            organization=org,
            card_type="FIRST_PARTY_UIM_POLICY_LIMIT",
            defaults={
                "is_enabled": True,
                "refresh_interval": 3600,
                "display_order": CardConfiguration.objects.filter(organization=org).count()
            }
        )

def reverse_func(apps, schema_editor):
    # Get the SystemCard model
    SystemCard = apps.get_model("case_management_cards", "SystemCard")
    # Get the CardConfiguration model
    CardConfiguration = apps.get_model("case_management_cards", "CardConfiguration")
    
    # Delete all FIRST_PARTY_UIM_POLICY_LIMIT cards and configurations
    SystemCard.objects.filter(card_type="FIRST_PARTY_UIM_POLICY_LIMIT").delete()
    CardConfiguration.objects.filter(card_type="FIRST_PARTY_UIM_POLICY_LIMIT").delete()

class Migration(migrations.Migration):
    dependencies = [
        ('case_management_cards', '0001_initial'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(forwards_func, reverse_func),
    ] 