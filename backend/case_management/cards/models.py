from django.core.exceptions import ValidationError
from django.db import models

from users.models import User


class BaseCard(models.Model):
    """Base abstract model for all cards in the system"""

    case = models.ForeignKey("case_management.Case", on_delete=models.CASCADE, related_name="%(class)s_cards")
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    order = models.PositiveIntegerField(default=0)
    is_visible = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True
        ordering = ["order", "-created_at"]

    def __str__(self):
        return f"{self.title} - {self.case}"


class SystemCard(BaseCard):
    """Model for system-generated cards"""

    CARD_TYPES = [
        ("CASE_AGE", "Case Age"),
        ("CLIENT_AGE", "Client Age"),
        ("PENDING_STATUTE", "Pending Statute"),
        ("THIRD_PARTY_POLICY_LIMIT", "Third Party Policy Limits"),
        ("FIRST_PARTY_UIM_POLICY_LIMIT", "First Party UIM Policy Limits"),
        ("ATTORNEY_LIENS", "Attorney Liens"),
        ("MISC_LIENS", "Miscellaneous Liens"),
        ("POLICE_REPORT_STATUS", "Police Report Status"),
        ("PROPERTY_DAMAGE", "Property Damage"),
        ("LOST_WAGES", "Lost Wages"),
        ("MEDICAL_TREATMENT", "Medical Treatment"),
        ("HEALTH_INSURANCE", "Health Insurance"),
        ("PIP_MEDPAY", "PIP/MedPay"),
        ("CASE_COST", "Case Cost"),
    ]

    card_type = models.CharField(max_length=50, choices=CARD_TYPES)
    data = models.JSONField(default=dict)  # Stores dynamic data for the card
    last_calculated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "case_management_v2_system_card"
        app_label = "case_management_cards"
        unique_together = ["case", "card_type"]
        verbose_name = "System Card"
        verbose_name_plural = "System Cards"

    def clean(self):
        super().clean()
        if not self.data:
            raise ValidationError("Data field cannot be empty for system cards")


class UserCard(BaseCard):
    """Model for user-created cards"""

    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name="created_cards")
    content = models.TextField()
    is_pinned = models.BooleanField(default=False)
    color = models.CharField(max_length=50, blank=True, null=True)  # For card customization

    class Meta:
        db_table = "case_management_v2_user_card"
        app_label = "case_management_cards"
        verbose_name = "User Card"
        verbose_name_plural = "User Cards"

    def clean(self):
        super().clean()
        if not self.content:
            raise ValidationError("Content is required for user cards")


class CardConfiguration(models.Model):
    """Configuration for system cards at the organization level"""

    organization = models.ForeignKey("users.Organization", on_delete=models.CASCADE, related_name="card_configurations")
    card_type = models.CharField(max_length=50, choices=SystemCard.CARD_TYPES)
    is_enabled = models.BooleanField(default=True)
    refresh_interval = models.PositiveIntegerField(default=3600)  # in seconds
    display_order = models.PositiveIntegerField(default=0)
    custom_settings = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = "case_management_v2_card_configuration"
        app_label = "case_management_cards"
        unique_together = ["organization", "card_type"]
        ordering = ["display_order"]
        verbose_name = "Card Configuration"
        verbose_name_plural = "Card Configurations"

    def __str__(self):
        return f"{self.get_card_type_display()} - {self.organization}"
