import calendar
import csv
import hashlib
import logging
import time
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal

import pandas as pd
from case_management.models import Case, CaseChecklist, CaseNote, CaseStatusHistory, Task
from case_management.reports.utils.policy_limit_utility import extract_policy_limit
from case_management.reports.utils.settlement_utility import generate_settlement_report_data
from case_management.reports.utils.statistics_utility import get_case_statistics
from case_management.v2.models import (
    AdjusterContact,
    CaseCost,
    CaseDefendant,
    CaseEvent,
    CaseNegotiation,
    CaseNegotiationUIM,
    CaseSettlementCalculation,
    ClientInsurance,
    ClientTrust,
    ClientTrustEntryType,
    DefendantInsurance,
    InsuranceCompany,
    LienHolder,
    MedicalProvider,
    MedPayDeposit,
    MedPayDepositStatus,
    TreatmentProvider,
)
from django.core.cache import cache
from django.db import models
from django.db.models import (
    Avg,
    Count,
    DurationField,
    ExpressionWrapper,
    F,
    Fun<PERSON>,
    Max,
    OuterRef,
    Prefetch,
    Q,
    <PERSON>query,
    <PERSON>m,
    <PERSON><PERSON>ield,
    Value,
    When,
)
from django.db.models import Case as CaseFunc
from django.db.models.functions import Coalesce, Concat, Now
from django.http import StreamingHttpResponse
from django.utils import timezone
from django.utils.dateparse import parse_date
from kpi_and_reports.models import KPIType
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from users.models import User, UserTag
from utils.utils import info_logger


# Custom function to handle string cleaning for SQLite compatibility
class CleanNumericString(Func):
    function = "REPLACE"
    output_field = TextField()

    def __init__(self, expression, **extra):
        # For SQLite, we'll use a series of REPLACE functions instead of REGEXP_REPLACE
        # This will replace common non-numeric characters with empty strings
        from django.db import connection

        if connection.vendor == "sqlite":
            # Start with the original expression
            result = expression
            # Replace common non-numeric characters one by one
            for char in ["$", ",", " ", "\t", "\n", "\r", "\\", '"', "'"]:
                result = Func(result, Value(char), Value(""), function="REPLACE")
            # Handle any remaining non-numeric chars in Python if needed
            super().__init__(result, **extra)
        else:
            # For PostgreSQL and other DBs that support regexp_replace
            super().__init__(
                Func(expression, Value("[^0-9.]"), Value(""), Value("g"), function="REGEXP_REPLACE"), **extra
            )


# Add loggers at the top of the file
logger = logging.getLogger(__name__)

# Create a dedicated logger for cache operations with higher visibility
cache_logger = logging.getLogger("cache_operations")
view_logger = logging.getLogger("view_operations")


# Queryset caching utility functions
def get_queryset_cache_key(prefix, user, query_params=None):
    """Generate a cache key for a queryset based on organization and query parameters.

    Args:
        prefix: A string prefix to identify the type of query
        user: The user making the request (used to get organization)
        query_params: Optional query parameters that affect the query

    Returns:
        A string cache key
    """
    # Get organization ID
    org_id = getattr(user.organizations.first(), "id", "no-org") if hasattr(user, "organizations") else "no-org"

    # Start with base key parts
    key_parts = [prefix, str(org_id)]

    if query_params:
        # Add view type (my_cases vs all_cases)
        view_type = query_params.get("view", "my_cases")
        key_parts.append(view_type)

        # For 'my_cases' view, include the user ID
        if view_type == "my_cases":
            key_parts.append(str(getattr(user, "id", "anonymous")))

        # Add other critical filters that affect the base queryset
        critical_params = [
            "case_manager",
            "lead_attorney",
            "supervising_attorney",
            "organization_status",
            "case_type",
            "role",
            "worker",
            "exclude_intake_statuses",
            "view_only_lead_cases",
        ]

        for param in critical_params:
            value = query_params.get(param)
            if value:
                key_parts.append(f"{param}:{value}")

        # Add search parameter if present as it affects the base queryset
        search = query_params.get("search")
        if search:
            key_parts.append(f"search:{search}")

    # Create a unique key
    key_string = "_".join(key_parts)
    cache_key = f"report_qs_{hashlib.md5(key_string.encode()).hexdigest()}"

    # Log the key generation for debugging
    cache_logger.debug(f"Generated cache key: {cache_key} from parts: {key_parts}")

    return cache_key


def get_cached_queryset(cache_key, queryset_func, timeout=600):
    """Get a cached queryset or execute the query and cache the result.

    Args:
        cache_key: The cache key to use
        queryset_func: A function that returns a queryset
        timeout: Cache timeout in seconds (default: 600 seconds / 10 minutes)

    Returns:
        The queryset result
    """
    # Start timing
    start_time = time.time()

    # Try to get from cache
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        # Cache hit - log detailed information
        elapsed = time.time() - start_time
        cache_logger.info(f"✅ CACHE HIT: Key={cache_key} | Time={elapsed:.4f}s")

        # Get expiration info if available
        try:
            if hasattr(cache, "_expire_info"):
                # For LocMemCache
                expires_in = cache._expire_info.get(cache_key, 0) - time.time()
                cache_logger.debug(f"Cache entry expires in {expires_in:.0f}s")
            elif hasattr(cache, "client") and hasattr(cache.client, "ttl"):
                # For RedisCache
                ttl = cache.client.ttl(cache.make_key(cache_key))
                cache_logger.debug(f"Cache entry TTL: {ttl}s")
        except Exception as e:
            cache_logger.warning(f"Could not get cache expiration info: {str(e)}")

        return cached_result

    # Cache miss - execute the query
    cache_logger.info(f"❌ CACHE MISS: Key={cache_key} | Executing database query...")
    query_start = time.time()

    try:
        result = queryset_func()
        query_time = time.time() - query_start

        # Cache the result
        cache.set(cache_key, result, timeout)
        total_time = time.time() - start_time

        # Log success
        cache_logger.info(
            f"✨ CACHE UPDATED: Key={cache_key} | Query time={query_time:.4f}s | "
            f"Total time={total_time:.4f}s | Expires in={timeout}s"
        )
        return result

    except Exception as e:
        # Log error but don't cache failures
        cache_logger.error(f"❌ CACHE ERROR: Key={cache_key} | Error: {str(e)}")
        # Re-raise the exception
        raise


def invalidate_report_caches(organization_id=None):
    """Invalidate report-related caches.

    Args:
        organization_id: Optional organization ID to invalidate caches for a specific organization.
                         If None, all report caches will be invalidated.
    """
    start_time = time.time()
    cache_logger.info(f"Starting cache invalidation for org {organization_id if organization_id else 'ALL'}")

    try:
        # For Redis cache
        if hasattr(cache, "client") and hasattr(cache.client, "keys"):
            pattern = f"{cache.key_prefix}report_qs_*"
            if organization_id:
                pattern = f"{cache.key_prefix}report_qs_*{organization_id}*"

            keys = cache.client.keys(pattern)
            if keys:
                cache_logger.info(f"Found {len(keys)} keys to invalidate")
                cache.client.delete(*keys)
                cache_logger.info(f"Successfully deleted {len(keys)} keys")

        # For local memory cache
        elif hasattr(cache, "_cache"):
            keys_to_delete = []
            for key in list(cache._cache.keys()):
                if key.startswith("report_qs_"):
                    if not organization_id or str(organization_id) in key:
                        keys_to_delete.append(key)

            if keys_to_delete:
                cache_logger.info(f"Found {len(keys_to_delete)} keys to invalidate")
                for key in keys_to_delete:
                    cache.delete(key)
                cache_logger.info(f"Successfully deleted {len(keys_to_delete)} keys")

        else:
            # Fallback to clearing entire cache
            cache_logger.warning("Cache backend not recognized, clearing entire cache")
            cache.clear()
            return

        elapsed = time.time() - start_time
        cache_logger.info(f"Cache invalidation completed in {elapsed:.3f}s")

    except Exception as e:
        cache_logger.error(f"Error during cache invalidation: {str(e)}")
        # Fallback to clearing entire cache
        cache.clear()
        cache_logger.info("Cleared entire cache as fallback")


# Add a CSV streaming helper class
class Echo:
    """An object that implements just the write method of the file-like interface."""

    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


# Add a custom pagination class
class CaseReportPagination(PageNumberPagination):
    page_size = 25
    page_size_query_param = "page_size"
    max_page_size = 1000

    def get_paginated_response(self, data):
        return Response(
            {
                "count": self.page.paginator.count,
                "next": self.get_next_link(),
                "previous": self.get_previous_link(),
                "total_pages": self.page.paginator.num_pages,
                "current_page": self.page.number,
                "page_size": self.get_page_size(self.request),
                "results": data.get("results", []),
                "summary": data.get("summary", {}),
                "tiles": data.get("tiles", []),
                "filter_options": data.get("filter_options", {}),
            }
        )

    def get_page_size(self, request):
        """
        Get the page size from the query parameters.
        If not specified, use the default page size.
        """
        if self.page_size_query_param:
            try:
                page_size = int(request.query_params.get(self.page_size_query_param, self.page_size))
                if page_size > 0:
                    return min(page_size, self.max_page_size)
            except (ValueError, TypeError):
                pass
        return self.page_size


class CaseReportViewSet(viewsets.ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    pagination_class = CaseReportPagination

    def get_base_queryset(self):
        """Get base queryset with common filters and annotations"""
        start_time = time.time()
        logger.info("Starting get_base_queryset")

        # Generate cache key based on organization, action, and query parameters
        action_key = f"{self.action}" if self.action else "default"
        cache_key = get_queryset_cache_key(f"base_queryset_{action_key}", self.request.user, self.request.query_params)

        # Log cache key and request details for debugging
        org_id = (
            getattr(self.request.user.organizations.first(), "id", "no-org")
            if hasattr(self.request.user, "organizations")
            else "no-org"
        )
        view_type = self.request.query_params.get("view", "my_cases")
        cache_logger.info(f"🔑 CACHE KEY: {cache_key} | Action={action_key} | Org={org_id} | View={view_type}")

        # Define the queryset function to execute if cache miss
        def get_queryset_func():
            # Get organization
            organization = self.request.user.organizations.first()
            cache_logger.info(
                f"Cache miss - Building query for organization: {organization.name if organization else 'None'}"
            )

            # Check if viewing my cases or all cases
            view_type = self.request.query_params.get("view", "my_cases")
            cache_logger.info(f"View type: {view_type}")

            # Initial queryset
            init_start = time.time()
            queryset = Case.objects.filter(organization=organization)

            # Filter for my cases if requested
            if view_type == "my_cases":
                my_cases_start = time.time()
                queryset = queryset.filter(
                    Q(v2_workers__primary_contact=self.request.user)
                    | Q(v2_workers__case_manager=self.request.user)
                    | Q(v2_workers__lead_attorney=self.request.user)
                    | Q(v2_workers__case_assistant=self.request.user)
                    | Q(v2_workers__supervising_attorney=self.request.user)
                    | Q(v2_workers__litigation_assistant=self.request.user)
                    | Q(v2_workers__lien_negotiator=self.request.user)
                    | Q(v2_workers__intake_specialist=self.request.user)
                    | Q(v2_workers__investigator=self.request.user)
                    | Q(v2_workers__accountant=self.request.user)
                    | Q(v2_workers__litigation_attorney=self.request.user)
                ).distinct()
                filter_time = time.time() - my_cases_start
                cache_logger.info(f"Time to filter for my cases: {filter_time:.3f}s")

            # Apply select_related and annotate
            annotate_start = time.time()
            queryset = queryset.select_related(
                "v2_workers",
                "v2_workers__primary_contact",
                "v2_workers__case_manager",
                "v2_workers__lead_attorney",
                "v2_workers__case_assistant",
                "v2_workers__supervising_attorney",
                "v2_workers__litigation_assistant",
                "v2_workers__lien_negotiator",
                "v2_workers__intake_specialist",
                "v2_workers__investigator",
                "v2_workers__accountant",
                "v2_workers__litigation_attorney",
                "incident_details",
                "organization_status",
                "client_basic_details",
            ).annotate(
                days=ExpressionWrapper((Now() - F("created_at")), output_field=DurationField()),
                last_touched=F("updated_at"),
            )

            annotate_time = time.time() - annotate_start
            total_time = time.time() - init_start
            cache_logger.info(f"Time to build base queryset: {total_time:.3f}s")
            return queryset

        # Get the queryset from cache or execute the query
        # Use a shorter timeout for "my_cases" view since it's user-specific
        timeout = 5 if view_type == "my_cases" else 5  # 5 minutes for my_cases, 10 minutes for all_cases
        return get_cached_queryset(cache_key, get_queryset_func, timeout=timeout)

    def get_queryset(self):
        return self.get_base_queryset()

    def get_case_status_categories(self, queryset):
        """Get unique case status categories with proper formatting"""
        # Get organization status names
        status_categories = (
            queryset.values_list(
                "organization_status__name", "organization_status__display_name", "organization_status__id"
            )
            .filter(organization_status__isnull=False)
            .distinct()
            .order_by("organization_status__name")
        )

        # Create a dictionary to ensure uniqueness by name
        unique_categories = {}
        # Fix: Unpack all three values
        for name, display_name, id in status_categories:
            if name and name not in unique_categories:
                unique_categories[name] = {"display": display_name or name, "id": id}

        # Format as list of dictionaries with value/label/id
        return [{"value": name, "label": info["display"], "id": info["id"]} for name, info in unique_categories.items()]

    def case_queryset_filter_user_tag(self, queryset, tag):
        """Filter cases based on a user tag"""
        if tag:
            # Get all users that have this tag
            tagged_users = User.objects.filter(tags__id=tag).values_list("id", flat=True)

            # Get cases where any worker is in the tagged users list
            worker_fields = [
                "v2_workers__primary_contact_id",
                "v2_workers__lead_attorney_id",
                "v2_workers__case_assistant_id",
                "v2_workers__supervising_attorney_id",
                "v2_workers__litigation_assistant_id",
                "v2_workers__case_manager_id",
                "v2_workers__lien_negotiator_id",
                "v2_workers__intake_specialist_id",
                "v2_workers__investigator_id",
                "v2_workers__accountant_id",
                "v2_workers__litigation_attorney_id",
            ]

            # Build query looking for any worker with the tag
            filter_query = Q()
            for field in worker_fields:
                filter_query |= Q(**{f"{field}__in": tagged_users})

            queryset = queryset.filter(filter_query).distinct()

        return queryset

    def apply_filter_on_case_queryset(self, queryset, skip_dates=False):
        """Apply filters to the case queryset"""
        # Open/Closed filter
        filters = {}
        search_query = self.request.query_params.get("search")
        case_status = self.request.query_params.get("case_kpi_status")
        # TODO: check this once
        if case_status == "open":
            queryset = queryset.exclude(
                organization_status__kpi_type__in=[
                    KPIType.case_closed,
                    KPIType.case_rejected,
                    KPIType.case_dropped,
                    KPIType.subbed_out,
                    KPIType.pending_drop,
                ]
            )
        elif case_status == "closed":
            queryset = queryset.filter(
                organization_status__kpi_type__in=[
                    KPIType.case_closed,
                    KPIType.case_rejected,
                    KPIType.case_dropped,
                    KPIType.subbed_out,
                    KPIType.pending_drop,
                ]
            )
        elif case_status == "dropped":
            queryset = queryset.filter(organization_status__kpi_type=KPIType.case_dropped)
        else:
            pass

        organization_status = self.request.query_params.get("organization_status")
        if organization_status:
            filters["organization_status__id"] = organization_status

        # Worker filter
        worker = self.request.query_params.get("worker")
        if worker:
            queryset = queryset.filter(
                Q(v2_workers__primary_contact_id=worker)
                | Q(v2_workers__case_manager_id=worker)
                | Q(v2_workers__lead_attorney_id=worker)
                | Q(v2_workers__case_assistant_id=worker)
                | Q(v2_workers__supervising_attorney_id=worker)
                | Q(v2_workers__litigation_assistant_id=worker)
                | Q(v2_workers__lien_negotiator_id=worker)
                | Q(v2_workers__intake_specialist_id=worker)
                | Q(v2_workers__investigator_id=worker)
                | Q(v2_workers__accountant_id=worker)
                | Q(v2_workers__litigation_attorney_id=worker)
            ).distinct()

        # Case Type filter (using incident_type instead)
        case_type = self.request.query_params.get("case_type")
        if case_type:
            filters["incident_details__incident_type"] = case_type

        # Organization Case Status filter
        org_status = self.request.query_params.get("status_category")
        if org_status:
            filters["organization_status__name"] = org_status

        # Case Age filter
        case_age = self.request.query_params.get("case_age")
        if case_age:
            try:
                days_ago = timezone.now() - timedelta(days=int(case_age))
                # We want cases NEWER than days_ago, so use __gte
                queryset = queryset.filter(created_at__gte=days_ago)

                # Add debug logging
                logger.info(f"Filtering for cases newer than: {days_ago}")
                logger.info(f"Cases count after age filter: {queryset.count()}")

            except ValueError:
                logger.error(f"Invalid case_age value: {case_age}")

        # Birthday Month filter
        birthday_month = self.request.query_params.get("birthday_month")
        if birthday_month:
            filters["client_basic_details__date_of_birth__month"] = birthday_month

        # Source Type filter
        source_type = self.request.query_params.get("source_type")
        if source_type:
            filters["client_basic_details__source_type"] = source_type

        # Source Detail filter
        source_detail = self.request.query_params.get("source_detail")
        if source_detail:
            filters["client_basic_details__source_detail"] = source_detail

        # Language filter
        language = self.request.query_params.get("language")
        if language:
            filters["client_basic_details__language"] = language

        # Additional checkbox filters
        has_email = self.request.query_params.get("has_email")
        if has_email == "true":
            queryset = queryset.filter(client_contact_details__primary_email__isnull=False).exclude(
                client_contact_details__primary_email__exact=""
            )
        elif has_email == "false":
            queryset = queryset.filter(
                Q(client_contact_details__primary_email__isnull=True)
                | Q(client_contact_details__primary_email__exact="")
            )

        exclude_minors = self.request.query_params.get("exclude_minors")
        if exclude_minors == "true":
            eighteen_years_ago = timezone.now().date() - timedelta(days=365 * 18)
            filters["client_basic_details__date_of_birth__lte"] = eighteen_years_ago
        elif exclude_minors == "false":
            filters["client_basic_details__date_of_birth__gte"] = eighteen_years_ago

        exclude_deceased = self.request.query_params.get("exclude_deceased") == "true"
        if exclude_deceased:
            filters["client_basic_details__deceased"] = False

        # Date Range filters
        date_field = self.request.query_params.get("date_field")
        from_date = self.request.query_params.get("from_date")
        to_date = self.request.query_params.get("to_date")
        view_logger.info(f"date_field: {date_field}, from_date: {from_date}, to_date: {to_date}")
        date_filter_applied = False

        if not skip_dates:
            if date_field == "closed_at":
                if from_date:
                    # Find cases where either:
                    # - closed_date is after from_date
                    # - OR most recent status history with KPI type case_closed is after from_date
                    # for kpi status clsoed, rejected, dropped, pending_drop, subbed_out these all atre closed cases
                    # rejected date and dorppped date needs to be added to the filter
                    date_filter = (
                        Q(closed_date__gte=from_date)
                        | Q(dropped_date__gte=from_date)
                        | Q(pending_drop_date__gte=from_date)
                        & Q(
                            status_history__new_status__kpi_type__in=[
                                KPIType.case_closed,
                                KPIType.case_rejected,
                                KPIType.case_dropped,
                                KPIType.pending_drop,
                                KPIType.subbed_out,
                            ],
                            status_history__changed_at__gte=from_date,
                        )
                    )
                    queryset = queryset.filter(date_filter).distinct()

                if to_date:
                    # Similar logic for to_date
                    date_filter = (
                        Q(closed_date__lte=to_date)
                        | Q(dropped_date__lte=to_date)
                        | Q(pending_drop_date__lte=to_date)
                        & Q(
                            status_history__new_status__kpi_type__in=[
                                KPIType.case_closed,
                                KPIType.case_rejected,
                                KPIType.case_dropped,
                                KPIType.pending_drop,
                                KPIType.subbed_out,
                            ],
                            status_history__changed_at__lte=to_date,
                        )
                    )
                    queryset = queryset.filter(date_filter).distinct()
            if date_field == "rejected_at":
                if from_date:
                    # Find cases where either:
                    # - closed_date is after from_date
                    # - OR most recent status history with KPI type case_closed is after from_date
                    # for kpi status clsoed, rejected, dropped, pending_drop, subbed_out these all atre closed cases
                    # rejected date and dorppped date needs to be added to the filter
                    date_filter = Q(closed_date__gte=from_date) | Q(
                        status_history__new_status__kpi_type__in=[
                            KPIType.case_rejected,
                        ],
                        status_history__changed_at__gte=from_date,
                    )
                    queryset = queryset.filter(date_filter).distinct()

                if to_date:
                    # Similar logic for to_date
                    date_filter = Q(closed_date__lte=to_date) | Q(
                        status_history__new_status__kpi_type__in=[
                            KPIType.case_rejected,
                        ],
                        status_history__changed_at__lte=to_date,
                    )
                    queryset = queryset.filter(date_filter).distinct()
            if date_field == "dropped_at":
                if from_date:
                    # Find cases where either:
                    # - closed_date is after from_date
                    # - OR most recent status history with KPI type case_closed is after from_date
                    # for kpi status clsoed, rejected, dropped, pending_drop, subbed_out these all atre closed cases
                    # rejected date and dorppped date needs to be added to the filter
                    date_filter = Q(dropped_date__gte=from_date) | Q(pending_drop_date__gte=from_date) & Q(
                        status_history__new_status__kpi_type__in=[
                            KPIType.case_dropped,
                            KPIType.pending_drop,
                        ],
                        status_history__changed_at__gte=from_date,
                    )
                    queryset = queryset.filter(date_filter).distinct()
                if to_date:
                    # Similar logic for to_date
                    date_filter = Q(dropped_date__lte=to_date) | Q(pending_drop_date__lte=to_date) & Q(
                        status_history__new_status__kpi_type__in=[
                            KPIType.case_dropped,
                            KPIType.pending_drop,
                        ],
                        status_history__changed_at__lte=to_date,
                    )
                    queryset = queryset.filter(date_filter).distinct()

            if date_field == "retained_date":
                if from_date and to_date:
                    queryset = queryset.filter(retained_at__range=[from_date, to_date])
                elif from_date:
                    queryset = queryset.filter(retained_at__gte=from_date)
                elif to_date:
                    queryset = queryset.filter(retained_at__lte=to_date)

            if date_field and (from_date or to_date):
                # Map frontend date field names to model fields
                date_field_mapping = {
                    "doi": "incident_details__incident_date",
                    "created_at": "created_at",
                    "updated_at": "updated_at",
                    "last_touched": "last_touched",
                    "statute": "incident_details__statute_of_limitations",
                    # "retained_date": "retained_at",
                }

                model_date_field = date_field_mapping.get(date_field)

                if model_date_field:
                    if from_date:
                        filters[f"{model_date_field}__gte"] = from_date
                    if to_date:
                        filters[f"{model_date_field}__lte"] = to_date
                    date_filter_applied = True
        else:
            logger.info("Skipping date filters")

        # Primary filter (client/contact)
        primary = self.request.query_params.get("primary")
        if primary:
            filters["v2_workers__primary_contact_id"] = primary

        # Lead Attorney filter
        lead_attorney = self.request.query_params.get("lead_attorney")
        if lead_attorney:
            filters["v2_workers__lead_attorney_id"] = lead_attorney

        case_manager = self.request.query_params.get("case_manager")
        if case_manager:
            filters["v2_workers__case_manager_id"] = case_manager

        # Supervising Attorney filter
        supervising_attorney = self.request.query_params.get("supervising_attorney")
        if supervising_attorney:
            filters["v2_workers__supervising_attorney_id"] = supervising_attorney

        # DOI (Date of Incident) filter
        doi = self.request.query_params.get("doi")
        if doi:
            filters["incident_details__incident_date"] = doi

        tag = self.request.query_params.get("role")
        if tag:
            queryset = self.case_queryset_filter_user_tag(queryset, tag)

        exclude_minors = self.request.query_params.get("exclude_minors") == "true"
        if exclude_minors:
            eighteen_years_ago = timezone.now().date() - timedelta(days=365 * 18)
            filters["client_basic_details__date_of_birth__lte"] = eighteen_years_ago

        if filters:
            filter_apply_start = time.time()
            queryset = queryset.filter(**filters)
            logger.info(f"Time to apply filters: {time.time() - filter_apply_start:.3f}s")

        # Apply text search if provided
        if search_query:
            for query in search_query.split(" "):
                queryset = queryset.filter(
                    Q(client_basic_details__first_name__icontains=query)
                    | Q(client_basic_details__last_name__icontains=query)
                    | Q(id__icontains=query)
                    | Q(client_contact_details__primary_email__icontains=query)
                    | Q(client_contact_details__phone_number_1__icontains=query)
                    | Q(v2_workers__case_manager__first_name__icontains=query)
                    | Q(v2_workers__case_manager__last_name__icontains=query)
                    | Q(v2_workers__lead_attorney__last_name__icontains=query)
                ).distinct()

            # add a new filter called kpi type
        kpi_type = self.request.query_params.get("kpi_type")
        if kpi_type:
            if kpi_type == KPIType.case_open:
                # exclude closed, rejected, dropped cases
                queryset = queryset.exclude(
                    organization_status__kpi_type__in=[
                        KPIType.case_closed,
                        KPIType.case_rejected,
                        KPIType.case_dropped,
                        KPIType.subbed_out,
                        KPIType.pending_drop,
                    ]
                )
            elif kpi_type == KPIType.case_closed:
                queryset = queryset.filter(
                    organization_status__kpi_type__in=[
                        KPIType.case_closed,
                        KPIType.case_rejected,
                        KPIType.case_dropped,
                        KPIType.subbed_out,
                        KPIType.pending_drop,
                    ]
                )
            elif kpi_type == KPIType.case_rejected:
                queryset = queryset.filter(organization_status__kpi_type=KPIType.case_rejected)
            elif kpi_type == KPIType.case_dropped:
                queryset = queryset.filter(
                    organization_status__kpi_type__in=[
                        KPIType.case_dropped,
                        KPIType.pending_drop,
                    ]
                )
            elif kpi_type == KPIType.client_retained:
                queryset = queryset.filter(retained=True)
            elif kpi_type == KPIType.intake_ready:
                if not date_filter_applied:
                    queryset = queryset.filter(intake_date__gte=timezone.now() - timedelta(days=90))
            else:
                queryset = queryset.filter(organization_status__kpi_type=kpi_type)

        return queryset

    def apply_sorting_on_case_queryset(self, queryset):
        return queryset

    def get_queryset_for_case_list(self):
        """Get base queryset with filters and sorting"""
        queryset = self.get_base_queryset()
        queryset = self.apply_filter_on_case_queryset(queryset)
        queryset = self.apply_sorting_on_case_queryset(queryset)

        open_cases_checkbox = self.request.query_params.get("open_cases")
        if open_cases_checkbox == "true":
            queryset = queryset.exclude(organization_status__kpi_type=KPIType.case_closed)
        return queryset

    def get_case_data(self, case):
        """Format case data for response"""
        start_time = time.time()

        # Convert duration to days
        days = case.days.days if case.days else 0

        # Get workers data
        workers = case.v2_workers if hasattr(case, "v2_workers") else None

        # Format case name for display
        case_name = f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details and case.incident_details.incident_date else 'N/A'} : {case.id}"

        # Get organization status name
        org_status = case.organization_status.name if case.organization_status else None

        team_start = time.time()
        team = self._get_team_data(workers) if workers else []
        team_time = time.time() - team_start

        result = {
            "case": case_name,
            "case_age": days,
            "case_type": getattr(case.incident_details, "incident_type", None),
            "status": org_status,
            "primary": workers.primary_contact.get_full_name() if workers and workers.primary_contact else None,
            "statute": case.incident_details.statute_of_limitations.strftime("%m/%d/%Y")
            if hasattr(case, "incident_details")
            and case.incident_details
            and case.incident_details.statute_of_limitations
            else None,
            "team": team,
        }

        total_time = time.time() - start_time
        if total_time > 0.05:  # Log only if it takes more than 50ms
            logger.info(
                f"Slow case formatting - Case ID: {case.id}, Total time: {total_time:.3f}s, Team time: {team_time:.3f}s"
            )

        return result

    def _get_team_data(self, workers):
        """Get formatted team data"""
        team = []
        for role in ["case_manager", "lead_attorney", "case_assistant", "primary_contact"]:
            member = getattr(workers, role)
            if member:
                team.append({"id": member.id, "name": member.get_full_name(), "role": role})
        return team

    def get_average_case_age(self, queryset):
        """Calculate average case age in days"""
        avg_timedelta = queryset.aggregate(avg_age=Avg("days"))["avg_age"]
        if avg_timedelta:
            return avg_timedelta.days
        return 0

    def _should_export_csv(self, request):
        """Check if the request is for CSV export"""
        return request.query_params.get("export") == "csv"

    def _get_csv_response(self, filename, data, headers=None):
        """Create a streaming CSV response"""
        if headers is None:
            # If headers is None, data should be a list of dicts
            # Extract headers from dict keys and format them
            # Create ordered dict to maintain consistent header and value order
            ordered_keys = list(data[0].keys())
            headers = [key.replace("_", " ").title() for key in ordered_keys]
            # Convert dict data to list format preserving order
            data = [[row[key] for key in ordered_keys] for row in data]

        pseudo_buffer = Echo()
        writer = csv.writer(pseudo_buffer)

        # Create a streaming response
        response = StreamingHttpResponse((writer.writerow(row) for row in [headers] + data), content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="{filename}"'
        return response

    def get_case_worker_role_filter_options(self, queryset):
        """
        Get filter options for case worker user roles based on the User model's ROLE_CHOICES.
        This provides filtering by role type rather than by specific individuals.
        """
        # Get unique user roles from the User model's ROLE_CHOICES
        organization = self.request.user.organizations.first()
        role_options = [
            {"value": tag.name, "label": tag.name, "id": tag.id}
            for tag in UserTag.objects.filter(organization=organization).distinct()
        ]
        return role_options

    @action(detail=False, methods=["get"])
    def case_list(self, request, *args, **kwargs):
        """Get case list with optimized performance"""

        def foramt_case_data(case, report_type):
            case_data = {
                "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details and case.incident_details.incident_date else 'N/A'} : {case.id}",
                "case_age": case.days.days if case.days else 0,
                "case_type": case.incident_details.incident_type
                if hasattr(case, "incident_details") and case.incident_details
                else None,
                "status": case.organization_status.name if case.organization_status else None,
                "primary": f"{case.v2_workers.primary_contact.first_name} {case.v2_workers.primary_contact.last_name}"
                if case.v2_workers and case.v2_workers.primary_contact
                else None,
                "statute": case.incident_details.statute_of_limitations.strftime("%m/%d/%Y")
                if hasattr(case, "incident_details")
                and case.incident_details
                and hasattr(case.incident_details, "statute_of_limitations")
                and case.incident_details.statute_of_limitations
                else None,
            }
            if report_type == "closed":
                # now closed at will have closed at value if present else dropped at
                if case.closed_at:
                    case_data["closed_at"] = case.closed_at.strftime("%m/%d/%Y")
                elif case.rejected_at:
                    case_data["closed_at"] = case.rejected_at.strftime("%m/%d/%Y")
                elif case.dropped_at:
                    case_data["closed_at"] = case.dropped_at.strftime("%m/%d/%Y")
                else:
                    case_data["closed_at"] = None

            if report_type == "rejected":
                case_data["rejected_at"] = case.rejected_at.strftime("%m/%d/%Y") if case.rejected_at else None
            if report_type == "dropped":
                if case.pending_drop_at:
                    case_data["dropped_at"] = case.pending_drop_at.strftime("%m/%d/%Y")
                else:
                    case_data["dropped_at"] = case.dropped_at.strftime("%m/%d/%Y") if case.dropped_at else None

            # Add team data efficiently
            if case.v2_workers:
                team = []
                for role in ["case_manager", "lead_attorney", "case_assistant"]:
                    member = getattr(case.v2_workers, role)
                    if member:
                        team.append({"id": member.id, "name": f"{member.first_name} {member.last_name}", "role": role})
                case_data["team"] = team
            else:
                case_data["team"] = []
            return case_data

        logger.info("Starting case_list endpoint")

        report_type = kwargs.get("case_list_type", "all")

        # Get base queryset
        queryset_start = time.time()
        queryset = self.get_queryset_for_case_list()
        logger.info(f"Time to get initial queryset: {time.time() - queryset_start:.3f}s")

        # Calculate summary statistics efficiently in a single query
        summary_start = time.time()

        # Get all case IDs for bulk queries
        case_ids = list(queryset.values_list("id", flat=True))

        # Get latest activity dates in bulk
        latest_notes = dict(
            CaseNote.objects.filter(case_id__in=case_ids)
            .values("case")
            .annotate(latest=Max("created_at"))
            .values_list("case", "latest")
        )

        latest_checklists = dict(
            CaseChecklist.objects.filter(case_id__in=case_ids, is_completed=True)
            .values("case")
            .annotate(latest=Max("completed_at"))
            .values_list("case", "latest")
        )

        latest_tasks = dict(
            Task.objects.filter(case_id__in=case_ids)
            .values("case")
            .annotate(latest=Max("updated_at"))
            .values_list("case", "latest")
        )

        info_logger.info(latest_notes, latest_tasks, "Notes task notes task ######XXXXXXXXX")

        # Calculate touched in last 7 days using the new activity logic
        seven_days_ago = timezone.now() - timedelta(days=7)
        touched_recently_count = 0

        for case_id in case_ids:
            # Get all activity dates for this case
            latest_note_date = latest_notes.get(case_id)
            latest_checklist_date = latest_checklists.get(case_id)
            latest_task_date = latest_tasks.get(case_id)

            # Find most recent activity
            dates = [d for d in [latest_note_date, latest_checklist_date, latest_task_date] if d is not None]
            info_logger.info(dates, "touched recently count current", touched_recently_count)
            if dates:
                last_activity = max(dates)
                if last_activity >= seven_days_ago:
                    touched_recently_count += 1

        # Get other basic stats
        stats = queryset.aggregate(
            total_cases=Count("id"),
            avg_days=Avg("days"),
            aged_cases=Count("id", filter=Q(created_at__lte=timezone.now() - timedelta(days=365))),
        )

        info_logger.info("last touched count", touched_recently_count)

        total_cases = stats["total_cases"]
        summary = {
            "total_cases": total_cases,
            "average_case_age": stats["avg_days"].days if stats["avg_days"] else 0,
        }
        summary["touched_last_7_days_percentage"] = round(
            (touched_recently_count / total_cases * 100) if total_cases else 0
        )
        summary["touched_recently_7_days_count"] = touched_recently_count
        summary["aged_over_1_year"] = round((stats["aged_cases"] / total_cases * 100) if total_cases else 0)

        logger.info(f"Time to calculate summary: {time.time() - summary_start:.3f}s")

        # Get filter options efficiently
        filter_start = time.time()
        filter_options = self.get_case_list_filter_options(self.get_base_queryset(), report_type)
        logger.info(f"Time to get filter options: {time.time() - filter_start:.3f}s")

        # Handle empty queryset case
        if not queryset.exists() and self.paginator:
            page = self.paginator.paginate_queryset([], request)
            return self.paginator.get_paginated_response(
                {"summary": summary, "results": [], "filter_options": filter_options}
            )

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        pagination_start = time.time()
        page = paginator.paginate_queryset(queryset, request)
        logger.info(f"Time to paginate queryset: {time.time() - pagination_start:.3f}s")

        if self._should_export_csv(request):
            results = [foramt_case_data(case, report_type) for case in queryset]
            return self._get_csv_response("case_list.csv", results)

        if page is not None:
            results = [foramt_case_data(case, report_type) for case in page]
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": results}
            )

    @action(detail=False, methods=["get"])
    def case_list_closed(self, request, *args, **kwargs):
        """Get case list with closed cases"""
        # Create a copy of the request object with the additional parameter
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = KPIType.case_closed
        # Create a new kwargs dict with case_list_type
        new_kwargs = kwargs.copy()
        new_kwargs["case_list_type"] = "closed"
        return self.case_list(request, *args, **new_kwargs)

    # case rejected
    @action(detail=False, methods=["get"])
    def case_list_rejected(self, request, *args, **kwargs):
        """Get case list with rejected cases"""
        # Create a copy of the request object with the additional parameter
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = KPIType.case_rejected
        # Create a new kwargs dict with case_list_type
        new_kwargs = kwargs.copy()
        new_kwargs["case_list_type"] = "rejected"
        return self.case_list(request, *args, **new_kwargs)

    # case dropped
    @action(detail=False, methods=["get"])
    def case_list_dropped(self, request, *args, **kwargs):
        """Get case list with dropped cases"""
        # Create a copy of the request object with the additional parameter
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = KPIType.case_dropped
        # Create a new kwargs dict with case_list_type
        new_kwargs = kwargs.copy()
        new_kwargs["case_list_type"] = "dropped"
        return self.case_list(request, *args, **new_kwargs)

    def case_workers_filters_options(self, queryset):
        workers = (
            queryset.values(
                "v2_workers__primary_contact_id",
                worker_name=Concat(
                    "v2_workers__primary_contact__first_name", Value(" "), "v2_workers__primary_contact__last_name"
                ),
            )
            .filter(v2_workers__primary_contact__isnull=False)
            .distinct()
            .order_by("worker_name")
        )

        lead_attorneys = (
            queryset.values(
                "v2_workers__lead_attorney_id",
                attorney_name=Concat(
                    "v2_workers__lead_attorney__first_name", Value(" "), "v2_workers__lead_attorney__last_name"
                ),
            )
            .filter(v2_workers__lead_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        # Supervising attorneys
        supervising_attorneys = (
            queryset.values(
                "v2_workers__supervising_attorney_id",
                attorney_name=Concat(
                    "v2_workers__supervising_attorney__first_name",
                    Value(" "),
                    "v2_workers__supervising_attorney__last_name",
                ),
            )
            .filter(v2_workers__supervising_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        lead_attorneys = (
            queryset.values(
                "v2_workers__lead_attorney_id",
                attorney_name=Concat(
                    "v2_workers__lead_attorney__first_name", Value(" "), "v2_workers__lead_attorney__last_name"
                ),
            )
            .filter(v2_workers__lead_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        case_managers = (
            queryset.values(
                "v2_workers__case_manager_id",
                manager_name=Concat(
                    "v2_workers__case_manager__first_name", Value(" "), "v2_workers__case_manager__last_name"
                ),
            )
            .filter(v2_workers__case_manager__isnull=False)
            .distinct()
            .order_by("manager_name")
        )

        return {
            "primary_workers": [
                {"id": worker["v2_workers__primary_contact_id"], "name": worker["worker_name"]} for worker in workers
            ],
            "workers": [
                {"id": worker["v2_workers__primary_contact_id"], "name": worker["worker_name"]} for worker in workers
            ],
            "lead_attorneys": [
                {"id": atty["v2_workers__lead_attorney_id"], "name": atty["attorney_name"]} for atty in lead_attorneys
            ],
            "supervising_attorneys": [
                {"id": atty["v2_workers__supervising_attorney_id"], "name": atty["attorney_name"]}
                for atty in supervising_attorneys
            ],
            "case_managers": [
                {"id": cm["v2_workers__case_manager_id"], "name": cm["manager_name"]} for cm in case_managers
            ],
        }

    def get_case_fix_status_filter_options(self, type=None, kpi_type=None):
        """Get distinct values for dropdown filters"""
        if type == "clients" and kpi_type == KPIType.intake_ready.value:
            return {
                "open/closed": [{"value": "open", "label": "Open"}, {"value": "closed", "label": "Closed"}],
            }
        return {
            "open/closed": [{"value": "open", "label": "Open"}, {"value": "closed", "label": "Closed"}],
        }

    def get_case_list_filter_options(self, queryset, report_type="all"):
        """Get distinct values for dropdown filters"""
        logger.info("Starting get_case_list_filter_options")
        # Workers

        # Case types
        types_start = time.time()
        case_types = (
            queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )
        logger.info(f"Time to get case types: {time.time() - types_start:.3f}s")

        case_age_options = [
            {"value": "30", "label": "30 days"},
            {"value": "60", "label": "60 days"},
            {"value": "90", "label": "90 days"},
            {"value": "180", "label": "6 months"},
            {"value": "365", "label": "1 year"},
        ]

        # Date field options for date range filters
        date_field_options = [
            {"value": "doi", "label": "Date of Incident"},
            {"value": "created_at", "label": "Created Date"},
            {"value": "updated_at", "label": "Updated Date"},
            {"value": "last_touched", "label": "Last Touched Date"},
            {"value": "statute", "label": "Statute Date"},
        ]
        if report_type == "closed" or report_type == "rejected" or report_type == "dropped":
            date_field_options = [{"value": "closed_at", "label": "Closed Date"}]
        if report_type == "rejected":
            date_field_options = [{"value": "rejected_at", "label": "Rejected Date"}]
        if report_type == "dropped":
            date_field_options = [{"value": "dropped_at", "label": "Dropped Date"}]

        result = {
            "case_types": [{"value": ct, "label": ct} for ct in case_types],
            **self.get_case_fix_status_filter_options(),
            "organization_statuses": self.get_case_status_categories(queryset),
            "case_ages": case_age_options,
            "date_fields": date_field_options,
            **self.case_workers_filters_options(queryset),
            "role_options": self.get_case_worker_role_filter_options(queryset),
            "open_cases_checkbox": [{"value": "true", "label": "Open Cases"}, {"value": "false", "label": "All Cases"}],
        }

        return result

    @action(detail=False, methods=["get"])
    def summary(self, request):
        """Get just the summary statistics"""
        queryset = self.get_base_queryset()
        queryset = self.apply_filter_on_case_queryset(queryset)
        filter_options = self.get_case_list_filter_options(self.get_case_list_filter_options(self.get_base_queryset()))

        total_cases = queryset.count()

        return Response(
            {
                "total_cases": total_cases,
                "average_case_age": self.get_average_case_age(queryset),
                "touched_last_7_days": (
                    queryset.filter(last_touched__gte=timezone.now() - timedelta(days=7)).count() / total_cases * 100
                )
                if total_cases
                else 0,
                "aged_over_1_year": (
                    queryset.filter(created_at__lte=timezone.now() - timedelta(days=365)).count() / total_cases * 100
                )
                if total_cases
                else 0,
                "filter_options": filter_options,
            }
        )

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get detailed case statistics for a date range with pagination and filters"""
        # Get base queryset with view type handling
        queryset = self.get_base_queryset()
        queryset = self.apply_filter_on_case_queryset(queryset)
        queryset = self.apply_sorting_on_case_queryset(queryset)

        summary = get_case_statistics(queryset)

        # Get filter options
        filter_options = self.get_statistics_filter_options(queryset=queryset)

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Prepare CSV data
            headers = [key.replace("_", " ").title() for key in summary.keys()]
            csv_data = [summary[key] for key in headers]
            return self._get_csv_response("case_statistics.csv", csv_data, headers)

        return Response({"summary": summary, "filter_options": filter_options})

    def get_statistics_filter_options(self, queryset):
        return {
            "organization_statuses": self.get_case_status_categories(queryset),
            "case_ages": [
                {"value": "30", "label": "30 days"},
                {"value": "60", "label": "60 days"},
                {"value": "90", "label": "90 days"},
                {"value": "180", "label": "6 months"},
                {"value": "365", "label": "1 year"},
            ],
            "role_options": self.get_case_worker_role_filter_options(queryset),
            **self.case_workers_filters_options(queryset),
            "date_fields": [
                {"value": "created_at", "label": "Created Date"},
                {"value": "updated_at", "label": "Updated Date"},
                {"value": "doi", "label": "Date of Incident"},
            ],
        }

    @action(detail=False, methods=["get"])
    def case_status(self, request):
        """Get case status statistics including counts and average days in each status"""
        # Get base queryset
        queryset = self.get_base_queryset()
        queryset = self.apply_filter_on_case_queryset(queryset)
        organization = request.user.organizations.first()

        # Get current time for calculations
        now = timezone.now()

        # First, get all cases with their current status
        current_cases = (
            queryset.values(
                "organization_status__name",
            )
            .annotate(
                current_case_count=Count("id", distinct=True),  # Count of cases currently in this status
            )
            .order_by("-current_case_count")
        )

        # For each status, calculate average duration from CaseStatusHistory
        status_stats = []
        for status_group in current_cases:
            status_name = status_group["organization_status__name"]
            if not status_name:  # Skip if no status
                continue

            # Get all cases that have been in this status
            cases_in_status = queryset.filter(
                Q(organization_status__name=status_name)  # Current status
                | Q(status_history__previous_status__name=status_name)  # Previous status
            ).distinct()

            total_case_durations = timedelta(0)
            cases_with_duration = 0

            # Calculate duration for each case
            for case in cases_in_status:
                case_duration = timedelta(0)

                # Get all status history entries for this case and status
                status_history = CaseStatusHistory.objects.filter(
                    case=case, previous_status__name=status_name
                ).order_by("changed_at")

                # Calculate duration from completed periods
                for history in status_history:
                    if history.time_in_previous_status:
                        case_duration += history.time_in_previous_status

                # If case is currently in this status, calculate current period
                if case.organization_status and case.organization_status.name == status_name:
                    # Get the most recent status change to this status
                    latest_change = (
                        CaseStatusHistory.objects.filter(
                            case=case, new_status__name=status_name, changed_at__isnull=False
                        )
                        .order_by("-changed_at")
                        .first()
                    )

                    if latest_change:
                        current_duration = now - latest_change.changed_at
                        case_duration += current_duration
                    else:
                        # If no status history exists, use case creation date
                        current_duration = now - case.created_at
                        case_duration += current_duration

                # Only count cases that have spent time in this status
                if case_duration.total_seconds() > 0:
                    total_case_durations += case_duration
                    cases_with_duration += 1

            # Calculate average duration across all cases
            if cases_with_duration > 0:
                avg_duration = total_case_durations / cases_with_duration
            else:
                avg_duration = timedelta(0)

            status_stats.append(
                {
                    "case_status": status_name,
                    "case_count": status_group["current_case_count"],  # Use current case count from Case model
                    "average_days": avg_duration.days,
                    "_debug": {
                        "total_cases_with_duration": cases_with_duration,
                        "total_duration_days": total_case_durations.days,
                        "current_cases": status_group["current_case_count"],
                    },
                }
            )

        # Sort by case count
        status_stats = sorted(status_stats, key=lambda x: x["case_count"], reverse=True)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", 10))
        page = int(request.query_params.get("page", 1))
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Prepare CSV data
            headers = ["Case Status", "Case Count", "Average Days"]
            csv_data = [
                [status["case_status"], status["case_count"], status["average_days"]] for status in status_stats
            ]
            return self._get_csv_response("case_status.csv", csv_data, headers)

        # Calculate total results for pagination
        total_results = len(status_stats)
        total_pages = (total_results + page_size - 1) // page_size

        # Paginate results
        paginated_results = status_stats[start_idx:end_idx]

        # Find longest and shortest average status
        if status_stats:
            longest_status = max(status_stats, key=lambda x: x["average_days"])
            shortest_status = min(status_stats, key=lambda x: x["average_days"])
        else:
            longest_status = shortest_status = None

        # Get filter options
        filter_options = self.get_case_status_filter_options(organization)

        return Response(
            {
                "summary": {
                    "longest_average_status": {
                        "status": longest_status["case_status"] if longest_status else None,
                        "days": longest_status["average_days"] if longest_status else 0,
                    },
                    "shortest_average_status": {
                        "status": shortest_status["case_status"] if shortest_status else None,
                        "days": shortest_status["average_days"] if shortest_status else 0,
                    },
                },
                "pagination": {
                    "total_results": total_results,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": page_size,
                    "has_next": page < total_pages,
                    "has_previous": page > 1,
                },
                "filter_options": filter_options,
                "status_details": paginated_results,
            }
        )

    def get_case_status_filter_options(self, organization):
        """Get filter options for case status report"""
        base_queryset = self.get_base_queryset()
        case_types = (
            base_queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )

        # Get source types and details
        source_types = (
            base_queryset.values_list("client_basic_details__source_type", flat=True)
            .filter(client_basic_details__source_type__isnull=False)
            .distinct()
            .order_by("client_basic_details__source_type")
        )

        source_details = (
            base_queryset.values_list("client_basic_details__source_detail", flat=True)
            .filter(client_basic_details__source_detail__isnull=False)
            .distinct()
            .order_by("client_basic_details__source_detail")
        )

        date_fields = [
            {"value": "doi", "label": "Date of Incident"},
            {"value": "created_at", "label": "Created Date"},
            {"value": "updated_at", "label": "Updated Date"},
            {"value": "last_touched", "label": "Last Touched Date"},
            {"value": "statute", "label": "Statute Date"},
        ]

        return {
            **self.get_case_fix_status_filter_options(),
            **self.case_workers_filters_options(base_queryset),
            "role_options": self.get_case_worker_role_filter_options(base_queryset),
            "case_types": [{"value": ct, "label": ct} for ct in case_types],
            "source_types": [{"value": st, "label": st} for st in source_types],
            "source_details": [{"value": sd, "label": sd} for sd in source_details],
            "date_fields": date_fields,
        }

    @action(detail=False, methods=["get"])
    def check_deposits(self, request):
        """Get payment statistics and details for all payment types"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        # Get base queryset for case costs using filtered cases
        queryset = CaseCost.objects.filter(case__in=case_queryset, is_void=False)

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(check_number__icontains=search_query)
                | Q(case__client_basic_details__first_name__icontains=search_query)
                | Q(case__client_basic_details__last_name__icontains=search_query)
                | Q(case__id__icontains=search_query)
                | Q(contact__company_name__icontains=search_query)
                | Q(case__v2_workers__lead_attorney__first_name__icontains=search_query)
                | Q(case__v2_workers__lead_attorney__last_name__icontains=search_query)
                | Q(case__v2_workers__case_manager__first_name__icontains=search_query)
                | Q(case__v2_workers__case_manager__last_name__icontains=search_query)
                | Q(memo__icontains=search_query)  # Check type/memo
            ).distinct()

        # Apply other filters
        # Date Range filters
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        date_field = request.query_params.get("date_field")
        if from_date and to_date:
            if date_field == "paid_date":
                queryset = queryset.filter(paid_date__range=[from_date, to_date])
            elif date_field == "requested_date":
                queryset = queryset.filter(requested_date__range=[from_date, to_date])

        payment_type = request.query_params.get("payment_type")
        if payment_type:
            queryset = queryset.filter(payment_type=payment_type)

        # Case Manager filter
        case_manager = request.query_params.get("case_manager")
        if case_manager:
            queryset = queryset.filter(case__v2_workers__case_manager_id=case_manager)

        # Lead Attorney filter
        lead_attorney = request.query_params.get("lead_attorney")
        if lead_attorney:
            queryset = queryset.filter(case__v2_workers__lead_attorney_id=lead_attorney)

        # Check Type filter
        check_type = request.query_params.get("check_type")
        if check_type:
            queryset = queryset.filter(memo=check_type)

        # Check Number filter
        check_number = request.query_params.get("check_number")
        if check_number:
            queryset = queryset.filter(check_number=check_number)

        # Get summary statistics
        total_checks = queryset.count()
        total_amount = float(
            queryset.aggregate(
                total=Coalesce(
                    Sum("amount", output_field=models.DecimalField(max_digits=12, decimal_places=2)),
                    0,
                    output_field=models.DecimalField(max_digits=12, decimal_places=2),
                )
            )["total"]
        )

        summary = {
            "checks": total_checks,
            "total": total_amount,
        }

        # Get detailed check information
        checks = queryset.select_related(
            "case",
            "contact",
            "case__v2_workers",
            "case__v2_workers__lead_attorney",
            "case__v2_workers__case_manager",
            "case__organization_status",  # Add organization status
        ).order_by("-paid_date")

        # Define the payment data formatting function
        def format_payment_data(payment):
            return {
                "case_id": payment.case.id,
                "case": f"{payment.case.client_basic_details.first_name} {payment.case.client_basic_details.last_name} - {payment.case.incident_details.incident_date.strftime('%m/%d/%Y')} - {payment.case.id}",
                "organization_status": payment.case.organization_status.display_name
                if payment.case.organization_status
                else None,
                "deposited": payment.paid_date.strftime("%m/%d/%Y") if payment.paid_date else None,
                "check_number": payment.check_number,
                "check_type": payment.memo,
                "issuer_payee": payment.contact.payee if payment.contact else None,
                "lead_attorney": (
                    payment.case.v2_workers.lead_attorney.get_full_name()
                    if payment.case.v2_workers and payment.case.v2_workers.lead_attorney
                    else None
                ),
                "case_manager": (
                    payment.case.v2_workers.case_manager.get_full_name()
                    if payment.case.v2_workers and payment.case.v2_workers.case_manager
                    else None
                ),
                "amount": float(payment.amount),
                "status": payment.status,
                "requested_date": payment.requested_date.strftime("%m/%d/%Y") if payment.requested_date else None,
            }

        # Get filter options
        filter_options = self.get_check_deposits_filter_options(queryset)

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all payments for CSV export
            all_payment_data = [format_payment_data(payment) for payment in checks]

            # Prepare CSV data
            headers = [
                "Case ID",
                "Case",
                "Organization Status",
                "Deposited",
                "Check Number",
                "Check Type",
                "Issuer/Payee",
                "Lead Attorney",
                "Case Manager",
                "Amount",
                "Status",
                "Requested Date",
            ]

            csv_data = []
            for payment in all_payment_data:
                csv_data.append(
                    [
                        payment.get("case_id", ""),
                        payment.get("case", ""),
                        payment.get("organization_status", ""),
                        payment.get("deposited", ""),
                        payment.get("check_number", ""),
                        payment.get("check_type", ""),
                        payment.get("issuer_payee", ""),
                        payment.get("lead_attorney", ""),
                        payment.get("case_manager", ""),
                        payment.get("amount", ""),
                        payment.get("status", ""),
                        payment.get("requested_date", ""),
                    ]
                )

            return self._get_csv_response("check_deposits.csv", csv_data, headers)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(checks, request)

        if page is not None:
            payment_data = [format_payment_data(payment) for payment in page]
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": payment_data}
            )

        payment_data = [format_payment_data(payment) for payment in checks]
        return Response({"summary": summary, "filter_options": filter_options, "results": payment_data})

    def get_check_deposits_filter_options(self, queryset):
        """Get filter options for check deposits report"""
        # Get case managers
        case_managers = (
            queryset.values(
                "case__v2_workers__case_manager_id",
                manager_name=Concat(
                    "case__v2_workers__case_manager__first_name",
                    Value(" "),
                    "case__v2_workers__case_manager__last_name",
                ),
            )
            .filter(case__v2_workers__case_manager__isnull=False)
            .distinct()
            .order_by("manager_name")
        )

        # Get lead attorneys
        lead_attorneys = (
            queryset.values(
                "case__v2_workers__lead_attorney_id",
                attorney_name=Concat(
                    "case__v2_workers__lead_attorney__first_name",
                    Value(" "),
                    "case__v2_workers__lead_attorney__last_name",
                ),
            )
            .filter(case__v2_workers__lead_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        # Get check types
        check_types = queryset.values_list("memo", flat=True).filter(memo__isnull=False).distinct().order_by("memo")
        payment_types = CaseCost.PAYMENT_TYPE_CHOICES

        return {
            "case_managers": [
                {"id": cm["case__v2_workers__case_manager_id"], "name": cm["manager_name"]} for cm in case_managers
            ],
            "lead_attorneys": [
                {"id": la["case__v2_workers__lead_attorney_id"], "name": la["attorney_name"]} for la in lead_attorneys
            ],
            "check_types": [{"value": ct, "label": ct} for ct in check_types],
            "date_fields": [
                {"value": "paid_date", "label": "Deposited Date"},
                {"value": "requested_date", "label": "Requested Date"},
            ],
            "payment_types": [{"value": pt, "label": pt} for pt in payment_types],
        }

    @action(detail=False, methods=["get"])
    def clients(self, request, custom_date_filters_for_retained_cases=False):
        """Get client statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()

        is_intake_ready = request.query_params.get("kpi_type") == KPIType.intake_ready.value
        is_retained = request.query_params.get("kpi_type") == KPIType.client_retained.value

        # For retained cases view with retained_date filter, we DON'T want to skip dates
        # Only skip dates for intake ready cases or when not handling specific date fields
        skip_dates = (not is_intake_ready) and not (custom_date_filters_for_retained_cases and is_retained)

        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=skip_dates)

        # Only apply DOB filtering here if it's not a retained case view
        if not is_intake_ready and not custom_date_filters_for_retained_cases:
            from_date = request.query_params.get("from_date")
            to_date = request.query_params.get("to_date")
            date_field = request.query_params.get("date_field")

            # Only apply DOB filtering here if explicitly selected
            if from_date and to_date and date_field == "dob":
                case_queryset = case_queryset.filter(client_basic_details__date_of_birth__range=[from_date, to_date])
        if is_intake_ready:
            date_field = request.query_params.get("date_field")
            if date_field == "intake_date":
                from_date = request.query_params.get("from_date")
                to_date = request.query_params.get("to_date")
                if from_date and to_date:
                    case_queryset = case_queryset.filter(intake_date__range=[from_date, to_date])
                elif from_date:
                    case_queryset = case_queryset.filter(intake_date__gte=from_date)
                elif to_date:
                    case_queryset = case_queryset.filter(intake_date__lte=to_date)

        # Calculate summary statistics
        total_clients = case_queryset.count()
        current_month = timezone.now().month
        birthdays_this_month = case_queryset.filter(client_basic_details__date_of_birth__month=current_month).count()

        summary = {
            "total_clients": total_clients,
            "birthdays_this_month": birthdays_this_month,
        }

        # Get client details with related data
        clients = case_queryset.select_related(
            "client_basic_details",
            "client_contact_details",
            "incident_details",
            "organization_status",
            "v2_workers",
            "v2_workers__case_manager",
            "v2_workers__lead_attorney",
        ).order_by("client_basic_details__last_name", "client_basic_details__first_name")

        def format_client_data(case):
            basic_details = case.client_basic_details if hasattr(case, "client_basic_details") else None
            contact_details = case.client_contact_details if hasattr(case, "client_contact_details") else None

            # Calculate age
            age = None
            if basic_details and basic_details.date_of_birth:
                today = timezone.now().date()
                age = (
                    today.year
                    - basic_details.date_of_birth.year
                    - ((today.month, today.day) < (basic_details.date_of_birth.month, basic_details.date_of_birth.day))
                )

            data = {
                "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}",
                "phone": contact_details.phone_number_1 if contact_details else None,
                "gender": basic_details.gender if basic_details else None,
                "address": f"{', '.join(filter(None, [contact_details.address1_street1, contact_details.address1_street2, contact_details.address1_city, contact_details.address1_state, contact_details.address1_zip]))}",
                "city": contact_details.address1_city if contact_details else None,
                "state": contact_details.address1_state if contact_details else None,
                "zip": contact_details.address1_zip if contact_details else None,
                "lastname": basic_details.last_name if basic_details else None,
                "firstname": basic_details.first_name if basic_details else None,
                "dob": basic_details.date_of_birth.strftime("%m/%d/%Y")
                if basic_details and basic_details.date_of_birth
                else None,
                "age": age,
                "intake_date": case.intake_date.strftime("%m/%d/%Y") if case.intake_date else None,
                "retained_date": case.retained_at.strftime("%m/%d/%Y") if case.retained_at else None,
                "closed_date": case.closed_at.strftime("%m/%d/%Y") if case.closed_at else None,
                "rejected_date": case.rejected_at.strftime("%m/%d/%Y") if case.rejected_at else None,
                "case_type": getattr(case.incident_details, "incident_type", None),
                "organization_status": case.organization_status.display_name if case.organization_status else None,
                "email": contact_details.primary_email if contact_details else None,
                "language": basic_details.language if basic_details else None,
                "source_type": basic_details.source_type if basic_details else None,
                "source_detail": basic_details.source_detail if basic_details else None,
                "is_deceased": basic_details.deceased if basic_details else False,
                "has_email": bool(contact_details and contact_details.primary_email),
                "team": self._get_team_data(case.v2_workers) if case.v2_workers else [],
            }

            # if KPIType.client_retained.value == request.query_params.get("kpi_type"):
            #     del data["intake_date"], data["closed_date"], data["rejected_date"]
            # if KPIType.intake_ready.value == request.query_params.get("kpi_type"):
            #     del data["retained_date"], data["closed_date"], data["rejected_date"]
            # if KPIType.case_closed.value == request.query_params.get("kpi_type"):
            #     del data["retained_date"], data["rejected_date"]
            # if KPIType.case_rejected.value == request.query_params.get("kpi_type"):
            #     del data["retained_date"], data["closed_date"]
            # if KPIType.referred_in.value == request.query_params.get("kpi_type"):
            #     del data["retained_date"], data["closed_date"], data["rejected_date"]
            data = {
                k: v
                for k, v in data.items()
                if k
                not in [
                    "firstname",
                    "lastname",
                    "gender",
                    "is_deceased",
                    "language",
                    "zip",
                    "address",
                    "city",
                    "phone",
                    "email",
                    "dob",
                    "source_type",
                ]
            }
            return data

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        if not self._should_export_csv(request):
            page = paginator.paginate_queryset(clients, request)
        else:
            page = clients

        # Get filter options
        filter_options = self.get_clients_filter_options(
            self.get_base_queryset(), custom_date_filters_for_retained_cases, request.query_params.get("kpi_type", None)
        )
        if request.query_params.get("kpi_type") == KPIType.intake_ready.value:
            filter_options["date_fields"] = [{"value": "intake_date", "label": "Intake Date"}]
        client_data = [format_client_data(case) for case in page]

        if self._should_export_csv(request):
            return self._get_csv_response("clients.csv", client_data)

        if page is not None:
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": client_data}
            )

    # clsoed cases for clients
    @action(detail=False, methods=["get"])
    def closed_cases_for_clients(self, request):
        """Get closed cases for clients"""
        kpi_type = KPIType.case_closed
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request)

    # rejected cases for clients
    @action(detail=False, methods=["get"])
    def rejected_cases_for_clients(self, request):
        """Get rejected cases for clients"""
        kpi_type = KPIType.case_rejected
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request)

    # open cases for clients
    @action(detail=False, methods=["get"])
    def open_cases_for_clients(self, request):
        """Get open cases for clients"""
        kpi_type = KPIType.case_open
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request)

    @action(detail=False, methods=["get"])
    def referred_in_cases_for_clients(self, request):
        """Get referred in cases for clients"""
        kpi_type = KPIType.referred_in
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request)

    @action(detail=False, methods=["get"])
    def subbed_out_cases_for_clients(self, request):
        """Get subbed out cases for clients"""
        kpi_type = KPIType.subbed_out
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request)

    @action(detail=False, methods=["get"])
    def intake_ready_cases_for_clients(self, request):
        """Get intake ready cases for clients"""
        kpi_type = KPIType.intake_ready
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request)

    @action(detail=False, methods=["get"])
    def client_retained_cases_for_clients(self, request):
        """Get client retained cases for clients"""
        kpi_type = KPIType.client_retained
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = kpi_type
        return self.clients(request, custom_date_filters_for_retained_cases=True)

    def get_clients_filter_options(self, queryset, custom_date_filters_for_retained_cases=False, kpi_type=None):
        """Get filter options for clients report"""
        organization = self.request.user.organizations.first()

        # Get case types
        case_types = (
            queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )

        # Get organization statuses
        org_statuses = organization.case_statuses.filter(is_active=True).order_by("order")

        # Get source types
        source_types = (
            queryset.values_list("client_basic_details__source_type", flat=True)
            .filter(client_basic_details__source_type__isnull=False)
            .distinct()
            .order_by("client_basic_details__source_type")
        )

        # Get source details
        source_details = (
            queryset.values_list("client_basic_details__source_detail", flat=True)
            .filter(client_basic_details__source_detail__isnull=False)
            .distinct()
            .order_by("client_basic_details__source_detail")
        )
        # Get languages
        languages = (
            queryset.values_list("client_basic_details__language", flat=True)
            .filter(client_basic_details__language__isnull=False)
            .distinct()
            .order_by("client_basic_details__language")
        )

        if custom_date_filters_for_retained_cases:
            date_field_options = [{"value": "retained_date", "label": "Retained Date"}]
        else:
            date_field_options = [
                {"value": "dob", "label": "Date of Birth"},
                {"value": "intake_date", "label": "Intake Date"},
            ]

        if not kpi_type:
            date_field_options = []

        filters_final = {
            "case_types": [{"value": ct, "label": ct} for ct in case_types],
            "organization_statuses": [
                {"id": status.id, "value": status.name, "label": status.display_name} for status in org_statuses
            ],
            "source_types": [{"value": st, "label": st} for st in source_types],
            "source_details": [{"value": sd, "label": sd} for sd in source_details],
            "languages": [{"value": lang, "label": lang} for lang in languages],
            "birthday_months": [{"value": str(i), "label": calendar.month_name[i]} for i in range(1, 13)],
            "has_email": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}],
            "exclude_minors": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}],
            "exclude_deceased": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}],
            **self.case_workers_filters_options(queryset),
            "role_options": self.get_case_worker_role_filter_options(queryset),
            "date_fields": date_field_options,
            **self.get_case_fix_status_filter_options(type="clients", kpi_type=kpi_type),
        }

        if not kpi_type:
            filters_final["minor_client"] = [
                {"value": "true", "label": "Yes"},
                {"value": "false", "label": "No"},
            ]

        if not kpi_type or kpi_type == KPIType.intake_ready.value:
            filters_final["has_email"] = [
                {"value": "true", "label": "Yes"},
                {"value": "false", "label": "No"},
            ]

        return filters_final

    @action(detail=False, methods=["get"])
    def defendant_types(self, request):
        """Get defendant type statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        logger.info(f"Base case queryset count: {case_queryset.count()}")

        case_queryset = self.apply_filter_on_case_queryset(case_queryset)
        logger.info(f"After filters case queryset count: {case_queryset.count()}")
        logger.info(f"Case age filter value: {request.query_params.get('case_age')}")

        # Base defendant queryset
        queryset = CaseDefendant.objects.filter(case__in=case_queryset)
        logger.info(f"Defendant queryset count: {queryset.count()}")

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query)
                | Q(case__id__icontains=search_query)
                | Q(case__client_basic_details__first_name__icontains=search_query)
                | Q(case__client_basic_details__last_name__icontains=search_query)
                | Q(insurance_company__name__icontains=search_query)
            ).distinct()

        # Defendant Type filter
        defendant_type = request.query_params.get("defendant_type")
        if defendant_type:
            queryset = queryset.filter(defendant_type=defendant_type)

        # Calculate summary statistics
        defendant_stats = queryset.values("defendant_type").annotate(count=Count("id")).order_by("defendant_type")

        summary = {
            "public_entity": next(
                (item["count"] for item in defendant_stats if item["defendant_type"] == "PUBLIC_ENTITY"), 0
            ),
            "fed_gov_entity": next(
                (item["count"] for item in defendant_stats if item["defendant_type"] == "FED_GOV_ENTITY"), 0
            ),
            "commercial": next(
                (item["count"] for item in defendant_stats if item["defendant_type"] == "COMMERCIAL"), 0
            ),
            "private_party": next(
                (item["count"] for item in defendant_stats if item["defendant_type"] == "PRIVATE_PARTY"), 0
            ),
        }

        # Get defendant details with related data
        defendants = (
            queryset.select_related(
                "case",
                "case__incident_details",
                "case__organization_status",
                "case__v2_workers",
                "case__v2_workers__lead_attorney",
                "case__v2_workers__case_manager",
            )
            .prefetch_related(
                "insurances__insurance_company"  # Add prefetch for defendant insurances
            )
            .order_by("-case__created_at")
        )

        def format_defendant_data(defendant):
            # Get primary insurance if any
            primary_insurance = defendant.insurances.first()
            insurance_name = (
                primary_insurance.insurance_company.name
                if primary_insurance and primary_insurance.insurance_company
                else None
            )

            medical_bills = (
                Case.objects.filter(id=defendant.case.id)
                .annotate(
                    total_bills=Coalesce(
                        Sum("treatment_providers__original_bill"),
                        0,
                        output_field=models.DecimalField(max_digits=12, decimal_places=2),
                    )
                )
                .values_list("total_bills", flat=True)
                .first()
            )

            insurance_limits = primary_insurance.policy_limits if primary_insurance else None
            estimated_value = (
                defendant.case.incident_details.estimated_value if hasattr(defendant.case, "incident_details") else None
            )
            team = self._get_team_data(defendant.case.v2_workers) if defendant.case.v2_workers else []
            case = defendant.case
            last_touched = case.updated_at

            days = (timezone.now() - case.created_at).days if case.created_at else 0

            return {
                "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}",
                "defendant": f"{defendant.first_name} {defendant.last_name}",
                "company_entity": defendant.company_entity,
                "defendant_type": defendant.defendant_type,
                "insurance_limits": insurance_limits,
                "estimated_value": estimated_value,
                "medical_bills": medical_bills,
                "days": days,
                "doi": defendant.case.incident_details.incident_date.strftime("%m/%d/%Y"),
                "case_type": getattr(defendant.case.incident_details, "incident_type", None),
                "case_status": defendant.case.organization_status.display_name
                if defendant.case.organization_status
                else None,
                "statute": defendant.case.incident_details.statute_of_limitations.strftime("%m/%d/%Y")
                if defendant.case.incident_details and defendant.case.incident_details.statute_of_limitations
                else None,
                "insurance_1": insurance_name,
                "team": team,
                "last_touched": last_touched,
            }

        # Get filter options
        filter_options = self.get_defendant_types_filter_options(queryset)

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all defendants for CSV export
            all_defendant_data = [format_defendant_data(defendant) for defendant in defendants]
            return self._get_csv_response("defendant_types.csv", all_defendant_data)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(defendants, request)

        if page is not None:
            defendant_data = [format_defendant_data(defendant) for defendant in page]
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": defendant_data}
            )

        defendant_data = [format_defendant_data(defendant) for defendant in defendants]
        return Response({"summary": summary, "filter_options": filter_options, "results": defendant_data})

    def get_defendant_types_filter_options(self, queryset):
        """Get filter options for defendant types report"""
        base_queryset = self.get_base_queryset()
        case_types = (
            base_queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )

        case_age_options = [
            {"value": "30", "label": "30 days"},
            {"value": "60", "label": "60 days"},
            {"value": "90", "label": "90 days"},
            {"value": "180", "label": "6 months"},
            {"value": "365", "label": "1 year"},
        ]

        # Date field options for date range filters
        date_field_options = [
            {"value": "doi", "label": "Date of Incident"},
            {"value": "created_at", "label": "Created Date"},
            {"value": "updated_at", "label": "Updated Date"},
            {"value": "last_touched", "label": "Last Touched Date"},
            {"value": "statute", "label": "Statute Date"},
        ]

        # Get distinct defendant types from database
        defendant_types = (
            queryset.values_list("defendant_type", flat=True)
            .filter(defendant_type__isnull=False)
            .distinct()
            .order_by("defendant_type")
        )

        # Get source types and details
        source_types = (
            base_queryset.values_list("client_basic_details__source_type", flat=True)
            .filter(client_basic_details__source_type__isnull=False)
            .distinct()
            .order_by("client_basic_details__source_type")
        )

        source_details = (
            base_queryset.values_list("client_basic_details__source_detail", flat=True)
            .filter(client_basic_details__source_detail__isnull=False)
            .distinct()
            .order_by("client_basic_details__source_detail")
        )

        return {
            **self.case_workers_filters_options(base_queryset),
            "case_types": [{"value": ct, "label": ct} for ct in case_types],
            "source_types": [{"value": st, "label": st} for st in source_types],
            "source_details": [{"value": sd, "label": sd} for sd in source_details],
            "defendant_types": [{"value": dt, "label": dt} for dt in defendant_types],
            "case_ages": case_age_options,
            "date_fields": date_field_options,
            "organization_statuses": self.get_case_status_categories(self.get_base_queryset()),
        }

    @action(detail=False, methods=["get"])
    def health_providers(self, request):
        """Get health provider statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()

        # Get medical providers for the organization
        queryset = MedicalProvider.objects.filter(organization=request.user.organizations.first()).select_related(
            "organization"
        )

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(company__icontains=search_query)
                | Q(specialties__icontains=search_query)
                | Q(contacts__name__icontains=search_query)
                | Q(contacts__email__icontains=search_query)
                | Q(contacts__phone__icontains=search_query)
            ).distinct()

        # Apply filters
        # Specialty filter
        specialty = request.query_params.get("specialty")
        if specialty:
            # Use contains lookup with proper handling of comma-separated values
            # This will match the specialty even if it's part of a comma-separated list
            queryset = queryset.filter(
                Q(specialties__icontains=f"{specialty},")  # Matches at the start or middle
                | Q(specialties__icontains=f", {specialty}")  # Matches in the middle
                | Q(specialties__iexact=specialty)  # Exact match for single specialty
            )

        # Add city filter
        city = request.query_params.get("city")
        if city:
            queryset = queryset.filter(contacts__city__iexact=city).distinct()

        # Status filters
        if request.query_params.get("accepts_liens") == "true":
            queryset = queryset.filter(accepts_liens=True)
        if request.query_params.get("no_records_service") == "true":
            queryset = queryset.filter(no_records_service=True)
        if request.query_params.get("do_not_use") == "true":
            queryset = queryset.filter(do_not_use=True)

        # Calculate summary statistics
        summary = {
            "total_providers": queryset.count(),
            "accepts_liens": queryset.filter(accepts_liens=True).count(),
            "no_records_service": queryset.filter(no_records_service=True).count(),
            "do_not_use": queryset.filter(do_not_use=True).count(),
        }

        # Get specialty breakdown
        specialty_stats = (
            queryset.values("specialties")
            .annotate(count=Count("id"))
            .filter(specialties__isnull=False)
            .order_by("-count")
        )

        summary["by_specialty"] = {}

        # Get detailed provider information with optimized queries
        providers = queryset.prefetch_related(
            "contacts",
            "treatments",
            "treatments__case",
            "treatments__case__organization_status",
        ).order_by("company")

        def format_provider_data(provider):
            # Get office contact
            office_contact = next(
                (contact for contact in provider.contacts.all() if contact.contact_type == "OFFICE"), None
            )

            # Get treatments for this provider's cases
            treatments = provider.treatments.filter(case__in=case_queryset)

            # Calculate case statistics
            open_cases = (
                treatments.filter(case__status__in=["ACTIVE", "PENDING", "INTAKE"]).values("case").distinct().count()
            )

            total_cases = treatments.values("case").distinct().count()

            # Calculate billing statistics
            billing_stats = treatments.aggregate(
                original_bill=Coalesce(
                    Sum("original_bill", output_field=models.DecimalField(max_digits=12, decimal_places=2)),
                    0,
                    output_field=models.DecimalField(max_digits=12, decimal_places=2),
                ),
                adjusted_bill=Coalesce(
                    Sum("adjusted_bill", output_field=models.DecimalField(max_digits=12, decimal_places=2)),
                    0,
                    output_field=models.DecimalField(max_digits=12, decimal_places=2),
                ),
            )

            original_bill = float(billing_stats["original_bill"])
            adjusted_bill = float(billing_stats["adjusted_bill"])

            # Calculate reduction percentage
            reduction_pct = (
                round(((original_bill - adjusted_bill) / original_bill) * 100, 1) if original_bill > 0 else 0
            )

            # Split specialties into a list for better display
            specialties = []
            if provider.specialties:
                specialties = [s.strip() for s in provider.specialties.split(",")]

            return {
                "office_name": provider.company,
                "specialties": ", ".join(specialties),  # Now returns a list of specialties
                "city": office_contact.city if office_contact else None,
                "office_phone": office_contact.phone if office_contact else None,
                "open_cases": open_cases,
                "total_cases": total_cases,
                "original_amount": original_bill,
                "final_amount": adjusted_bill,
                "avg_reduction": reduction_pct,
                "accepts_liens": provider.accepts_liens,
                "no_records_service": provider.no_records_service,
                "do_not_use": provider.do_not_use,
                "contacts": [
                    {
                        "type": contact.get_contact_type_display(),
                        "name": contact.name,
                        "phone": contact.phone,
                        "email": contact.email,
                        "address": {
                            "city": contact.city,
                            "state": contact.state,
                            "zip_code": contact.zip_code,
                        },
                    }
                    for contact in provider.contacts.all()
                ],
            }

        # Get filter options
        filter_options = self.get_health_providers_filter_options(self.get_base_queryset())

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        if not self._should_export_csv(request):
            page = paginator.paginate_queryset(providers, request)
        else:
            page = providers

        if page is not None:
            provider_data = [format_provider_data(provider) for provider in page]
            if self._should_export_csv(request):
                return self._get_csv_response("health_providers.csv", provider_data)

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": provider_data}
            )

        provider_data = [format_provider_data(provider) for provider in providers]
        return Response({"summary": summary, "filter_options": filter_options, "results": provider_data})

    def get_health_providers_filter_options(self, queryset):
        """Get filter options for health providers report"""
        organization = self.request.user.organizations.first()

        # Get distinct specialties from MedicalProvider model
        # Since specialties are comma-separated, we need to split and get unique values
        specialties_queryset = (
            MedicalProvider.objects.filter(organization=organization)
            .exclude(specialties__isnull=True)
            .exclude(specialties="")
            .values_list("specialties", flat=True)
            .distinct()
        )

        # Process comma-separated specialties into a unique set
        all_specialties = set()
        for specialty_string in specialties_queryset:
            if specialty_string:
                # Split by comma and strip whitespace
                specialties = [s.strip() for s in specialty_string.split(",")]
                all_specialties.update(specialties)

        # Sort the specialties alphabetically
        sorted_specialties = sorted(all_specialties)

        # Get distinct cities from contacts
        cities = (
            MedicalProvider.objects.filter(organization=organization)
            .values_list("contacts__city", flat=True)
            .filter(contacts__city__isnull=False)
            .distinct()
            .order_by("contacts__city")
        )

        return {
            "specialties": [{"value": s, "label": s} for s in sorted_specialties if s],
            "cities": [{"value": c, "label": c} for c in cities if c],
            "status_filters": [
                {"value": "accepts_liens", "label": "Accepts Liens"},
                {"value": "no_records_service", "label": "No Records Service"},
                {"value": "do_not_use", "label": "Do Not Use"},
            ],
        }

    @action(detail=False, methods=["get"])
    def insurance_adjusters(self, request):
        """Get insurance adjuster statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()

        # Get base queryset for adjusters, need both variables as base queryset is used for filter options
        queryset = adjuster_base_queryset = AdjusterContact.objects.filter(
            insurance_company__organization=request.user.organizations.first()
        ).select_related("insurance_company")

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(first_name__icontains=search_query)
                | Q(last_name__icontains=search_query)
                | Q(email__icontains=search_query)
                | Q(phone__icontains=search_query)
                | Q(insurance_company__name__icontains=search_query)
            ).distinct()

        # Apply filters
        # Insurance Company filter
        company = request.query_params.get("insurance_company")
        if company:
            queryset = queryset.filter(insurance_company_id=company)

        # Calculate summary statistics
        total_adjusters = queryset.count()

        # Get company breakdown
        company_stats = queryset.values("insurance_company__name").annotate(count=Count("id")).order_by("-count")

        summary = {"total_adjusters": total_adjusters, "by_company": {}}

        # Get adjusters with related data
        adjusters = (
            queryset.select_related("insurance_company")
            .prefetch_related(
                Prefetch(
                    "insurance_company__defendant_insurances",
                    queryset=DefendantInsurance.objects.filter(defendant__case__in=case_queryset).select_related(
                        "defendant__case"
                    ),
                ),
                Prefetch(
                    "insurance_company__client_insurances",
                    queryset=ClientInsurance.objects.filter(case__in=case_queryset).select_related("case"),
                ),
            )
            .order_by("insurance_company__name", "last_name")
        )

        def format_adjuster_data(adjuster):
            # Get case counts through insurance company
            defendant_cases = adjuster.insurance_company.defendant_insurances.values("defendant__case").distinct()
            # Get client case counts through insurance company
            client_cases = adjuster.insurance_company.client_insurances.values("case").distinct()

            # Combine both sets of cases uniquely
            all_case_ids = set()

            # Add defendant case IDs
            for case_dict in defendant_cases:
                all_case_ids.add(case_dict["defendant__case"])

            # Add client case IDs
            for case_dict in client_cases:
                all_case_ids.add(case_dict["case"])

            # Count open cases (filtering out closed and dropped cases)
            open_case_count = 0
            if all_case_ids:
                open_case_count = (
                    Case.objects.filter(id__in=all_case_ids)
                    .exclude(
                        organization_status__kpi_type__in=[
                            KPIType.case_closed,
                            KPIType.case_dropped,
                        ]
                    )
                    .count()
                )

            return {
                "name": f"{adjuster.first_name} {adjuster.last_name}",
                "company": adjuster.insurance_company.name,
                "phone": adjuster.phone,
                "email": adjuster.email,
                "open_cases": open_case_count,
                "total_cases": len(all_case_ids),
            }

        # Get filter options
        filter_options = self.get_insurance_adjusters_filter_options(adjuster_base_queryset)

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all adjusters for CSV export
            all_adjuster_data = [format_adjuster_data(adjuster) for adjuster in adjusters]

            # Prepare CSV data
            headers = ["Name", "Company", "Phone", "Email", "Open Cases", "Total Cases"]

            csv_data = []
            for adjuster in all_adjuster_data:
                csv_data.append(
                    [
                        adjuster.get("name", ""),
                        adjuster.get("company", ""),
                        adjuster.get("phone", ""),
                        adjuster.get("email", ""),
                        adjuster.get("open_cases", ""),
                        adjuster.get("total_cases", ""),
                    ]
                )

            return self._get_csv_response("insurance_adjusters.csv", csv_data, headers)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(adjusters, request)

        if page is not None:
            adjuster_data = [format_adjuster_data(adjuster) for adjuster in page]
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": adjuster_data}
            )

        adjuster_data = [format_adjuster_data(adjuster) for adjuster in adjusters]
        return Response({"summary": summary, "filter_options": filter_options, "results": adjuster_data})

    def get_insurance_adjusters_filter_options(self, queryset):
        """Get filter options for insurance adjusters report"""
        # Get insurance companies
        companies = (
            queryset.values("insurance_company_id", "insurance_company__name")
            .distinct()
            .order_by("insurance_company__name")
        )

        return {
            "insurance_companies": [
                {"value": c["insurance_company_id"], "label": c["insurance_company__name"]} for c in companies
            ]
        }

    @action(detail=False, methods=["get"])
    def insurance_companies(self, request):
        """Get insurance company statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset)

        # Get base queryset
        queryset = InsuranceCompany.objects.filter(organization=request.user.organizations.first())

        # Filter by insurance type (client/defendant)
        insurance_type = request.query_params.get("insurance_type")
        if insurance_type:
            if insurance_type == "client":
                queryset = queryset.filter(client_insurances__case__in=case_queryset).distinct()
            elif insurance_type == "defendant":
                queryset = queryset.filter(defendant_insurances__defendant__case__in=case_queryset).distinct()

        company_type = request.query_params.get("company_type")
        if company_type:
            queryset = queryset.filter(type=company_type)

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) | Q(phone__icontains=search_query) | Q(email__icontains=search_query)
            ).distinct()

        # Calculate summary statistics
        total_companies = queryset.count()

        # Get type breakdown
        type_stats = {
            "client": queryset.filter(client_insurances__isnull=False).distinct().count(),
            "defendant": queryset.filter(defendant_insurances__isnull=False).distinct().count(),
        }

        summary = {"total_companies": total_companies, "by_type": type_stats}

        # Get companies with related data
        companies = queryset.prefetch_related(
            Prefetch(
                "defendant_insurances",
                queryset=DefendantInsurance.objects.filter(defendant__case__in=case_queryset).select_related(
                    "defendant__case"
                ),
            ),
            Prefetch(
                "client_insurances",
                queryset=ClientInsurance.objects.filter(case__in=case_queryset).select_related("case"),
            ),
            "adjuster_contacts",
        ).order_by("name")

        def format_company_data(company):
            # Get defendant case counts
            defendant_cases = company.defendant_insurances.values("defendant__case").distinct()
            defendant_open_cases = defendant_cases.exclude(
                defendant__case__organization_status__kpi_type=KPIType.case_closed
            ).count()
            defendant_total_cases = defendant_cases.count()

            # Get client case counts
            client_cases = company.client_insurances.values("case").distinct()
            client_open_cases = client_cases.exclude(case__organization_status__kpi_type=KPIType.case_closed).count()
            client_total_cases = client_cases.count()

            all_cases = (
                Case.objects.filter(
                    Q(v2_defendants__insurances__insurance_company=company) | Q(insurances__insurance_company=company)
                )
            ).distinct()

            # Calculate total fees across all cases
            calculations = all_cases.aggregate(
                total_settlement_proceeds=models.Sum(
                    "settlement_calculations__settlement_proceed",
                    default=Decimal("0.00"),
                ),
                total_medical_bills=models.Sum(
                    "settlement_calculations__medical_bills",
                    default=Decimal("0.00"),
                ),
            )

            total_settlement_proceeds = calculations["total_settlement_proceeds"] or 0
            total_medical_bills = calculations["total_medical_bills"] or 0

            total_cases = all_cases.count()

            return {
                "name": company.name,
                "phone": company.phone,
                "email": company.email,
                "total_cases": total_cases,
                "total_adjusters": company.adjuster_contacts.count(),
                "defendant_cases_open": defendant_open_cases,
                "defendant_cases_total": defendant_total_cases,
                "client_cases_open": client_open_cases,
                "client_cases_total": client_total_cases,
                "type": company.type,
                "total_settlements": total_settlement_proceeds,
                "total_medical_bills": total_medical_bills,
            }

        # Get filter options
        filter_options = {
            "insurance_types": [
                {"value": "client", "label": "Client Insurance"},
                {"value": "defendant", "label": "Defendant Insurance"},
            ],
            "company_types": [{"value": t[0], "label": t[1]} for t in InsuranceCompany.type_choices],
        }

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all companies for CSV export
            all_company_data = [format_company_data(company) for company in companies]
            return self._get_csv_response("insurance_companies.csv", all_company_data)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(companies, request)

        if page is not None:
            company_data = [format_company_data(company) for company in page]
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": company_data}
            )

        company_data = [format_company_data(company) for company in companies]
        return Response({"summary": summary, "filter_options": filter_options, "results": company_data})

    @action(detail=False, methods=["get"])
    def lien_holders(self, request):
        """Get lien holder statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset)
        queryset = LienHolder.objects.filter(organization=request.user.organizations.first())

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query)
                | Q(contact_name__icontains=search_query)
                | Q(phone__icontains=search_query)
                | Q(email__icontains=search_query)
            ).distinct()

        # Calculate summary statistics
        total_lien_holders = queryset.count()
        total_health_liens = TreatmentProvider.objects.filter(lien_holder__in=queryset).count()

        # Get lien type breakdown from treatment providers
        lien_stats = (
            TreatmentProvider.objects.filter(case__in=case_queryset, lien_holder__isnull=False)
            .values("lien_holder__first_name", "lien_holder__last_name")
            .annotate(
                signed_liens=Count("id", filter=Q(signed_lien=True)),
                filed_liens=Count("id", filter=Q(filed_lien=True)),
                total_liens=Count("id"),
            )
            .order_by("-total_liens")
        )

        summary = {
            "total_lien_holders": total_lien_holders,
            "total_health_liens": total_health_liens,
        }

        # Get lien holders with related data
        lien_holders = queryset.prefetch_related(
            Prefetch(
                "treatments",
                queryset=TreatmentProvider.objects.filter(case__in=case_queryset).select_related(
                    "case", "medical_provider"
                ),
            )
        ).order_by("company")

        def format_lien_holder_data(lien_holder):
            # Get treatments with liens
            treatments = lien_holder.treatments.all()

            # Get case counts
            cases = {t.case for t in treatments}  # Use set for unique cases
            open_cases = sum(1 for case in cases if case.organization_status.kpi_type != KPIType.case_closed)

            # Calculate lien statistics
            signed_liens = sum(1 for t in treatments if t.signed_lien)
            filed_liens = sum(1 for t in treatments if t.filed_lien)

            # Calculate total amounts
            total_original = sum(t.original_bill or 0 for t in treatments)
            total_adjusted = sum(t.adjusted_bill or 0 for t in treatments)
            avg_reduction = 0
            if total_original > 0:  # Avoid division by zero
                avg_reduction = ((total_original - total_adjusted) / total_original) * 100

            return {
                "name": f"{lien_holder.first_name} {lien_holder.last_name}",
                "company": lien_holder.company,
                "phone": lien_holder.phone,
                "email": lien_holder.email,
                "open_cases": open_cases,
                "total_cases": len(cases),
                "total_health_liens": len(treatments),
                "liens_signed": signed_liens,
                "liens_filed": filed_liens,
                "amount_original": total_original,
                "amount_adjusted": total_adjusted,
                "avg_reduction": avg_reduction,
            }

        # Get filter options
        # TODO: Add filter options
        """
        Case Status
        Lien Holder
        Worker
        Outstanding Liens
        """
        filter_options = {}  # self.get_lien_holders_filter_options(self.get_base_queryset())

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all lien holders for CSV export
            all_lien_holder_data = [format_lien_holder_data(lien_holder) for lien_holder in lien_holders]

            return self._get_csv_response("lien_holders.csv", all_lien_holder_data)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(lien_holders, request)

        if page is not None:
            lien_holder_data = [format_lien_holder_data(lien_holder) for lien_holder in page]
            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": lien_holder_data}
            )

        lien_holder_data = [format_lien_holder_data(lien_holder) for lien_holder in lien_holders]
        return Response({"summary": summary, "filter_options": filter_options, "results": lien_holder_data})

    @action(detail=False, methods=["get"])
    def medpay_requests(self, request):
        """Get MedPay request statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)
        # Get treatment providers with medpay_pip_paid values
        queryset = TreatmentProvider.objects.filter(case__in=case_queryset)

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            case_queryset = case_queryset.filter(
                Q(client_basic_details__first_name__icontains=search_query)
                | Q(client_basic_details__last_name__icontains=search_query)
                | Q(id__icontains=search_query)
                | Q(treatment_providers__medical_provider__company__icontains=search_query)
                | Q(v2_workers__lead_attorney__first_name__icontains=search_query)
                | Q(v2_workers__lead_attorney__last_name__icontains=search_query)
                | Q(v2_workers__case_manager__first_name__icontains=search_query)
                | Q(v2_workers__case_manager__last_name__icontains=search_query)
            ).distinct()
            queryset = queryset.filter(case__in=case_queryset, medpay_pip_paid__isnull=False).exclude(medpay_pip_paid=0)

        # Apply date range filters
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        date_field = request.query_params.get("date_field")

        if from_date and to_date:
            if date_field == "billing_date":
                queryset = queryset.filter(case__treatment_providers__billing_received_at__range=[from_date, to_date])
            elif date_field == "recorded_date":
                queryset = queryset.filter(case__treatment_providers__recorded_date__range=[from_date, to_date])

        # Get summary statistics
        total_requests = queryset.count()
        total_demanded = queryset.aggregate(Sum("medpay_pip_paid"))["medpay_pip_paid__sum"] or 0

        summary = {"medpay_requests": total_requests, "total_demanded": float(total_demanded)}

        # Get filter options
        filter_options = self.get_medpay_requests_filter_options(self.get_base_queryset())

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all medpay requests for CSV export
            all_medpay_data = []
            for treatment in queryset:
                case = treatment.case
                insurance = self._get_client_insurance(case)

                all_medpay_data.append(
                    {
                        "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {case.incident_date.strftime('%m/%d/%Y') if case.incident_date else None} : {case.id}",
                        "date_sent": treatment.updated_at.strftime("%m/%d/%Y"),
                        "insurance": insurance.insurance_company.name if insurance else None,
                        "medpay_limits": self._get_medpay_limits(insurance),
                        "total_meds": float(treatment.original_bill or 0),
                        "costs": float(treatment.adjusted_bill or 0),
                        "case_manager": self._get_case_manager_name(case),
                        "requested": float(treatment.medpay_pip_paid or 0),
                        "team": self._get_team_data(case.v2_workers) if case.v2_workers else [],
                    }
                )

            # Prepare CSV data
            headers = [
                "Case",
                "Date Sent",
                "Insurance",
                "MedPay Limits",
                "Total Meds",
                "Costs",
                "Case Manager",
                "Requested",
            ]

            csv_data = []
            for item in all_medpay_data:
                csv_data.append(
                    [
                        item.get("case", ""),
                        item.get("date_sent", ""),
                        item.get("insurance", ""),
                        item.get("medpay_limits", ""),
                        item.get("total_meds", ""),
                        item.get("costs", ""),
                        item.get("case_manager", ""),
                        item.get("requested", ""),
                        item.get("team", ""),
                    ]
                )

            return self._get_csv_response("medpay_requests.csv", csv_data, headers)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            medpay_data = []
            for treatment in page:
                case = treatment.case
                insurance = self._get_client_insurance(case)

                medpay_data.append(
                    {
                        "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details and case.incident_details.incident_date else None} : {case.id}",
                        "date_sent": treatment.updated_at.strftime("%m/%d/%Y"),
                        "insurance": insurance.insurance_company.name if insurance else None,
                        "medpay_limits": self._get_medpay_limits(insurance),
                        "total_meds": float(treatment.original_bill or 0),
                        "costs": float(treatment.adjusted_bill or 0),
                        "case_manager": self._get_case_manager_name(case),
                        "requested": float(treatment.medpay_pip_paid or 0),
                        "team": self._get_team_data(case.v2_workers) if case.v2_workers else [],
                    }
                )

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": medpay_data}
            )

        # Format all results (no pagination)

    def get_medpay_requests_filter_options(self, queryset):
        """Get filter options for medpay requests report"""
        organization = self.request.user.organizations.first()

        # Get team options
        # Get case type options
        case_types = (
            queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )
        case_type_options = [{"value": ct, "label": ct} for ct in case_types if ct]

        # Get case status category options

        date_fields = [
            {"value": "billing_date", "label": "Billing Date"},
            {"value": "recorded_date", "label": "Recorded Date"},
        ]

        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset),
            **self.case_workers_filters_options(queryset),
            "date_fields": date_fields,
        }

    def _get_client_insurance(self, case):
        """Get client's insurance with MedPay coverage"""
        try:
            return case.client_insurances.filter(insurance_type__in=["AUTO", "HEALTH", "UMBRELLA"]).first()
        except:
            return None

    def _get_medpay_limits(self, insurance):
        """Get MedPay limits from client insurance"""
        if not insurance:
            return None

        # Check for MedPay coverage in policy details
        try:
            if hasattr(insurance, "policy_details") and insurance.policy_details:
                if "medpay_limit" in insurance.policy_details:
                    return insurance.policy_details.get("medpay_limit")
                elif "pip_limit" in insurance.policy_details:
                    return insurance.policy_details.get("pip_limit")
            return None
        except:
            return None

    def _get_case_manager_name(self, case):
        """Get case manager's name"""
        try:
            if hasattr(case, "v2_workers") and case.v2_workers.case_manager:
                cm = case.v2_workers.case_manager
                return f"{cm.first_name} {cm.last_name}"
            return None
        except:
            return None

    def _get_lead_attorney_name(self, case):
        """Get lead attorney's name"""
        try:
            if hasattr(case, "v2_workers") and case.v2_workers.lead_attorney:
                la = case.v2_workers.lead_attorney
                return f"{la.first_name} {la.last_name}"
            return None
        except:
            return None

    @action(detail=False, methods=["get"])
    def notes(self, request):
        """Get case notes statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()

        # Get base queryset for notes
        queryset = CaseNote.objects.filter(case__in=case_queryset)

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(note__icontains=search_query)
                | Q(case__client_basic_details__first_name__icontains=search_query)
                | Q(case__client_basic_details__last_name__icontains=search_query)
                | Q(case__id__icontains=search_query)
                | Q(created_by__first_name__icontains=search_query)
                | Q(created_by__last_name__icontains=search_query)
                | Q(directed_to__first_name__icontains=search_query)
                | Q(directed_to__last_name__icontains=search_query)
                | Q(note_tags__name__icontains=search_query)
            ).distinct()

        # Apply date range filters
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        date_field = request.query_params.get("date_field", "created_at")  # Default to created_at if not specified

        if from_date and to_date:
            if date_field == "updated_at":
                queryset = queryset.filter(updated_at__range=[from_date, to_date])
            else:  # default to created_at
                queryset = queryset.filter(created_at__range=[from_date, to_date])

        # Apply worker filter (created by)
        worker = request.query_params.get("worker")
        if worker:
            queryset = queryset.filter(created_by_id=worker)

        # Apply case status filter
        organization_status_id = request.query_params.get("organization_status")
        if organization_status_id:
            queryset = queryset.filter(case__organization_status_id=organization_status_id)

        tags = request.query_params.get("tags")
        if tags:
            # Convert string of tags into a list if it's comma-separated
            tag_list = tags.split(",") if isinstance(tags, str) else tags
            queryset = queryset.filter(tags__name__in=tag_list)

        # Apply case status category filter
        case_status_category = request.query_params.get("case_status_category")
        if case_status_category:
            queryset = queryset.filter(case__organization_status__name=case_status_category)

        # Apply directed to filter
        directed_to = request.query_params.get("directed_to")
        if directed_to:
            queryset = queryset.filter(directed_to_id=directed_to)

        # Apply auto notes filter
        include_auto_notes = request.query_params.get("include_auto_notes", "true").lower() == "true"
        if not include_auto_notes:
            queryset = queryset.filter(is_auto_generated=False)

        # Get summary statistics
        total_notes = queryset.count()

        # Get category breakdown
        tag_stats = (
            queryset.values("tags__name")
            .annotate(count=Count("id"))
            .filter(tags__name__isnull=False)
            .order_by("-count")
        )

        summary = {
            "total_notes": total_notes,
            "by_tags": {stat["tags__name"]: stat["count"] for stat in tag_stats if stat["tags__name"]},
        }

        # Get filter options
        filter_options = self.get_notes_filter_options(self.get_base_queryset())

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Order by created_at descending by default
        queryset = queryset.order_by("-created_at")

        if not self._should_export_csv(request):
            page = paginator.paginate_queryset(queryset, request)
        else:
            page = queryset

        if page is not None:
            notes_data = []
            for note in page:
                case = note.case

                notes_data.append(
                    {
                        "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details and case.incident_details.incident_date else None} : {case.id}",
                        "note": note.content,
                        "date": note.created_at.strftime("%m/%d/%Y %I:%M %p"),
                        "tags": [{"name": tag.name, "id": tag.id} for tag in note.tags.all()],
                        "added_by": f"{note.created_by.first_name} {note.created_by.last_name}",
                        "directed_to": f"{', '.join([user.first_name + ' ' + user.last_name for user in note.tagged_users.all()])}"
                        if note.tagged_users.count() > 0
                        else None,
                    }
                )
            if self._should_export_csv(request):
                return self._get_csv_response("notes.csv", notes_data)

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": notes_data}
            )

    def get_notes_filter_options(self, queryset):
        """Get filter options for notes report"""
        organization = self.request.user.organizations.first()

        # Get worker options (users who can create notes)
        workers = organization.users.all().order_by("first_name", "last_name")
        worker_options = [
            {"value": worker.id, "label": f"{worker.first_name} {worker.last_name}"} for worker in workers
        ]

        # Get case status options
        case_statuses = Case.objects.filter(organization=organization).values_list("status", flat=True).distinct()
        case_status_options = [{"value": cs, "label": cs} for cs in case_statuses if cs]

        tag_stats = (
            CaseNote.objects.filter(case__in=queryset)
            .values("tags__name")
            .annotate(count=Count("id"))
            .filter(tags__name__isnull=False)
            .order_by("-count")
        )

        tags_filter = list(set([stat["tags__name"] for stat in tag_stats if stat["tags__name"]]))
        tags_filter_options = [{"value": tag, "label": tag} for tag in tags_filter]

        # Add date field options
        date_field_options = [
            {"value": "created_at", "label": "Created Date"},
            {"value": "updated_at", "label": "Updated Date"},
        ]

        return {
            "workers": worker_options,
            "organization_statuses": self.get_case_status_categories(queryset),
            "tags": tags_filter_options,
            "date_fields": date_field_options,  # Add date fields to filter options
        }

    @action(detail=False, methods=["get"])
    def source_performance(self, request):
        """Get source performance statistics and details"""
        # Get tile filter parameter (default to source_detail)
        tile_filter = request.query_params.get("tile", "source_detail")

        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset)

        # Debug: Log the number of cases with source details
        source_detail_count = (
            case_queryset.exclude(client_basic_details__source_detail__isnull=True)
            .exclude(client_basic_details__source_detail="")
            .count()
        )

        source_type_count = (
            case_queryset.exclude(client_basic_details__source_type__isnull=True)
            .exclude(client_basic_details__source_type="")
            .count()
        )

        logger.info(f"Cases with source details: {source_detail_count} out of {case_queryset.count()}")
        logger.info(f"Cases with source types: {source_type_count} out of {case_queryset.count()}")

        # Create mapping of source_detail to most common source_type
        source_detail_to_type_map = {}
        source_details = (
            case_queryset.exclude(client_basic_details__source_type__isnull=True)
            .exclude(client_basic_details__source_type="")
            .exclude(client_basic_details__source_detail__isnull=True)
            .exclude(client_basic_details__source_detail="")
            .values_list("client_basic_details__source_detail", "client_basic_details__source_type")
        )

        # Count frequency of source types for each source detail
        for source_detail, source_type in source_details:
            if source_detail not in source_detail_to_type_map:
                source_detail_to_type_map[source_detail] = {}
            if source_type not in source_detail_to_type_map[source_detail]:
                source_detail_to_type_map[source_detail][source_type] = 0
            source_detail_to_type_map[source_detail][source_type] += 1

        # Take most frequent source type for each source detail
        for source_detail in list(source_detail_to_type_map.keys()):
            counts = source_detail_to_type_map[source_detail]
            most_common = max(counts.items(), key=lambda x: x[1])[0]
            source_detail_to_type_map[source_detail] = most_common

        # Create a Case-When expression to handle source type mapping
        when_expressions = []

        # Handle null/empty source detail case
        when_expressions.append(
            When(
                Q(client_basic_details__source_detail__isnull=True) | Q(client_basic_details__source_detail=""),
                then=Value(""),
            )
        )

        # Handle non-null, non-empty source details by mapping to their source type
        for source_detail, source_type in source_detail_to_type_map.items():
            when_expressions.append(When(Q(client_basic_details__source_detail=source_detail), then=Value(source_type)))

        # Apply the case expressions to create a corrected source_type
        case_queryset = case_queryset.annotate(
            corrected_source_type=CaseFunc(
                *when_expressions,
                default=Value(""),  # Default to empty string if no matches
                output_field=models.CharField(),
            )
        )

        # Get base queryset grouped by source detail or source type based on tile filter
        if tile_filter == "source_detail":
            # For source_detail view, we use both source_type and source_detail
            queryset = (
                case_queryset.values("corrected_source_type", "client_basic_details__source_detail")
                .annotate(
                    total_leads=Count("id", distinct=True),
                    total_retained=Count("id", filter=Q(retained=True), distinct=True),
                    settlement_checks_amount=Coalesce(
                        Sum(
                            "client_trust_entries__amount",
                            filter=Q(
                                client_trust_entries__client_trust_entry_type="SETTLEMENT_CHECK",
                            ),
                        ),
                        0,
                        output_field=models.DecimalField(max_digits=12, decimal_places=2),
                    ),
                    fees_checks_amount=Coalesce(
                        Sum(
                            "client_trust_entries__amount",
                            filter=Q(client_trust_entries__client_trust_entry_type="ATTORNEY_CHECK"),
                        ),
                        0,
                        output_field=models.DecimalField(max_digits=12, decimal_places=2),
                    ),
                )
                .order_by("-total_leads")
            )
        else:  # source_type
            # For source_type view, we only group by source_type
            queryset = (
                case_queryset.values("corrected_source_type")
                .annotate(
                    total_leads=Count("id"),
                    total_retained=Count("id", filter=Q(retained=True)),
                    settlement_checks_amount=Coalesce(
                        Sum(
                            "client_trust_entries__amount",
                            filter=Q(
                                client_trust_entries__client_trust_entry_type="SETTLEMENT_CHECK",
                            ),
                        ),
                        0,
                        output_field=models.DecimalField(max_digits=12, decimal_places=2),
                    ),
                    fees_checks_amount=Coalesce(
                        Sum(
                            "client_trust_entries__amount",
                            filter=Q(client_trust_entries__client_trust_entry_type="ATTORNEY_CHECK"),
                        ),
                        0,
                        output_field=models.DecimalField(max_digits=12, decimal_places=2),
                    ),
                )
                .order_by("-total_leads")
            )

        # Format data for display
        def format_source_data(source):
            # Get source type from corrected field if available
            source_type = source.get("corrected_source_type") or "Unknown"
            # Fall back to original field if needed
            if source_type == "Unknown" and "client_basic_details__source_type" in source:
                source_type = source.get("client_basic_details__source_type") or "Unknown"

            source_detail = source.get("client_basic_details__source_detail") or "Unknown"

            # If source_detail is "Unknown" but we have a source_type, use that as the detail
            # This helps when source_detail is missing but source_type is present
            if source_detail == "Unknown" and source_type != "Unknown":
                # Check if this is a grouped query (tile=source_type)
                if "client_basic_details__source_detail" not in source:
                    source_detail = source_type

            # If source_type is still Unknown/None but source_detail exists and we have a mapping
            if (source_type == "Unknown" or not source_type) and source_detail != "Unknown":
                if source_detail in source_detail_to_type_map:
                    source_type = source_detail_to_type_map[source_detail]

            total_leads = source.get("total_leads", 0)
            total_retained = source.get("total_retained", 0)

            total_settlements = source.get("settlement_checks_amount", 0)

            total_fees = abs(source.get("fees_checks_amount", 0))

            conversion = round((total_retained / total_leads * 100) if total_leads else 0)

            # Calculate fees per case only for settled cases
            fees_per_case = round(total_fees / total_leads if total_leads else 0, 2)

            return {
                "source_detail": source_detail,
                "source_type": source_type,
                "cost_per_month": 0.00,  # Placeholder, would need actual cost data
                "leads": total_leads,
                "retained": total_retained,
                "conversion": conversion,
                "total_settlements": total_settlements,
                "total_fees": total_fees,
                "fees_per_case": fees_per_case,
            }

        # Calculate summary data directly from the querysets for tiles
        # We calculate this separately to avoid double-counting
        # For source_detail summary
        source_detail_summary = {
            "total_leads": 0,
            "total_retained": 0,
            "total_settlements": 0,
            "total_fees": 0,
            "total_settled_cases": 0,
        }

        # For source_type summary
        source_type_summary = {
            "total_leads": 0,
            "total_retained": 0,
            "total_settlements": 0,
            "total_fees": 0,
            "total_settled_cases": 0,
        }

        # Calculate source_detail summary
        source_detail_data = case_queryset.aggregate(
            total_leads=Count("id"),
            total_retained=Count("id", filter=Q(retained=True)),
            # Get total settlements from settlement calculations
            total_settlements=Coalesce(
                Sum("settlement_calculations__settlement_proceed"),
                0,
                output_field=models.DecimalField(max_digits=12, decimal_places=2),
            ),
            # Get total fees from settlement calculations
            total_fees=Coalesce(
                Sum("settlement_calculations__settlement_fees"),
                0,
                output_field=models.DecimalField(max_digits=12, decimal_places=2),
            ),
            # Count settled cases
            settled_cases=Count("settlement_calculations"),
        )

        source_detail_summary["total_leads"] = source_detail_data["total_leads"]
        source_detail_summary["total_retained"] = source_detail_data["total_retained"]
        source_detail_summary["total_settlements"] = float(source_detail_data["total_settlements"])
        source_detail_summary["total_fees"] = float(source_detail_data["total_fees"])
        source_detail_summary["total_settled_cases"] = source_detail_data["settled_cases"]
        source_detail_summary["retention_rate"] = round(
            (source_detail_summary["total_retained"] / source_detail_summary["total_leads"] * 100)
            if source_detail_summary["total_leads"]
            else 0,
            1,
        )

        # Source type summary uses the same aggregate data - no need to query again
        source_type_summary = source_detail_summary.copy()

        # Format the summary data into tiles
        tiles = [
            {
                "title": "BY SOURCE DETAIL",
                "lead_count": source_detail_summary["total_leads"],
                "retained_count": source_detail_summary["total_retained"],
                "conversion_rate": f"{source_detail_summary['retention_rate']}%",
                "filter_value": "source_detail",
                "active": tile_filter == "source_detail",
            },
            {
                "title": "BY SOURCE TYPE",
                "lead_count": source_type_summary["total_leads"],
                "retained_count": source_type_summary["total_retained"],
                "conversion_rate": f"{source_type_summary['retention_rate']}%",
                "filter_value": "source_type",
                "active": tile_filter == "source_type",
            },
        ]

        # Get filter options
        filter_options = self.get_source_performance_filter_options(self.get_base_queryset())

        # Check if CSV export is requested
        if self._should_export_csv(request):
            all_source_data = [format_source_data(source) for source in queryset]
            return self._get_csv_response("source_performance.csv", all_source_data)

        # Handle pagination
        page = self.paginator.paginate_queryset(queryset, request)
        if page is not None:
            source_data = [format_source_data(source) for source in page]
            return self.paginator.get_paginated_response(
                {
                    "summary": source_detail_summary if tile_filter == "source_detail" else source_type_summary,
                    "tiles": tiles,
                    "filter_options": filter_options,
                    "results": source_data
                    if tile_filter == "source_detail"
                    else [{k: v for k, v in item.items() if k != "source_detail"} for item in source_data],
                }
            )

        # Format all results (no pagination)
        source_data = [format_source_data(source) for source in queryset]
        return Response(
            {
                "summary": source_detail_summary if tile_filter == "source_detail" else source_type_summary,
                "tiles": tiles,
                "filter_options": filter_options,
                "results": source_data,
            }
        )

    def get_source_performance_filter_options(self, queryset):
        """Get filter options for source performance report"""
        organization = self.request.user.organizations.first()

        # Get the current tile parameter from request
        tile_filter = self.request.query_params.get("tile", "source_detail")

        # Get source detail options
        source_details = (
            Case.objects.filter(organization=organization)
            .values_list("client_basic_details__source_detail", flat=True)
            .distinct()
        )
        sources = list(set([sd for sd in source_details if sd]))
        source_detail_options = [{"value": sd, "label": sd} for sd in sources]

        source_types = (
            Case.objects.filter(organization=organization)
            .values_list("client_basic_details__source_type", flat=True)
            .distinct()
        )
        sources_types = list(set([sd for sd in source_types if sd]))
        source_types_options = [{"value": sd, "label": sd} for sd in sources_types]

        # View by options (for grouping)
        view_by_options = [
            {"value": "source_detail", "label": "Source Detail"},
            {"value": "source_type", "label": "Source Type"},
        ]

        # Quick filter presets
        quick_filters = [
            {"value": "high_conversion", "label": "High Conversion (>50%)"},
            {"value": "low_conversion", "label": "Low Conversion (<20%)"},
            {"value": "high_value", "label": "High Value Sources (>$10k fees/case)"},
            {"value": "recent_leads", "label": "Recent Leads (Last 30 Days)"},
        ]

        date_fields = [{"value": "created_at", "label": "Created Date"}]

        return {
            "source_details": source_detail_options,
            "view_by": view_by_options,
            "quick_filters": quick_filters,
            "source_types": source_types_options,
            "date_fields": date_fields,
            "current_tile": tile_filter,
        }

    def apply_source_performance_quick_filter(self, queryset, filter_name):
        """Apply predefined quick filters to source performance data"""
        if filter_name == "high_conversion":
            # Filter sources with conversion rate > 50%
            filtered_items = []
            for item in queryset:
                total_leads = item.get("total_leads", 0)
                total_retained = item.get("total_retained", 0)
                if total_leads > 0 and (total_retained / total_leads) > 0.5:
                    filtered_items.append(item)
            return filtered_items

        elif filter_name == "low_conversion":
            # Filter sources with conversion rate < 20%
            filtered_items = []
            for item in queryset:
                total_leads = item.get("total_leads", 0)
                total_retained = item.get("total_retained", 0)
                if total_leads > 0 and (total_retained / total_leads) < 0.2:
                    filtered_items.append(item)
            return filtered_items

        elif filter_name == "high_value":
            # Filter sources with fees per case > $10,000
            filtered_items = []
            for item in queryset:
                total_retained = item.get("total_retained", 0)
                total_fees = float(item.get("total_fees", 0))
                if total_retained > 0 and (total_fees / total_retained) > 10000:
                    filtered_items.append(item)
            return filtered_items

        elif filter_name == "recent_leads":
            # This would need to be implemented at the database query level
            thirty_days_ago = timezone.now() - timedelta(days=30)
            return queryset.filter(created_at__gte=thirty_days_ago)

        return queryset

    @action(detail=False, methods=["get"])
    def medical_requests(self, request):
        """Get medical request statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        # Get base queryset for medical requests
        # Use records_status or billing_status instead of request_type
        queryset = (
            TreatmentProvider.objects.filter(case__in=case_queryset)
            .filter(Q(records_status__isnull=False) | Q(billing_status__isnull=False))
            .select_related(
                "case", "case__v2_workers__lead_attorney", "case__v2_workers__case_manager", "medical_provider"
            )
        )

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(client_basic_details__first_name__icontains=search_query)
                | Q(client_basic_details__last_name__icontains=search_query)
                | Q(id__icontains=search_query)
                | Q(medical_provider__company__icontains=search_query)
                | Q(v2_workers__lead_attorney__first_name__icontains=search_query)
                | Q(v2_workers__lead_attorney__last_name__icontains=search_query)
            ).distinct()

        # Apply date range filters
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        date_field = request.query_params.get("date_field")
        if from_date and to_date:
            if date_field == "billing_date":
                queryset = queryset.filter(case__treatment_providers__billing_received_at__range=[from_date, to_date])
            elif date_field == "recorded_date":
                queryset = queryset.filter(case__treatment_providers__recorded_date__range=[from_date, to_date])

        # Apply case type filter
        case_type = request.query_params.get("case_type")
        if case_type:
            queryset = queryset.filter(case__incident_details__incident_type=case_type)

        # Apply case status filter
        case_status = request.query_params.get("case_status")
        if case_status:
            queryset = queryset.filter(case__status=case_status)

        # Apply case status category filter
        case_status_category = request.query_params.get("case_status_category")
        if case_status_category:
            queryset = queryset.filter(case__organization_status__name=case_status_category)

        # Apply case manager filter
        case_manager = request.query_params.get("case_manager")
        if case_manager:
            queryset = queryset.filter(case__v2_workers__case_manager_id=case_manager)

        # Apply lead attorney filter
        lead_attorney = request.query_params.get("lead_attorney")
        if lead_attorney:
            queryset = queryset.filter(case__v2_workers__lead_attorney_id=lead_attorney)

        # Apply specialty filter
        specialty = request.query_params.get("specialty")
        if specialty:
            queryset = queryset.filter(medical_provider__specialties__icontains=specialty)

        # Apply treatment status filter
        treatment_status = request.query_params.get("treatment_status")
        if treatment_status:
            queryset = queryset.filter(treatment_status=treatment_status)

        # Apply request type filter
        request_type = request.query_params.get("request_type")
        if request_type:
            if request_type == "Billing":
                queryset = queryset.filter(billing_status__isnull=False)
            elif request_type == "Records":
                queryset = queryset.filter(records_status__isnull=False)

        # Apply request status filter
        request_status = request.query_params.get("request_status")
        if request_status:
            if request_status == "Requested":
                queryset = queryset.filter(Q(records_status="REQUESTED") | Q(billing_status="REQUESTED"))
            elif request_status == "Complete":
                queryset = queryset.filter(Q(records_status="RECEIVED") | Q(billing_status="RECEIVED"))
            elif request_status == "Treating":
                queryset = queryset.filter(treatment_status="TREATING")

        # Get summary statistics
        total_requests = queryset.count()
        billing_requests = queryset.filter(billing_status__isnull=False).count()
        records_requests = queryset.filter(records_status__isnull=False).count()

        summary = {
            "total_requests": total_requests,
            "billing_requests": billing_requests,
            "records_requests": records_requests,
        }

        # Get filter options
        filter_options = self.get_medical_requests_filter_options(self.get_base_queryset())

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Order by updated_at descending by default
        queryset = queryset.order_by("-updated_at")

        if not self._should_export_csv(request):
            page = paginator.paginate_queryset(queryset, request)
        else:
            page = queryset

        if page is not None:
            request_data = []
            for treatment in page:
                case = treatment.case

                # Handle both billing and records requests separately
                requests = []

                # Add billing request if exists
                if treatment.billing_status:
                    requests.append(
                        {
                            "type": "Billing",
                            "status": "Complete"
                            if treatment.billing_status == "RECEIVED"
                            else "Requested"
                            if treatment.billing_status == "REQUESTED"
                            else treatment.billing_status,
                        }
                    )

                # Add records request if exists
                if treatment.records_status:
                    requests.append(
                        {
                            "type": "Records",
                            "status": "Complete"
                            if treatment.records_status == "RECEIVED"
                            else "Requested"
                            if treatment.records_status == "REQUESTED"
                            else treatment.records_status,
                        }
                    )

                # Create base entry that will be duplicated for each request type
                base_entry = {
                    "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details and case.incident_details.incident_date else None} : {case.id}",
                    "case_status": case.organization_status.display_name if case.organization_status else None,
                    "medical_provider": treatment.medical_provider.company if treatment.medical_provider else None,
                    "request_date": treatment.billing_requested_at.strftime("%m/%d/%Y")
                    if treatment.billing_requested_at
                    else treatment.records_requested_at.strftime("%m/%d/%Y")
                    if treatment.records_requested_at
                    else None,
                    "treatment_status": treatment.treatment_status,
                    "lead_attorney": f"{case.v2_workers.lead_attorney.first_name} {case.v2_workers.lead_attorney.last_name}"
                    if hasattr(case, "v2_workers") and case.v2_workers.lead_attorney
                    else None,
                }

                # Add an entry for each request type
                for _request in requests:
                    entry = base_entry.copy()
                    entry["request_type"] = _request["type"]
                    entry["status"] = _request["status"]
                    request_data.append(entry)
            if self._should_export_csv(request):
                return self._get_csv_response("medical_requests.csv", request_data)

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": request_data}
            )

    def get_medical_requests_filter_options(self, queryset):
        """Get filter options for medical requests report"""
        organization = self.request.user.organizations.first()

        # Get case type options
        case_types = (
            queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )
        case_type_options = [{"value": ct, "label": ct} for ct in case_types if ct]

        # Get specialty options
        specialties = (
            MedicalProvider.objects.filter(organization=organization).values_list("specialties", flat=True).distinct()
        )
        specialty_options = [{"value": s, "label": s} for s in specialties if s]

        # Get treatment status options from the model's choices
        treatment_status_options = [
            {"value": status[0], "label": status[1]} for status in TreatmentProvider.TREATMENT_STATUS_CHOICES
        ]

        # Get request type options
        request_type_options = [{"value": "Billing", "label": "Billing"}, {"value": "Records", "label": "Records"}]

        # Get request status options
        request_status_options = [
            {"value": "Requested", "label": "Requested"},
            {"value": "Complete", "label": "Complete"},
            {"value": "Treating", "label": "Treating"},
        ]

        date_fields = [
            {"value": "billing_date", "label": "Billing Date"},
            {"value": "recorded_date", "label": "Recorded Date"},
        ]

        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset),
            "specialties": specialty_options,
            "treatment_statuses": treatment_status_options,
            "request_types": request_type_options,
            "request_statuses": request_status_options,
            **self.case_workers_filters_options(queryset),
            "date_fields": date_fields,
        }

    @action(detail=False, methods=["get"])
    def statute(self, request):
        """Get statute of limitations statistics and details"""

        def format_statute_data(case):
            statute_date = case.incident_details.statute_of_limitations
            days_left = (statute_date - current_date).days if statute_date else None

            return {
                "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}",
                "case_type": case.incident_details.incident_type if hasattr(case, "incident_details") else None,
                "case_status": case.organization_status.display_name if case.organization_status else None,
                "lead_attorney": f"{case.v2_workers.lead_attorney.first_name} {case.v2_workers.lead_attorney.last_name}"
                if hasattr(case, "v2_workers") and case.v2_workers.lead_attorney
                else None,
                "case_manager": f"{case.v2_workers.case_manager.first_name} {case.v2_workers.case_manager.last_name}"
                if hasattr(case, "v2_workers") and case.v2_workers.case_manager
                else None,
                "incident_state": case.incident_details.state if hasattr(case, "incident_details") else None,
                "statute": statute_date.strftime("%m/%d/%Y") if statute_date else None,
                "days_left": days_left if days_left > 0 else f"Date passed {abs(days_left)} days ago",
                "doi": case.incident_details.incident_date.strftime("%m/%d/%Y")
                if hasattr(case, "incident_details") and case.incident_details.incident_date
                else None,
            }

        # First get filtered cases based on view type (my_cases/all_cases)
        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = KPIType.case_open

        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset)

        # Get current date for comparison
        current_date = timezone.now().date()

        # Calculate date thresholds for statute categories
        next_7_days = current_date + timedelta(days=7)
        next_30_days = current_date + timedelta(days=30)
        next_60_days = current_date + timedelta(days=60)
        next_90_days = current_date + timedelta(days=90)

        # Filter cases with statute of limitations dates
        queryset = case_queryset.filter(incident_details__statute_of_limitations__isnull=False)

        # Get min/max days from request params
        status_days_min = int(request.query_params.get("statue_of_limitation_incoming_end_in_days_min", 0))
        status_days_max = int(request.query_params.get("statue_of_limitation_incoming_end_in_days_max", 365))

        # Calculate dates based on days left
        min_date = current_date + timedelta(days=status_days_min)
        if status_days_max == 365:
            max_date = datetime.max
        else:
            max_date = current_date + timedelta(days=status_days_max)

        # Filter cases where statute date falls within the calculated range
        queryset = queryset.filter(
            incident_details__statute_of_limitations__gte=min_date,
            incident_details__statute_of_limitations__lte=max_date,
        )

        next_7_days_count = queryset.filter(incident_details__statute_of_limitations__lte=next_7_days).count()

        next_30_days_count = queryset.filter(
            incident_details__statute_of_limitations__lte=next_30_days,
            incident_details__statute_of_limitations__gt=next_7_days,
        ).count()

        next_60_days_count = queryset.filter(
            incident_details__statute_of_limitations__lte=next_60_days,
            incident_details__statute_of_limitations__gt=next_30_days,
        ).count()

        next_90_days_count = queryset.filter(
            incident_details__statute_of_limitations__lte=next_90_days,
            incident_details__statute_of_limitations__gt=next_60_days,
        ).count()

        summary = {
            "next_7_days": next_7_days_count,
            "next_30_days": next_30_days_count,
            "next_60_days": next_60_days_count,
            "next_90_days": next_90_days_count,
            "total": next_7_days_count + next_30_days_count + next_60_days_count + next_90_days_count,
        }

        # Get filter options
        filter_options = self.get_statute_filter_options(self.get_base_queryset())
        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all statute data for CSV export
            all_statute_data = [format_statute_data(case) for case in queryset]
            return self._get_csv_response("statute_of_limitations.csv", all_statute_data)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            statute_data = []
            for case in page:
                statute_data.append(format_statute_data(case))

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": statute_data}
            )

    def get_statute_filter_options(self, queryset):
        """Get filter options for statute report"""
        organization = self.request.user.organizations.first()
        # Get case type options
        case_types = (
            Case.objects.filter(organization=organization)
            .values_list("incident_details__incident_type", flat=True)
            .distinct()
        )

        case_types = list(set([ct for ct in case_types if ct]))
        case_type_options = [{"value": ct, "label": ct} for ct in case_types]

        # Get case age options
        case_age_options = [
            {"value": "30", "label": "30+ Days"},
            {"value": "60", "label": "60+ Days"},
            {"value": "90", "label": "90+ Days"},
            {"value": "180", "label": "180+ Days"},
            {"value": "365", "label": "365+ Days"},
        ]

        # Get incident state options
        incident_states = (
            Case.objects.filter(organization=organization).values_list("incident_details__state", flat=True).distinct()
        )
        incident_state_options = [{"value": state, "label": state} for state in incident_states if state]

        value_range_filters = {
            "statue_of_limitation_incoming_end_in_days": [
                {
                    "min": 0,
                    "max": 365,
                    "label": "Statue of Limitation Incoming End In Days",
                },
            ],
            # "manual_estimated_value_range": {
            #     "min": value_ranges["manual_estimated_value"]["min"],
            #     "max": value_ranges["manual_estimated_value"]["max"],
            #     "label": "Manual Estimated Value Range",
            # },
        }

        date_fields = [
            {"value": "statute", "label": "Statute Date"},
        ]

        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset),
            "case_ages": case_age_options,
            "incident_states": incident_state_options,
            **self.case_workers_filters_options(queryset),
            "date_fields": date_fields,
            **value_range_filters,
        }

    @action(detail=False, methods=["get"])
    def demands(self, request):
        """Get demand statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        # Get base queryset for third party demands
        third_party_demands = (
            CaseNegotiation.objects.filter(
                defendant__case__in=case_queryset,
                previous_offer__isnull=True,  # Parent demands only
                type="INITIAL_DEMAND",
                is_archived=False,
            )
            .select_related(
                "defendant",
                "defendant__case",
                "defendant__case__client_basic_details",
                "defendant__case__incident_details",
                "defendant__case__v2_workers",
                "defendant__case__v2_workers__lead_attorney",
                "defendant__case__v2_workers__case_manager",
                "adjuster",
            )
            .prefetch_related(
                "defendant__insurances",
                "defendant__insurances__insurance_company",
            )
        )

        # Get base queryset for UIM demands
        uim_demands = CaseNegotiationUIM.objects.filter(
            client_insurance__case__in=case_queryset,
            previous_offer__isnull=True,  # Parent demands only
            type="INITIAL_DEMAND",
            is_archived=False,
        ).select_related(
            "client_insurance",
            "client_insurance__case",
            "client_insurance__case__client_basic_details",
            "client_insurance__case__incident_details",
            "client_insurance__case__v2_workers",
            "client_insurance__case__v2_workers__lead_attorney",
            "client_insurance__case__v2_workers__case_manager",
            "client_insurance__insurance_company",
            "adjuster",
        )

        date_field = self.request.query_params.get("date_field")
        date_from = self.request.query_params.get("from_date")
        date_to = self.request.query_params.get("to_date")

        if date_from and date_to:
            # Create filter kwargs based on the date field
            filter_field = f"{date_field}__" if date_field else "date_sent__"

            if date_from:
                third_party_demands = third_party_demands.filter(**{f"{filter_field}gte": date_from})
                uim_demands = uim_demands.filter(**{f"{filter_field}gte": date_from})
            if date_to:
                third_party_demands = third_party_demands.filter(**{f"{filter_field}lte": date_to})
                uim_demands = uim_demands.filter(**{f"{filter_field}lte": date_to})

        # Calculate summary statistics
        total_third_party = third_party_demands.count()
        total_uim = uim_demands.count()
        total_third_party_amount = third_party_demands.aggregate(Sum("amount"))["amount__sum"] or 0
        total_uim_amount = uim_demands.aggregate(Sum("amount"))["amount__sum"] or 0

        summary = {
            "demands_sent": total_third_party + total_uim,
            "total_demanded": float(total_third_party_amount + total_uim_amount),
            "third_party_demands": total_third_party,
            "um_uim_demands": total_uim,
        }

        def get_latest_offer(demand, is_uim=False):
            """Get latest accepted or counter offer for a demand"""
            Model = CaseNegotiationUIM if is_uim else CaseNegotiation
            filter_kwargs = {
                "client_insurance": demand.client_insurance if is_uim else None,
                "defendant": None if is_uim else demand.defendant,
                "type__in": ["ACCEPTED_OFFER", "COUNTER_OFFER"],
            }
            if is_uim:
                del filter_kwargs["defendant"]
            else:
                del filter_kwargs["client_insurance"]

            return Model.objects.filter(**filter_kwargs).order_by("-created_at").first()

        def format_demand_data(demand, is_uim=False):
            """Format demand data for response"""
            latest_offer = get_latest_offer(demand, is_uim)
            case = demand.client_insurance.case if is_uim else demand.defendant.case

            # Get insurance info
            if is_uim:
                insurance_company = demand.client_insurance.insurance_company
                insurance_obj = demand.client_insurance
            else:
                insurance_obj = demand.defendant.insurances.first()
                insurance_company = insurance_obj.insurance_company if insurance_obj else None

            # Get treatment costs
            treatment_costs = self._get_treatment_costs(case)

            is_accepted = latest_offer and latest_offer.type == "ACCEPTED_OFFER"
            if latest_offer:
                is_expired = False
            else:
                is_expired = (
                    demand.response_deadline and demand.response_deadline < timezone.now().date() and not is_accepted
                )

            summary["expired"] = summary.get("expired", 0) + 1 if is_expired else summary.get("expired", 0)

            return {
                "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}",
                "date_sent": demand.date_sent.strftime("%m/%d/%Y") if demand.date_sent else None,
                "date_expires": demand.response_deadline.strftime("%m/%d/%Y") if demand.response_deadline else None,
                "type": "UIM" if is_uim else "Third Party",
                "insurance": insurance_company.name if insurance_company else None,
                "policy_limits": insurance_obj.um_uim
                if is_uim
                else (insurance_obj.policy_limits if insurance_obj else None),
                "total_meds": float(treatment_costs.get("total_meds", 0)),
                "costs": float(treatment_costs.get("costs", 0)),
                "amount": float(demand.amount or 0),
                "offer": float(latest_offer.amount) if latest_offer else 0,
                "offer_status": latest_offer.get_type_display() if latest_offer else "No Offers",
                "expired": is_expired if not is_accepted else "Accepted Offer",
                "demand_note": demand.notes,
                "doi": case.incident_details.incident_date.strftime("%m/%d/%Y")
                if case.incident_details.incident_date
                else None,
                "team": self._get_team_data(case.v2_workers) if case.v2_workers else [],
            }

        # Combine and format all demands
        all_demands = []
        for demand in third_party_demands:
            all_demands.append((demand.created_at, format_demand_data(demand, False)))
        for demand in uim_demands:
            all_demands.append((demand.created_at, format_demand_data(demand, True)))

        # Sort by date
        all_demands.sort(key=lambda x: x[0], reverse=True)
        formatted_demands = [demand[1] for demand in all_demands]

        # Get filter options
        filter_options = self.get_demands_filter_options(self.get_base_queryset())

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Prepare CSV data
            return self._get_csv_response("demands.csv", formatted_demands)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        page = int(request.query_params.get("page", 1))
        start = (page - 1) * page_size
        end = start + page_size

        # Create paginated subset
        paginated_demands = formatted_demands[start:end]

        return Response(
            {
                "count": len(formatted_demands),
                "next": f"?page={page + 1}" if end < len(formatted_demands) else None,
                "previous": f"?page={page - 1}" if page > 1 else None,
                "total_pages": (len(formatted_demands) + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size,
                "results": paginated_demands,
                "summary": summary,
                "filter_options": filter_options,
            }
        )

    def get_demands_filter_options(self, queryset):
        """Get filter options for demands report"""
        base_queryset = self.get_base_queryset()
        case_types = base_queryset.values_list("incident_details__incident_type", flat=True).distinct()
        case_types = list(set([ct for ct in case_types if ct]))
        case_type_options = [{"value": ct, "label": ct} for ct in case_types]

        # Get demand type options
        demand_type_options = [
            {"value": "THIRD_PARTY", "label": "Third Party"},
            {"value": "UM", "label": "UM"},
            {"value": "UIM", "label": "UIM"},
        ]

        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset=base_queryset),
            "demand_types": demand_type_options,
            **self.case_workers_filters_options(base_queryset),
            "role_options": self.get_case_worker_role_filter_options(base_queryset),
            "date_fields": [
                {"value": "date_sent", "label": "Sent Date"},
            ],
        }

    def _get_demand_type_display(self, demand_type):
        """Get display value for demand type"""
        type_mapping = {"THIRD_PARTY": "Third Party", "LIABILITY": "Third Party", "UM": "UM", "UIM": "UIM"}
        return type_mapping.get(demand_type, demand_type)

    def _get_treatment_costs(self, case):
        """Get treatment costs for a case"""
        try:
            # Get all treatment providers for the case
            treatments = TreatmentProvider.objects.filter(case=case)

            # Get all case costs for the case
            case_costs = CaseCost.objects.filter(
                case=case,
                is_void=False,  # Exclude voided costs
                status__in=["APPROVED", "PAID"],  # Only include approved or paid costs
            )

            # Calculate total original bills from treatments
            total_meds = sum(t.original_bill or 0 for t in treatments)

            # Calculate total costs from case costs
            costs = sum(c.amount for c in case_costs)

            return {"total_meds": total_meds, "costs": costs}
        except:
            return {"total_meds": 0, "costs": 0}

    def _get_client_insurance(self, case):
        """Get client's insurance with MedPay coverage"""
        try:
            return ClientInsurance.objects.filter(case=case, insurance_type__in=["AUTO", "HEALTH", "UMBRELLA"]).first()
        except:
            return None

    def _get_medpay_limits(self, insurance):
        """Get MedPay limits from client insurance"""
        if not insurance:
            return None

        # Check for MedPay coverage in policy details
        try:
            if hasattr(insurance, "policy_details") and insurance.policy_details:
                if "medpay_limit" in insurance.policy_details:
                    return insurance.policy_details.get("medpay_limit")
                elif "pip_limit" in insurance.policy_details:
                    return insurance.policy_details.get("pip_limit")
            return insurance.medpay
        except:
            return None

    def _get_case_manager_name(self, case):
        """Get case manager's name"""
        try:
            if hasattr(case, "v2_workers") and case.v2_workers.case_manager:
                cm = case.v2_workers.case_manager
                return f"{cm.first_name} {cm.last_name}"
            return None
        except:
            return None

    def _get_lead_attorney_name(self, case):
        """Get lead attorney's name"""
        try:
            if hasattr(case, "v2_workers") and case.v2_workers.lead_attorney:
                la = case.v2_workers.lead_attorney
                return f"{la.first_name} {la.last_name}"
            return None
        except:
            return None

    def get_settlement_filter_options(self, queryset, case_queryset):
        """Get filter options for settlement report"""
        organization = self.request.user.organizations.first()

        # Get case type options
        case_types = case_queryset.values_list("incident_details__incident_type", flat=True).distinct()
        case_types = list(set([ct for ct in case_types if ct]))
        case_type_options = [{"value": ct, "label": ct} for ct in case_types]

        # Get source detail options
        source_details = (
            Case.objects.filter(organization=organization)
            .values_list("client_basic_details__source_detail", flat=True)
            .distinct()
        )
        sources = list(set([sd for sd in source_details if sd]))
        source_detail_options = [{"value": sd, "label": sd} for sd in sources]

        # Get source type options
        source_types = (
            Case.objects.filter(organization=organization)
            .values_list("client_basic_details__source_type", flat=True)
            .distinct()
        )
        sources_types = list(set([sd for sd in source_types if sd]))
        source_types_options = [{"value": sd, "label": sd} for sd in sources_types]

        date_fields = [
            {"value": "accepted_date", "label": "Accepted Date"},
        ]

        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset=self.get_base_queryset()),
            "source_details": source_detail_options,  # Add source details
            "source_types": source_types_options,  # Add source types
            **self.case_workers_filters_options(case_queryset),
            "role_options": self.get_case_worker_role_filter_options(case_queryset),
            "date_fields": date_fields,
        }

    @action(detail=False, methods=["get"])
    def settlement(self, request):
        """Get settlement statistics and details"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        self.request._request.GET = self.request._request.GET.copy()
        self.request._request.GET["kpi_type"] = KPIType.case_open

        # Only select cases that have any offers in negotiations or negotiations_uim
        case_queryset = case_queryset.filter(
            Q(v2_defendants__negotiations__isnull=False) | Q(insurances__negotiations_uim__isnull=False)
        )

        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        date_field = request.query_params.get("date_field")
        date_from = request.query_params.get("from_date")
        date_to = request.query_params.get("to_date")
        # Get case IDs for further processing
        case_ids = case_queryset.values_list("id", flat=True)

        # Get all defendants with negotiations for these cases

        defendants = (
            CaseDefendant.objects.filter(
                case_id__in=case_ids,
                negotiations__isnull=False,
            )
            .filter(
                Q(negotiations__type="ACCEPTED_OFFER", negotiations__amount__gt=0)
                | ~Q(negotiations__type="ACCEPTED_OFFER")
            )
            .distinct()
            .select_related(
                "case",
                "case__client_basic_details",
                "case__incident_details",
                "case__organization_status",
                "case__v2_workers",
                "case__v2_workers__lead_attorney",
                "case__v2_workers__case_manager",
            )
        )

        # Get all client insurances with UIM negotiations for these cases
        client_insurances = (
            ClientInsurance.objects.filter(
                case_id__in=case_ids,
                negotiations_uim__isnull=False,
            )
            .filter(
                Q(negotiations_uim__type="ACCEPTED_OFFER", negotiations_uim__amount__gt=0)
                | ~Q(negotiations_uim__type="ACCEPTED_OFFER")
            )
            .distinct()
            .select_related(
                "case",
                "case__client_basic_details",
                "case__incident_details",
                "case__organization_status",
                "case__v2_workers",
                "case__v2_workers__lead_attorney",
                "case__v2_workers__case_manager",
            )
        )

        # Create consolidated data for each defendant and client insurance
        defendant_data = []
        for defendant in defendants:
            # Get all negotiations for this defendant
            negotiations = CaseNegotiation.objects.filter(defendant=defendant)

            # Get the count of offers
            offer_count = negotiations.count()

            if not negotiations.filter(type="ACCEPTED_OFFER", amount__gt=0).exists():
                continue

            # Get the initial demand amount
            initial_demand = negotiations.filter(type="INITIAL_DEMAND").order_by("created_at").first()
            initial_amount = initial_demand.amount if initial_demand else 0

            # Get the accepted offer amount (if any)
            accepted_offer = negotiations.filter(type="ACCEPTED_OFFER").order_by("-created_at").first()
            accepted_amount = accepted_offer.amount if accepted_offer else 0

            # Filter date from and date to of accepted offer negoation if its odesn lie between range of this date, continue
            if (date_from and date_to) and (
                accepted_offer.accepted_date < datetime.strptime(date_from, "%Y-%m-%d").date()
                or accepted_offer.accepted_date > datetime.strptime(date_to, "%Y-%m-%d").date()
            ):
                continue

            # Determine offer status
            offer_status = "Received"
            if accepted_offer:
                offer_status = "Accepted"
                # Check if there's a settlement check deposit
                has_check = ClientTrust.objects.filter(
                    case=defendant.case, client_trust_entry_type="SETTLEMENT_CHECK"
                ).exists()
                if has_check:
                    offer_status = "Deposited"

            # Get deposit date if available
            deposit_date = "N/A"
            if offer_status == "Deposited":
                settlement_check = (
                    ClientTrust.objects.filter(case=defendant.case, client_trust_entry_type="SETTLEMENT_CHECK")
                    .order_by("-deposit_date")
                    .first()
                )
                if settlement_check and settlement_check.deposit_date:
                    deposit_date = settlement_check.deposit_date.strftime("%m/%d/%Y")

            # Calculate fees
            case = defendant.case
            settlement_calc = CaseSettlementCalculation.objects.filter(case=case).first()
            if settlement_calc and settlement_calc.fees_percentage:
                fee_percentage = settlement_calc.fees_percentage / 100
            else:
                fee_percentage = Decimal("0.333")

            fees = accepted_amount * fee_percentage if accepted_amount else 0

            # Add to defendant data list

            if deposit_date:
                offer_status = "Deposited"
            elif accepted_offer:
                offer_status = "Accepted"
            else:
                offer_status = "Received"

            defendant_data.append(
                {
                    "case": case,
                    "defendant": defendant,
                    "negotiations": negotiations,
                    "offer_count": offer_count,
                    "initial_amount": initial_amount,
                    "accepted_amount": accepted_amount,
                    "offer_status": offer_status,
                    "deposit_date": deposit_date,
                    "fees": fees,
                    "negotiation_type": "Third Party",
                    "accepted_date": accepted_offer.accepted_date if accepted_offer else None,
                    "accepted_by": accepted_offer.accepted_by.first_name + " " + accepted_offer.accepted_by.last_name
                    if accepted_offer and accepted_offer.accepted_by
                    else None,
                }
            )

        # Create consolidated data for each client insurance
        client_insurance_data = []
        for client_insurance in client_insurances:
            # Get all negotiations for this client insurance
            negotiations = CaseNegotiationUIM.objects.filter(client_insurance=client_insurance)

            if not negotiations.filter(type="ACCEPTED_OFFER", amount__gt=0).exists():
                continue

            # Get the count of offers
            offer_count = negotiations.count()

            # Get the initial demand amount
            initial_demand = negotiations.filter(type="INITIAL_DEMAND").order_by("created_at").first()
            initial_amount = initial_demand.amount if initial_demand else 0

            # Get the accepted offer amount (if any)
            accepted_offer = negotiations.filter(type="ACCEPTED_OFFER").order_by("-created_at").first()
            accepted_amount = accepted_offer.amount if accepted_offer else 0

            if (date_from and date_to) and (
                accepted_offer.accepted_date < datetime.strptime(date_from, "%Y-%m-%d").date()
                or accepted_offer.accepted_date > datetime.strptime(date_to, "%Y-%m-%d").date()
            ):
                continue

            # Determine offer status
            offer_status = "Received"
            if accepted_offer:
                offer_status = "Accepted"
                # Check if there's a settlement check deposit
                has_check = ClientTrust.objects.filter(
                    case=client_insurance.case, client_trust_entry_type="SETTLEMENT_CHECK"
                ).exists()
                if has_check:
                    offer_status = "Deposited"

            # Get deposit date if available
            deposit_date = "N/A"
            if offer_status == "Deposited":
                settlement_check = (
                    ClientTrust.objects.filter(case=client_insurance.case, client_trust_entry_type="SETTLEMENT_CHECK")
                    .order_by("-deposit_date")
                    .first()
                )
                if settlement_check and settlement_check.deposit_date:
                    deposit_date = settlement_check.deposit_date.strftime("%m/%d/%Y")

            # Calculate fees
            case = client_insurance.case
            settlement_calc = CaseSettlementCalculation.objects.filter(case=case).first()
            if settlement_calc and settlement_calc.fees_percentage:
                fee_percentage = settlement_calc.fees_percentage / 100
            else:
                fee_percentage = Decimal("0.333")

            fees = accepted_amount * fee_percentage if accepted_amount else 0

            if deposit_date:
                offer_status = "Deposited"
            elif accepted_offer:
                offer_status = "Accepted"
            else:
                offer_status = "Received"

            # Add to client insurance data list
            client_insurance_data.append(
                {
                    "case": case,
                    "client_insurance": client_insurance,
                    "negotiations": negotiations,
                    "offer_count": offer_count,
                    "initial_amount": initial_amount,
                    "accepted_amount": accepted_amount,
                    "offer_status": offer_status,
                    "deposit_date": deposit_date,
                    "fees": fees,
                    "negotiation_type": "UM / UIM",
                    "accepted_date": accepted_offer.accepted_date if accepted_offer else None,
                    "accepted_by": accepted_offer.accepted_by.first_name + " " + accepted_offer.accepted_by.last_name
                    if accepted_offer and accepted_offer.accepted_by
                    else None,
                }
            )

        # Combine both data sets
        combined_data = defendant_data + client_insurance_data

        # Calculate summary statistics
        total_settlements = sum(item["accepted_amount"] for item in combined_data)
        total_fees = sum(item["fees"] for item in combined_data)
        total_offers = len(combined_data)
        avg_per_case = total_settlements / total_offers if total_offers > 0 else 0

        # Get source type breakdown
        source_type_breakdown = {}
        for item in combined_data:
            case = item["case"]
            if not hasattr(case, "client_basic_details") or not case.client_basic_details:
                continue

            source_type = case.client_basic_details.source_type or "Unknown"

            if source_type not in source_type_breakdown:
                source_type_breakdown[source_type] = {
                    "count": 0,
                    "total": 0,
                    "fees": 0,
                }

            source_type_breakdown[source_type]["count"] += 1
            source_type_breakdown[source_type]["total"] += float(item["accepted_amount"])
            source_type_breakdown[source_type]["fees"] += float(item["fees"])

        # Get medical bills, liens, advanced costs, and settlement advances from settlement calculations
        settlement_calcs = CaseSettlementCalculation.objects.filter(case_id__in=case_ids)

        settlement_stats = settlement_calcs.aggregate(
            total_medical_bills=Sum("medical_bills"),
            total_liens=Sum(F("health_insurance_liens") + F("attorney_liens") + F("miscellaneous_liens")),
            total_advanced_costs=Sum("advanced_costs"),
            total_settlement_advances=Sum("settlement_advance_loan"),
        )

        summary = {
            "total": float(total_settlements),
            "offers": total_offers,
            "avg_per_case": float(avg_per_case),
            "total_fees": float(total_fees),
            "total_medical_bills": float(settlement_stats.get("total_medical_bills") or 0),
            "total_liens": float(settlement_stats.get("total_liens") or 0),
            "total_advanced_costs": float(settlement_stats.get("total_advanced_costs") or 0),
            "total_settlement_advances": float(settlement_stats.get("total_settlement_advances") or 0),
        }

        # Get filter options
        filter_options = self.get_settlement_filter_options(None, self.get_base_queryset())

        # Check if CSV export is requested
        if self._should_export_csv(request):
            # Format all settlements for CSV export
            all_settlement_data = []

            for item in combined_data:
                case = item["case"]

                # Get source information
                source_type = "N/A"
                source_detail = "N/A"
                if hasattr(case, "client_basic_details") and case.client_basic_details:
                    source_type = case.client_basic_details.source_type or "N/A"
                    source_detail = case.client_basic_details.source_detail or "N/A"

                # Format incident date
                incident_date = "N/A"
                if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
                    incident_date = case.incident_details.incident_date.strftime("%m/%d/%Y")

                # Format the negotiation data for CSV
                all_settlement_data.append(
                    {
                        "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {incident_date} : {case.id}",
                        "case_type": case.incident_details.incident_type
                        if hasattr(case, "incident_details") and case.incident_details
                        else "N/A",
                        "case_stage": case.organization_status.display_name
                        if hasattr(case, "organization_status") and case.organization_status
                        else "N/A",
                        "case_status": case.get_status_display(),
                        "offer_count": item["offer_count"],
                        "accepted_date": item["accepted_date"].strftime("%m/%d/%Y") if item["accepted_date"] else "N/A",
                        "deposit_date": item["deposit_date"],
                        "offer_status": item["offer_status"],
                        "fees": float(item["fees"] or 0),
                        "accepted_offer": float(item["accepted_amount"] or 0),
                        "initial_offer": float(item["initial_amount"] or 0),
                        "negotiation_type": item["negotiation_type"],
                        "time": self._calculate_case_duration(case),
                        "source_type": source_type,
                        "source_detail": source_detail,
                        **generate_settlement_report_data(case),
                    }
                )

            # Prepare CSV data
            headers = [
                "Case",
                "Case Type",
                "Case Stage",
                "Case Status",
                "Offer Count",
                "Accepted Date",
                "Deposit Date",
                "Offer Status",
                "Fees",
                "Accepted Offer",
                "Initial Offer",
                "Negotiation Type",
                "Time (months)",
                "Source Type",
                "Source Detail",
            ]

            csv_data = []
            for item in all_settlement_data:
                csv_data.append(
                    [
                        item.get("case", ""),
                        item.get("case_type", ""),
                        item.get("case_stage", ""),
                        item.get("case_status", ""),
                        item.get("offer_count", ""),
                        item.get("accepted_date", ""),
                        item.get("deposit_date", ""),
                        item.get("offer_status", ""),
                        item.get("fees", ""),
                        item.get("accepted_offer", ""),
                        item.get("initial_offer", ""),
                        item.get("negotiation_type", ""),
                        item.get("time", ""),
                        item.get("source_type", ""),
                        item.get("source_detail", ""),
                    ]
                )

            return self._get_csv_response("settlements.csv", csv_data, headers)

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Paginate the combined data
        if not self._should_export_csv(request):
            page = paginator.paginate_queryset(combined_data, request)
        else:
            page = combined_data

        if page is not None:
            settlement_data = []
            for item in page:
                case = item["case"]

                # Get source information
                source_type = "N/A"
                source_detail = "N/A"
                if hasattr(case, "client_basic_details") and case.client_basic_details:
                    source_type = case.client_basic_details.source_type or "N/A"
                    source_detail = case.client_basic_details.source_detail or "N/A"

                # Format incident date for display
                incident_date = "N/A"
                if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
                    incident_date = case.incident_details.incident_date.strftime("%m/%d/%Y")

                # Format the item for display

                if item["deposit_date"] and str(item["deposit_date"]) != "N/A" and len(str(item["deposit_date"])) > 6:
                    offer_status = "Deposited"
                elif (
                    item["accepted_date"]
                    and str(item["accepted_date"]) != "N/A"
                    and len(str(item["accepted_date"])) > 6
                ):
                    offer_status = "Accepted"
                else:
                    offer_status = "Received"

                settlement_data.append(
                    {
                        "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {incident_date} : {case.id}",
                        "case_status": case.organization_status.display_name
                        if hasattr(case, "organization_status") and case.organization_status
                        else "N/A",
                        "offer_count": item["offer_count"],
                        "accepted_date": item["accepted_date"].strftime("%m/%d/%Y") if item["accepted_date"] else "N/A",
                        "deposit_date": item["deposit_date"],
                        "fees": float(item["fees"] or 0),
                        "accepted_offer": float(item["accepted_amount"] or 0),
                        "initial_offer": float(item["initial_amount"] or 0),
                        "negotiation_type": item["negotiation_type"],
                        "case_duration (days)": self._calculate_case_duration(case),
                        "source_detail": source_detail,
                        **generate_settlement_report_data(case),
                    }
                )

            if self._should_export_csv(request):
                return self._get_csv_response("settlements.csv", settlement_data)

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": settlement_data}
            )

    def _get_initial_offer_for_negotiation(self, negotiation, negotiation_type):
        """Get the initial offer amount for a specific negotiation"""
        try:
            if negotiation_type == "Third Party":
                # Get the initial demand for this defendant
                defendant = negotiation.defendant
                initial_offer = (
                    CaseNegotiation.objects.filter(defendant=defendant, type="INITIAL_DEMAND")
                    .order_by("created_at")
                    .first()
                )

                if initial_offer:
                    return initial_offer.amount
            else:
                # Get the initial demand for this client insurance (UIM)
                client_insurance = negotiation.client_insurance
                initial_offer = (
                    CaseNegotiationUIM.objects.filter(client_insurance=client_insurance, type="INITIAL_DEMAND")
                    .order_by("created_at")
                    .first()
                )

                if initial_offer:
                    return initial_offer.amount

            return 0
        except:
            return 0

    def _get_initial_offer_amount(self, case):
        """Get the initial offer amount for a case"""
        try:
            # Get all defendants for this case
            defendants = case.defendants.all()

            # Get the first initial offer for any defendant
            for defendant in defendants:
                initial_offer = (
                    CaseNegotiation.objects.filter(defendant=defendant, type="INITIAL_DEMAND")
                    .order_by("created_at")
                    .first()
                )

                if initial_offer:
                    return initial_offer.amount

            return 0
        except:
            return 0

    def _calculate_case_duration(self, case):
        """Calculate the duration of a case in months"""
        try:
            if case.created_at:
                # Calculate months between case creation and now
                today = timezone.now().date()
                case_start = case.created_at.date()

                # Calculate difference in months
                months = (today.year - case_start.year) * 12 + (today.month - case_start.month)
                return months
            return 0
        except:
            return 0

    @action(detail=False, methods=["get"])
    def client_trust(self, request, skip_few_filters=False):
        """Get client trust report with entries and summary statistics"""
        # Get base queryset of cases the user has access to
        case_queryset = self.get_base_queryset()
        # only select cases which has any clinet turst entry
        # case_queryset = case_queryset.filter(client_trust_entries__isnull=False)

        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        tag = request.query_params.get("role")
        if tag:
            case_queryset = self.case_queryset_filter_user_tag(case_queryset, tag)

        # Get all client trust entries for the filtered cases
        trust_entries = ClientTrust.objects.filter(case__in=case_queryset).exclude(
            client_trust_entry_type=ClientTrustEntryType.REFERRED_FEE
        )

        # Apply filters
        # Deposit type filter
        show_attorney_fees = request.query_params.get("show_attorney_fees", None)
        if show_attorney_fees:
            if show_attorney_fees == "true":
                trust_entries = trust_entries.filter(
                    client_trust_entry_type__in=[
                        ClientTrustEntryType.ATTORNEY_CHECK,
                    ]
                )

        is_deposit = request.query_params.get("is_deposit")
        if is_deposit is not None:
            is_deposit_bool = is_deposit.lower() == "true"
            if is_deposit_bool:
                trust_entries = trust_entries.filter(is_deposit=is_deposit_bool)

        issued_payments = request.query_params.get("issued_payments")
        if issued_payments:
            if issued_payments == "true":
                trust_entries = trust_entries.filter(is_deposit=False)

        only_check_deposits = request.query_params.get("only_check_deposits")
        if only_check_deposits:
            if only_check_deposits.lower() == "true":
                trust_entries = trust_entries.filter(
                    client_trust_entry_type__in=[
                        ClientTrustEntryType.SETTLEMENT_CHECK,
                        ClientTrustEntryType.MEDPAY_DEPOSIT,
                        ClientTrustEntryType.REFERRED_FEE,
                    ]
                )

        client_trust_entry_type = request.query_params.get("client_trust_entry_type")
        if client_trust_entry_type:
            trust_entries = trust_entries.filter(client_trust_entry_type=client_trust_entry_type)

        # Date range filter YYYY-MM-DD
        date_from = request.query_params.get("from_date")
        view_logger.info(f"from_date: {date_from}")
        if date_from:
            try:
                date_from = parse_date(date_from)
                trust_entries = trust_entries.filter(deposit_date__gte=date_from)
                view_logger.info(f"trust_entries count: {trust_entries.count()}")
            except ValueError:
                pass

        date_to = request.query_params.get("to_date")
        view_logger.info(f"to_date: {date_to}")
        if date_to:
            try:
                date_to = parse_date(date_to)
                trust_entries = trust_entries.filter(deposit_date__lte=date_to)
                view_logger.info(f"trust_entries count: {trust_entries.count()}")
            except ValueError:
                pass

        # Amount range filter
        amount_min = request.query_params.get("amount_min")
        if amount_min:
            try:
                amount_min = float(amount_min)
                trust_entries = trust_entries.filter(amount__gte=amount_min)
            except ValueError:
                pass

        amount_max = request.query_params.get("amount_max")
        if amount_max:
            try:
                amount_max = float(amount_max)
                trust_entries = trust_entries.filter(amount__lte=amount_max)
            except ValueError:
                pass

        drop_columns = request.query_params.get("drop_columns")
        drop_columns = drop_columns.split(",") if drop_columns else []

        rename_columns = request.query_params.get("rename_columns")
        rename_columns = rename_columns.split(",") if rename_columns else []
        rename_columns = {k: v for k, v in (item.split(":") for item in rename_columns)}

        # Calculate summary information for trust entries
        total_deposits = trust_entries.filter(is_deposit=True).aggregate(total=models.Sum("amount"))[
            "total"
        ] or Decimal("0")
        total_debits = abs(
            trust_entries.filter(is_deposit=False).aggregate(total=models.Sum("amount"))["total"] or Decimal("0")
        )

        # Get the current balance based on all entries, not just filtered ones
        all_entries = ClientTrust.objects.filter(case__in=case_queryset)
        current_balance = Decimal("0")
        if all_entries.exists():
            # Get the most recent entry for each case and sum their balances
            latest_entries = {}
            for entry in all_entries.order_by("case_id", "-deposit_date", "-created_at"):
                if entry.case_id not in latest_entries:
                    latest_entries[entry.case_id] = entry.balance or Decimal("0")

            current_balance = sum(latest_entries.values())

        # Order the entries
        ordering = request.query_params.get("ordering", "-deposit_date")
        if ordering:
            trust_entries = trust_entries.order_by(ordering)
        else:
            trust_entries = trust_entries.order_by("-deposit_date", "-created_at")

        # Use select_related to optimize the query
        trust_entries = trust_entries.select_related(
            "case", "case__client_basic_details", "case__organization_status", "case__incident_details"
        )

        def format_trust_entry(entry, is_case_cost=False):
            if is_case_cost:
                client_name = "Unknown"
                if hasattr(entry.case, "client_basic_details") and entry.case.client_basic_details:
                    basic_details = entry.case.client_basic_details
                    client_name = f"{basic_details.first_name} {basic_details.last_name}"

                case_name = f"{entry.case.client_basic_details.first_name} {entry.case.client_basic_details.last_name} - {entry.case.incident_details.incident_date.strftime('%m/%d/%Y')} : {entry.case.id}"

                return {
                    "case": case_name,
                    "organization_status": entry.case.organization_status.display_name
                    if entry.case.organization_status
                    else None,
                    "is_deposit": False,
                    "client_trust_entry_type": "CASE_COST",
                    "deposit_date": entry.paid_date.strftime("%m/%d/%Y") if entry.paid_date else None,
                    "issuer_payee": entry.contact.company_name if entry.contact else "Unknown",
                    "memo": entry.memo,
                    "check_number": entry.check_number,
                    "amount": float(-entry.amount) if entry.amount else 0,  # Negative since it's a payment
                    "balance": None,  # Case costs don't affect trust balance
                    "created_at": entry.created_at.strftime("%m/%d/%Y %H:%M") if entry.created_at else None,
                }
            else:
                client_name = "Unknown"
                if hasattr(entry.case, "client_basic_details") and entry.case.client_basic_details:
                    basic_details = entry.case.client_basic_details
                    client_name = f"{basic_details.first_name} {basic_details.last_name}"

                case_name = f"{entry.case.client_basic_details.first_name} {entry.case.client_basic_details.last_name} - {entry.case.incident_details.incident_date.strftime('%m/%d/%Y')} : {entry.case.id}"

                return {
                    "case": case_name,
                    "organization_status": entry.case.organization_status.display_name
                    if entry.case.organization_status
                    else None,
                    "is_deposit": entry.is_deposit,
                    "client_trust_entry_type": entry.client_trust_entry_type,
                    "deposit_date": entry.deposit_date.strftime("%m/%d/%Y") if entry.deposit_date else None,
                    "issuer_payee": entry.issuer_payee,
                    "created_by": f"{entry.created_by.first_name} {entry.created_by.last_name}"
                    if entry.created_by
                    else None,
                    "memo": entry.memo,
                    "check_number": entry.check_number,
                    "amount": float(entry.amount) if entry.amount else 0,
                    "balance": float(entry.balance) if entry.balance else 0,
                    "created_at": entry.created_at.strftime("%m/%d/%Y %H:%M") if entry.created_at else None,
                }

        # Prepare summary data
        summary = {
            "total_deposits": float(total_deposits),
            "total_debits": float(total_debits),
            "current_balance": float(current_balance),
            "total_entries": trust_entries.count(),
            "trust_count": trust_entries.values("case").distinct().count(),
        }

        # Get filter options
        filter_options = self._get_client_trust_filter_options(
            self.get_base_queryset(), skip_few_filters, only_check_deposits
        )

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Combine and sort entries
        all_entries = []
        for entry in trust_entries:
            entry_data = format_trust_entry(entry)
            if drop_columns:
                entry_data = {k: v for k, v in entry_data.items() if k not in drop_columns}
            if rename_columns:
                entry_data = {rename_columns.get(k, k): v for k, v in entry_data.items()}
            all_entries.append((entry.deposit_date, entry.created_at, entry_data))

        # Sort combined entries by date
        all_entries.sort(key=lambda x: (x[0], x[1]), reverse=True)
        formatted_entries = [entry[2] for entry in all_entries]

        # Check if CSV export is requested
        if self._should_export_csv(request):
            return self._get_csv_response("client_trust_report.csv", formatted_entries)

        # Calculate pagination
        page = int(request.query_params.get("page", 1))
        start = (page - 1) * page_size
        end = start + page_size

        # Create paginated subset
        paginated_entries = formatted_entries[start:end]

        # Create response with proper pagination metadata
        return Response(
            {
                "count": len(formatted_entries),
                "next": f"?page={page + 1}" if end < len(formatted_entries) else None,
                "previous": f"?page={page - 1}" if page > 1 else None,
                "total_pages": (len(formatted_entries) + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size,
                "results": paginated_entries,
                "summary": summary,
                "filter_options": filter_options,
            }
        )

    @action(detail=False, methods=["get"])
    def client_trust_attorney_fees(self, request):
        """Get client trust attorney fees"""
        request._request.GET = request._request.GET.copy()
        request._request.GET["drop_columns"] = "is_deposit,client_trust_entry_type,issuer_payee"
        request._request.GET["show_attorney_fees"] = "true"
        return self.client_trust(request, skip_few_filters=True)

    @action(detail=False, methods=["get"])
    def client_trust_issued_payments(self, request):
        """Get client trust issued payments"""
        request._request.GET = request._request.GET.copy()
        request._request.GET["issued_payments"] = "true"
        request._request.GET["drop_columns"] = "balance,is_deposit"
        request._request.GET["rename_columns"] = "deposit_date:issued_date"
        return self.client_trust(request, skip_few_filters=True)

    @action(detail=False, methods=["get"])
    def client_trust_check_deposits(self, request):
        """Get client trust check deposits"""
        request._request.GET = request._request.GET.copy()
        request._request.GET["drop_columns"] = "is_deposit,memo"
        request._request.GET["only_check_deposits"] = "true"
        return self.client_trust(request, skip_few_filters=True)

    @action(detail=False, methods=["get"])
    def client_trust_consolidated(self, request):
        """Get consolidated client trust report grouped by case with summary statistics"""
        # Get base queryset of cases the user has access to
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        # filter cases which have client trust entries
        case_queryset = case_queryset.filter(client_trust_entries__isnull=False).distinct()

        all_case_ids = [case.id for case in case_queryset]  # for overall summary

        # Handle pagination
        page_size = int(request.query_params.get("page_size", 25))
        paginator = self.paginator
        paginator.page_size = page_size
        all_entry_count = case_queryset.count()

        page = int(request.query_params.get("page", 1))
        start = (page - 1) * page_size
        end = start + page_size
        paginated_case_queryset = (
            self.paginator.paginate_queryset(case_queryset, request)
            if not self._should_export_csv(request)
            else case_queryset
        )
        case_ids = [case.id for case in paginated_case_queryset]

        # Use aggregation to calculate summary per case with a single query
        case_summaries_query = (
            ClientTrust.objects.filter(case_id__in=case_ids)
            .values("case_id")
            .annotate(
                total_deposits=models.Sum(
                    models.Case(
                        models.When(is_deposit=True, then="amount"), default=0, output_field=models.DecimalField()
                    )
                ),
                total_debits=models.Sum(
                    models.Case(
                        models.When(is_deposit=False, then=models.Func(models.F("amount"), function="ABS")),
                        default=0,
                        output_field=models.DecimalField(),
                    )
                ),
            )
            .annotate(balance=models.F("total_deposits") - models.F("total_debits"))
        )

        # Calculate overall summary with a single query
        overall_totals = ClientTrust.objects.filter(case_id__in=all_case_ids).aggregate(
            total_deposits=models.Sum(
                models.Case(models.When(is_deposit=True, then="amount"), default=0, output_field=models.DecimalField())
            ),
            total_debits=models.Sum(
                models.Case(
                    models.When(is_deposit=False, then=models.Func(models.F("amount"), function="ABS")),
                    default=0,
                    output_field=models.DecimalField(),
                )
            ),
        )

        total_deposits = overall_totals["total_deposits"] or Decimal("0")
        total_debits = overall_totals["total_debits"] or Decimal("0")
        current_balance = total_deposits - total_debits

        # Create a dictionary of case summaries keyed by case_id
        case_summaries_dict = {item["case_id"]: item for item in case_summaries_query}

        # Fetch detailed case information in a single query to avoid N+1 problems
        case_details = Case.objects.filter(id__in=case_ids).select_related(
            "client_basic_details", "organization_status", "incident_details"
        )

        # Prepare formatted summaries
        formatted_summaries = []
        for case in case_details:
            case_id = case.id
            summary = case_summaries_dict.get(
                case_id, {"total_deposits": Decimal("0"), "total_debits": Decimal("0"), "balance": Decimal("0")}
            )

            client_name = ""
            case_display = ""
            organization_status = ""

            if hasattr(case, "client_basic_details") and case.client_basic_details:
                basic_details = case.client_basic_details
                client_name = f"{basic_details.first_name} {basic_details.last_name}"

                incident_date = (
                    case.incident_details.incident_date.strftime("%m/%d/%Y")
                    if hasattr(case, "incident_details")
                    and case.incident_details
                    and case.incident_details.incident_date
                    else "Unknown"
                )
                case_display = f"{client_name} - {incident_date} : {case_id}"

            if hasattr(case, "organization_status") and case.organization_status:
                organization_status = case.organization_status.display_name

            formatted_summaries.append(
                {
                    "case": case_display,
                    "organization_status": organization_status,
                    "total_deposits": float(summary.get("total_deposits") or 0),
                    "total_debits": float(summary.get("total_debits") or 0),
                    "balance": float(summary.get("balance") or 0),
                }
            )

        # Order by balance or other specified criteria
        ordering = request.query_params.get("ordering", "-balance")
        reverse_order = ordering.startswith("-")
        sort_key = ordering.lstrip("-")

        formatted_summaries.sort(key=lambda x: (x.get(sort_key) or 0) if not reverse_order else -(x.get(sort_key) or 0))

        # Prepare overall summary data
        summary = {
            "total_deposits": float(total_deposits),
            "total_debits": float(total_debits),
            "current_balance": float(current_balance),
            "case_count": all_entry_count,
        }

        # Get filter options
        filter_options = {
            "date_fields": [{"value": "created_at", "label": "Created Date"}],
            **self.get_case_fix_status_filter_options(),
        }

        # Check if CSV export is requested
        if self._should_export_csv(request):
            return self._get_csv_response(
                "client_trust_consolidated_report.csv",
                formatted_summaries,
            )

        # Create response with proper pagination metadata
        return Response(
            {
                "count": all_entry_count,
                "next": f"?page={page + 1}" if end < all_entry_count else None,
                "previous": f"?page={page - 1}" if page > 1 else None,
                "total_pages": (all_entry_count + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size,
                "results": formatted_summaries,
                "summary": summary,
                "filter_options": filter_options,
            }
        )

    def _get_client_trust_filter_options(self, case_queryset, skip_few_filters=False, only_check_deposits=False):
        """Get filter options for client trust report"""
        # Get cases with client trust entries

        # Get available deposit types
        deposit_types = [{"value": "true", "label": "Deposits"}, {"value": "false", "label": "Debits"}]

        if not skip_few_filters:
            return {
                "organization_statuses": self.get_case_status_categories(case_queryset),
                **self.case_workers_filters_options(case_queryset),
                "role_options": self.get_case_worker_role_filter_options(case_queryset),
                "deposit_types": deposit_types,
                "client_trust_entry_types": [
                    {"value": entry_type.value, "label": entry_type.label} for entry_type in ClientTrustEntryType
                ],
                "show_attorney_fees": [
                    {"value": "true", "label": "Show Attorney Fees"},
                    {"value": "false", "label": "Hide Attorney Fees"},
                ],
                "date_fields": [{"value": "created_at", "label": "Created Date"}],
            }
        else:
            if only_check_deposits:
                client_trust_entry_types = [
                    {
                        "value": ClientTrustEntryType.SETTLEMENT_CHECK.value,
                        "label": ClientTrustEntryType.SETTLEMENT_CHECK.label,
                    },
                    {
                        "value": ClientTrustEntryType.MEDPAY_DEPOSIT.value,
                        "label": ClientTrustEntryType.MEDPAY_DEPOSIT.label,
                    },
                    {
                        "value": ClientTrustEntryType.REFERRED_FEE.value,
                        "label": ClientTrustEntryType.REFERRED_FEE.label,
                    },
                ]
            else:
                client_trust_entry_types = [
                    {"value": entry_type.value, "label": entry_type.label} for entry_type in ClientTrustEntryType
                ]

            return {
                "organization_statuses": self.get_case_status_categories(case_queryset),
                **self.case_workers_filters_options(case_queryset),
                "role_options": self.get_case_worker_role_filter_options(case_queryset),
                "client_trust_entry_types": client_trust_entry_types,
                "date_fields": [{"value": "deposit_date", "label": "Deposit Date"}],
            }

    @action(detail=False, methods=["get"])
    def estimated_value_report(self, request):
        """
        Generate a report of estimated case values.

        This includes:
        - Case information
        - Number of defendants
        - 3rd party policy limits (sum for all defendants)
        - UIM policy limits
        - Medical bills total
        - Loss of wages
        - Calculated estimated value ((Medical Cost + Loss of Wages) * 3)
        - Manually entered estimated value (from incident details)
        """
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()

        # Calculate min/max ranges from the full dataset before filtering
        full_dataset_min_max = self.calculate_estimated_value_ranges(case_queryset)

        # Now apply filters
        case_queryset = self.apply_filter_on_case_queryset(case_queryset)

        case_queryset = case_queryset.exclude(
            Q(organization_status__kpi_type__in=[KPIType.case_closed, KPIType.case_dropped])
            | Q(organization_status__name__in=["Settlement", "Disbursement"])
        )

        # Apply estimated value range filters if provided
        calculated_value_min = request.query_params.get("calculated_value_min")
        calculated_value_max = request.query_params.get("calculated_value_max")

        # Filter by calculated estimated value range
        if calculated_value_min:
            try:
                calculated_value_min = float(calculated_value_min)
                # We need to filter on the calculation (medical_bills + lost_wages) * 3
                case_queryset = case_queryset.annotate(
                    calc_value=ExpressionWrapper(
                        (
                            Coalesce(Sum("treatment_providers__original_bill"), 0)
                            + Coalesce(Sum("employers__total_lost_wages"), 0)
                        )
                        * 3,
                        output_field=models.FloatField(),
                    )
                )
                if calculated_value_min < 1:
                    pass
                else:
                    case_queryset = case_queryset.filter(calc_value__gte=calculated_value_min)
            except ValueError:
                pass

        if calculated_value_max:
            try:
                calculated_value_max = float(calculated_value_max)
                # Ensure we have the annotation if it wasn't added above
                if not calculated_value_min:
                    case_queryset = case_queryset.annotate(
                        calc_value=ExpressionWrapper(
                            (
                                Coalesce(Sum("treatment_providers__original_bill"), 0)
                                + Coalesce(Sum("employers__total_lost_wages"), 0)
                            )
                            * 3,
                            output_field=models.FloatField(),
                        )
                    )
                if calculated_value_max < 1000000:
                    case_queryset = case_queryset.filter(calc_value__lte=calculated_value_max)
                else:
                    case_queryset = case_queryset.filter(calc_value__lte=10**9)
            except ValueError:
                pass

        # Get all the data we need for the report with prefetch related
        case_queryset = case_queryset.prefetch_related(
            "v2_defendants",
            "v2_defendants__insurances",
            "v2_defendants__insurances__insurance_company",
            "insurances",
            "employers",
            "treatment_providers",
        ).select_related(
            "incident_details",
            "client_basic_details",
            "v2_workers",
        )

        # Format data for the report
        formatted_cases = []

        total_cases = case_queryset.count()
        summary = {
            "total_cases": total_cases,
            "total_calculated_estimated_value": 0,
            "total_manual_estimated_value": 0,
        }

        for case in case_queryset:
            # Extract case data
            case_data = self.get_case_data_for_estimated_value_report(case)
            summary["total_calculated_estimated_value"] += case_data.get("calculated_estimated_value", 0) or 0
            summary["total_manual_estimated_value"] += case_data.get("estimated_value", 0) or 0
            formatted_cases.append(case_data)
        # Get filter options
        filter_options = self.get_estimated_value_filter_options(case_queryset, full_dataset_min_max)

        # Check if CSV export is requested
        if self._should_export_csv(request):
            return self._get_csv_response("estimated_value_report.csv", formatted_cases)

        # Return paginated response
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(formatted_cases, request)

        return paginator.get_paginated_response(
            {
                "results": page,
                "summary": summary,
                "filter_options": filter_options,
            }
        )

    def get_case_data_for_estimated_value_report(self, case):
        """Extract case data for the estimated value report"""
        # Format case name
        case_name = f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details and case.incident_details.incident_date else 'N/A'} : {case.id}"

        # Count number of defendants
        num_defendants = case.v2_defendants.count() if hasattr(case, "v2_defendants") else 0

        # Calculate 3rd party policy limit (sum for all defendants)
        third_party_limit = 0
        if hasattr(case, "v2_defendants"):
            for defendant in case.v2_defendants.all():
                for insurance in defendant.insurances.all():
                    try:
                        third_party_limit += extract_policy_limit(str(insurance.policy_limits))
                    except Exception:
                        pass
        if third_party_limit == 0:
            third_party_limit = None

        # Get UIM policy limit from client insurance
        uim_limit = 0
        if hasattr(case, "insurances"):
            for insurance in case.insurances.all():
                if hasattr(insurance, "um_uim") and insurance.um_uim:
                    try:
                        uim_limit = max(uim_limit, extract_policy_limit(insurance.um_uim))
                    except Exception:
                        pass
            if uim_limit == 0:
                uim_limit = None

        # Calculate total medical bills
        medical_bills = 0
        if hasattr(case, "treatment_providers"):
            for provider in case.treatment_providers.all():
                if hasattr(provider, "original_bill") and provider.original_bill:
                    medical_bills += float(provider.original_bill or 0)
        if medical_bills == 0:
            medical_bills = None

        # Calculate total loss of wages
        loss_of_wages = 0
        if hasattr(case, "employers"):
            for employer in case.employers.all():
                if hasattr(employer, "total_lost_wages") and employer.total_lost_wages:
                    loss_of_wages += float(employer.total_lost_wages or 0)
        if loss_of_wages == 0:
            loss_of_wages = None

        # Calculate estimated value
        calculated_value = 0
        if medical_bills:
            calculated_value += medical_bills
        if loss_of_wages:
            calculated_value += loss_of_wages
        calculated_value *= 3
        if calculated_value == 0:
            calculated_value = None

        # Get manual estimated value
        manual_value = 0
        if hasattr(case, "incident_details") and case.incident_details:
            manual_value = float(case.incident_details.estimated_value or 0)
        if manual_value == 0:
            manual_value = None

        return {
            "case": case_name,
            "number_of_defendants": num_defendants,
            "calculated_estimated_value": calculated_value,
            "estimated_value": manual_value,
            "third_party_policy_limit": third_party_limit,
            "uim_policy_limit": uim_limit,
            "medical_bills_total": medical_bills,
            "loss_of_wages": loss_of_wages,
        }

    def calculate_estimated_value_ranges(self, queryset):
        """Calculate min/max ranges for estimated values from the full dataset"""
        from django.db.models import FloatField, Max, Min
        from django.db.models.functions import Coalesce

        # Calculate min/max for manual estimated values
        manual_range = queryset.filter(incident_details__estimated_value__isnull=False).aggregate(
            min_value=Min("incident_details__estimated_value"), max_value=Max("incident_details__estimated_value")
        )

        # For calculated values, we need to use the formula (Medical Cost + Loss of Wages) * 3
        # First, get the range of calculated values using annotations
        calculated_range = queryset.annotate(
            calc_value=ExpressionWrapper(
                (
                    Coalesce(Sum("treatment_providers__original_bill"), 0)
                    + Coalesce(Sum("employers__total_lost_wages"), 0)
                )
                * 3,
                output_field=FloatField(),
            )
        ).aggregate(min_value=Min("calc_value"), max_value=Max("calc_value"))

        # Ensure we have values (not None)
        manual_min = float(manual_range["min_value"] or 0)
        manual_max = float(manual_range["max_value"] or 0)
        calculated_min = float(calculated_range["min_value"] or 0)
        calculated_max = float(calculated_range["max_value"] or 0)

        return {
            "manual_estimated_value": {
                "min": manual_min,
                "max": manual_max,
            },
            "calculated_estimated_value": {
                "min": calculated_min,
                "max": calculated_max,
            },
        }

    def get_estimated_value_filter_options(self, queryset, value_ranges=None):
        """Get filter options for estimated value report"""

        # Get case type options
        base_queryset = self.get_base_queryset()
        case_types = base_queryset.values_list("incident_details__incident_type", flat=True).distinct()
        case_types = list(set([ct for ct in case_types if ct]))
        case_type_options = [{"value": ct, "label": ct} for ct in case_types]

        # Get date fields
        date_fields = [
            {"value": "doi", "label": "Date of Incident"},
            {"value": "created_at", "label": "Created Date"},
            {"value": "updated_at", "label": "Updated Date"},
        ]

        # Add estimated value range filters
        value_range_filters = {}
        if value_ranges:
            value_range_filters = {
                "calculated_estimated_value_range": [
                    {
                        "min": 0,
                        "max": max(
                            min(round(value_ranges["calculated_estimated_value"]["max"] // 1000 + 1) * 1000, 1000000),
                            1000000,
                        ),
                        "label": "Calculated Estimated Value Range",
                    },
                ],
                # "manual_estimated_value_range": {
                #     "min": value_ranges["manual_estimated_value"]["min"],
                #     "max": value_ranges["manual_estimated_value"]["max"],
                #     "label": "Manual Estimated Value Range",
                # },
            }

        # Return combined filter options
        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset),
            **self.case_workers_filters_options(queryset),
            "date_fields": date_fields,
            **self.get_case_fix_status_filter_options(),
            **value_range_filters,
        }

    @action(detail=False, methods=["get"])
    def case_costs(self, request):
        """Get case costs report with entries and summary statistics"""
        # Get base queryset of cases the user has access to
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        # Get paid case costs
        case_costs = CaseCost.objects.filter(case__in=case_queryset, is_void=False).select_related(
            "case", "contact", "case__client_basic_details", "case__organization_status", "case__incident_details"
        )

        # Apply payment type filter
        payment_type = request.query_params.get("payment_type")
        if payment_type:
            case_costs = case_costs.filter(payment_type=payment_type)

        # Apply priority filter
        priority = request.query_params.get("priority")
        if priority:
            case_costs = case_costs.filter(priority=priority)

        # Apply section filter
        section = request.query_params.get("section")
        if section:
            case_costs = case_costs.filter(cost_for=section)

        # Apply status filter
        status = request.query_params.get("cost_status")
        if status:
            case_costs = case_costs.filter(status=status)

        # Date range filter
        date_from = request.query_params.get("from_date")
        if date_from:
            try:
                date_from = parse_date(date_from)
                case_costs = case_costs.filter(paid_date__gte=date_from)
            except ValueError:
                pass

        date_to = request.query_params.get("to_date")
        if date_to:
            try:
                date_to = parse_date(date_to)
                case_costs = case_costs.filter(paid_date__lte=date_to)
            except ValueError:
                pass

        # Amount range filter
        amount_min = request.query_params.get("amount_min")
        if amount_min:
            try:
                amount_min = float(amount_min)
                case_costs = case_costs.filter(amount__gte=amount_min)
            except ValueError:
                pass

        amount_max = request.query_params.get("amount_max")
        if amount_max:
            try:
                amount_max = float(amount_max)
                case_costs = case_costs.filter(amount__lte=amount_max)
            except ValueError:
                pass

        # Search filter for contact name, memo, or check number
        search_query = request.query_params.get("search")
        if search_query:
            case_costs = case_costs.filter(
                Q(contact__company_name__icontains=search_query)
                | Q(memo__icontains=search_query)
                | Q(check_number__icontains=search_query)
            )

        # Calculate summary information
        total_costs = case_costs.aggregate(total=models.Sum("amount"))["total"] or Decimal("0")

        # Apply ordering
        ordering = request.query_params.get("ordering", "-paid_date")
        if ordering:
            case_costs = case_costs.order_by(ordering)
        else:
            case_costs = case_costs.order_by("-paid_date", "-created_at")

        def format_case_cost(cost):
            client_name = "Unknown"
            if hasattr(cost.case, "client_basic_details") and cost.case.client_basic_details:
                basic_details = cost.case.client_basic_details
                client_name = f"{basic_details.first_name} {basic_details.last_name}"

            case_name = None
            if (
                hasattr(cost.case, "client_basic_details")
                and cost.case.client_basic_details
                and hasattr(cost.case, "incident_details")
                and cost.case.incident_details
                and cost.case.incident_details.incident_date
            ):
                case_name = f"{cost.case.client_basic_details.first_name} {cost.case.client_basic_details.last_name} - {cost.case.incident_details.incident_date.strftime('%m/%d/%Y')} : {cost.case.id}"
            else:
                case_name = f"Case {cost.case.id}"

            return {
                "case": case_name,
                "organization_status": cost.case.organization_status.display_name
                if cost.case.organization_status
                else None,
                "status": cost.status,
                "paid_date": cost.paid_date.strftime("%m/%d/%Y") if cost.paid_date else None,
                "requested_date": cost.requested_date.strftime("%m/%d/%Y") if cost.requested_date else None,
                "request_created_by": cost.request_created_by.first_name + " " + cost.request_created_by.last_name
                if cost.request_created_by
                else None,
                "paid_created_by": cost.paid_created_by.first_name + " " + cost.paid_created_by.last_name
                if cost.paid_created_by
                else None,
                "payee": cost.contact.company_name if cost.contact else "Unknown",
                "memo": cost.memo,
                "check_number": (
                    str(cost.check_number).replace("nan", "") or None
                    if hasattr(cost, "check_number")
                    and cost.check_number
                    and cost.check_number != ""
                    and pd.notna(cost.check_number)
                    else None
                ),
                "amount": float(cost.amount) if cost.amount else 0,
                "created_at": cost.created_at.strftime("%m/%d/%Y %H:%M") if cost.created_at else None,
            }

        # Prepare summary data
        summary = {
            "total_costs": float(total_costs),
            "total_entries": case_costs.count(),
            "case_count": case_costs.values("case").distinct().count(),
        }

        # Get filter options
        filter_options = self._get_case_costs_filter_options(self.get_base_queryset())

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Format all entries
        formatted_entries = [format_case_cost(cost) for cost in case_costs]
        if self._should_export_csv(request):
            return self._get_csv_response("case_costs.csv", formatted_entries)

        # Calculate pagination
        page = int(request.query_params.get("page", 1))
        start = (page - 1) * page_size
        end = start + page_size

        # Create paginated subset
        paginated_entries = formatted_entries[start:end]

        # Create response with proper pagination metadata
        return Response(
            {
                "count": len(formatted_entries),
                "next": f"?page={page + 1}" if end < len(formatted_entries) else None,
                "previous": f"?page={page - 1}" if page > 1 else None,
                "total_pages": (len(formatted_entries) + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size,
                "results": paginated_entries,
                "summary": summary,
                "filter_options": filter_options,
            }
        )

    def _get_case_costs_filter_options(self, case_queryset):
        """Get filter options for case costs report"""
        # Get status options from model choices
        status_options = [{"value": status[0], "label": status[1]} for status in CaseCost.STATUS_CHOICES]

        # Get payment type options from model choices
        payment_type_options = [{"value": ptype[0], "label": ptype[1]} for ptype in CaseCost.PAYMENT_TYPE_CHOICES]

        # Get priority options from model choices
        priority_options = [{"value": priority[0], "label": priority[1]} for priority in CaseCost.PRIORITY_CHOICES]

        # Get section options from model choices
        section_options = [{"value": section[0], "label": section[1]} for section in CaseCost.SECTION_CHOICES]

        return {
            "organization_statuses": self.get_case_status_categories(case_queryset),
            **self.case_workers_filters_options(case_queryset),
            "role_options": self.get_case_worker_role_filter_options(case_queryset),
            "cost_status": status_options,
            "payment_type_options": payment_type_options,
            "priority_options": priority_options,
            "section_options": section_options,
            "date_fields": [
                {"value": "created_at", "label": "Paid Date"},
            ],
        }

    @action(detail=False, methods=["get"])
    def case_review(self, request, *args, **kwargs):
        """Return case review data for the organization with filters, sorting, and pagination."""

        def format_case_data(case):
            case_data = {
                "case": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y') if hasattr(case, 'incident_details') and case.incident_details.incident_date else 'N/A'} : {case.id}",
                "days": case.days.days if case.days else 0,
                "case_type": case.incident_details.incident_type
                if hasattr(case, "incident_details") and case.case.incident_details
                else None,
                "status": case.organization_status.name if case.organization_status else None,
                "primary": f"{case.v2_workers.primary_contact.first_name} {case.v2_workers.primary_contact.last_name}"
                if case.v2_workers and case.v2_workers.primary_contact
                else None,
                "last_touched": case.updated_at.strftime("%m/%d/%Y") if case.updated_at else None,
            }

            # Add team data efficiently
            if case.v2_workers:
                team = []
                for role in ["case_manager", "lead_attorney"]:
                    member = getattr(case.v2_workers, role)
                    if member:
                        team.append({"id": member.id, "name": f"{member.first_name} {member.last_name}", "role": role})
                case_data["team"] = team
            else:
                case_data["team"] = []
            return case_data

        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = KPIType.case_open

        start_time = time.time()
        logger.info("Starting case_review endpoint")

        # Get base queryset
        queryset_start = time.time()
        queryset = self.get_base_queryset()
        logger.info(f"Time to get base queryset: {time.time() - queryset_start:.3f}s")

        # Apply filters
        filters_start = time.time()
        queryset = self.apply_filter_on_case_queryset(queryset)
        queryset = self.apply_case_review_filters(queryset, request)
        logger.info(f"Time to apply filters: {time.time() - filters_start:.3f}s")

        # Calculate summary statistics efficiently in a single query
        stats_start = time.time()
        stats = queryset.aggregate(
            total_cases=Count("id"),
            avg_days=Avg("days"),
            old_cases=Count("id", filter=Q(created_at__lte=timezone.now() - timedelta(days=365))),
            recent_touches=Count("id", filter=Q(updated_at__gte=timezone.now() - timedelta(days=7))),
        )

        total_cases = stats["total_cases"]
        summary = {
            "total_cases": total_cases,
            "average_case_age": stats["avg_days"].days if stats["avg_days"] else 0,
            "touched_last_7_days": round((stats["recent_touches"] / total_cases * 100) if total_cases else 0),
            "aged_over_1_year": round((stats["old_cases"] / total_cases * 100) if total_cases else 0),
        }
        logger.info(f"Time to calculate summary stats: {time.time() - stats_start:.3f}s")

        # Get filter options
        filter_start = time.time()
        filter_options = self.get_case_review_filter_options(self.get_base_queryset())
        logger.info(f"Time to get filter options: {time.time() - filter_start:.3f}s")

        # Apply sorting
        queryset = self.apply_case_review_sorting(queryset, request)

        # Check if CSV export is requested
        if self._should_export_csv(request):
            result = [format_case_data(case) for case in queryset]
            return self._get_csv_response("case_review.csv", result)

        # Handle pagination with optimization
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Optimize the query by selecting only necessary related fields
        queryset = queryset.select_related(
            "client_basic_details",
            "incident_details",
            "organization_status",
            "v2_workers",
            "v2_workers__primary_contact",
            "v2_workers__lead_attorney",
            "v2_workers__case_manager",
        ).defer(
            "client_basic_details__created_at",
            "client_basic_details__updated_at",
            "incident_details__created_at",
            "incident_details__updated_at",
            "organization_status__created_at",
            "organization_status__updated_at",
            "v2_workers__created_at",
            "v2_workers__updated_at",
        )

        pagination_start = time.time()
        page = paginator.paginate_queryset(queryset, request)
        logger.info(f"Time to paginate queryset: {time.time() - pagination_start:.3f}s")

        if page is not None:
            format_start = time.time()
            results = [format_case_data(case) for case in page]
            logger.info(f"Time to format results: {time.time() - format_start:.3f}s")
            total_time = time.time() - start_time
            logger.info(f"Total response time: {total_time:.3f}s")

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": results}
            )

    def apply_case_review_filters(self, queryset, request):
        """Apply filters to the case review queryset based on query parameters"""
        filters = {}
        search_query = request.query_params.get("search")
        # Worker filter
        worker = request.query_params.get("worker")
        if worker:
            queryset = queryset.filter(
                Q(v2_workers__primary_contact_id=worker)
                | Q(v2_workers__case_manager_id=worker)
                | Q(v2_workers__lead_attorney_id=worker)
                | Q(v2_workers__case_assistant_id=worker)
                | Q(v2_workers__lien_negotiator_id=worker)
                | Q(v2_workers__supervising_attorney_id=worker)
                | Q(v2_workers__intake_specialist_id=worker)
                | Q(v2_workers__investigator_id=worker)
                | Q(v2_workers__accountant_id=worker)
                | Q(v2_workers__litigation_attorney_id=worker)
            ).distinct()

        tag = request.query_params.get("role")
        if tag:
            # Get all users that have this tag
            queryset = self.case_queryset_filter_user_tag(queryset, tag)

        # Primary filter
        primary = request.query_params.get("primary")
        if primary:
            filters["v2_workers__primary_contact_id"] = primary

        # Case Type filter
        case_type = request.query_params.get("case_type")
        if case_type:
            filters["incident_details__incident_type"] = case_type

        # Case Status filter
        case_status = request.query_params.get("case_status")
        if case_status:
            filters["organization_status__name"] = case_status

        # Lead Attorney filter
        lead_attorney = request.query_params.get("lead_attorney")
        if lead_attorney:
            filters["v2_workers__lead_attorney_id"] = lead_attorney

        # Supervising Attorney filter
        supervising_attorney = request.query_params.get("supervising_attorney")
        if supervising_attorney:
            filters["v2_workers__supervising_attorney_id"] = supervising_attorney

        # Case Manager filter
        case_manager = request.query_params.get("case_manager")
        if case_manager:
            filters["v2_workers__case_manager_id"] = case_manager

        # Organization Status filter
        org_status_id = request.query_params.get("organization_status")
        if org_status_id:
            filters["organization_status_id"] = org_status_id

        # Apply all collected filters
        if filters:
            queryset = queryset.filter(**filters)

        # Apply text search if provided
        if search_query:
            queryset = queryset.filter(
                Q(client_basic_details__first_name__icontains=search_query)
                | Q(client_basic_details__last_name__icontains=search_query)
                | Q(case_name__icontains=search_query)
                | Q(id__icontains=search_query)
                | Q(v2_workers__primary_contact__first_name__icontains=search_query)
                | Q(v2_workers__primary_contact__last_name__icontains=search_query)
                | Q(v2_workers__lead_attorney__first_name__icontains=search_query)
                | Q(v2_workers__lead_attorney__last_name__icontains=search_query)
            ).distinct()

        # Exclude intake statuses if requested
        exclude_intake = request.query_params.get("exclude_intake_statuses") == "true"
        if exclude_intake:
            queryset = queryset.exclude(organization_status__kpi_type=KPIType.intake_completed.value)

        # View only lead cases if requested
        lead_only = request.query_params.get("view_only_lead_cases") == "true"
        if lead_only:
            queryset = queryset.filter(is_lead_case=True)

        return queryset

    def apply_case_review_sorting(self, queryset, request):
        """Apply sorting to the case review queryset based on query parameters"""
        sort_by = request.query_params.get("sort_by", "case")
        sort_dir = "-" if request.query_params.get("sort_dir") == "desc" else ""

        # Map frontend sort fields to model fields
        sort_field_mapping = {
            "case": "client_basic_details__last_name",
            "days": "days",
            "case_type": "incident_details__incident_type",
            "case_status": "organization_status__name",
            "worker": "v2_workers__primary_contact__last_name",
            "primary": "v2_workers__primary_contact__last_name",
            "lead_attorney": "v2_workers__lead_attorney__last_name",
            "supervising_attorney": "v2_workers__supervising_attorney__last_name",
        }

        sort_field = sort_field_mapping.get(sort_by.replace("-", ""), "client_basic_details__last_name")
        if sort_by.startswith("-"):
            sort_field = f"-{sort_field}"

        return queryset.order_by(f"{sort_dir}{sort_field}")

    def format_case_review_data(self, case):
        """Format case data for the case review response"""
        # Convert duration to days
        days = case.days.days if case.days else 0

        # Get workers data
        workers = case.v2_workers if hasattr(case, "v2_workers") else None

        # Format case name for display
        case_name = f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}"
        # Get team data
        team = self._get_team_data(workers) if workers else []

        # Format the response data
        result = {
            "case": case_name,
            "days": days,
            "case_type": getattr(case.incident_details, "incident_type", None)
            if hasattr(case, "incident_details")
            else None,
            "case_status": case.organization_status.name,
            "worker": workers.primary_contact.get_full_name() if workers and workers.primary_contact else None,
            "primary": workers.primary_contact.get_full_name() if workers and workers.primary_contact else None,
            "lead_attorney": workers.lead_attorney.get_full_name() if workers and workers.lead_attorney else None,
            "supervising_attorney": workers.supervising_attorney.get_full_name()
            if workers and workers.supervising_attorney
            else None,
            "team": team,
        }

        return result

    def generate_case_review_csv(self, queryset):
        """Generate CSV export for case review data"""
        # Format all cases for CSV export
        all_case_data = [self.format_case_review_data(case) for case in queryset]

        # Prepare CSV data
        headers = [
            "ID",
            "Case",
            "Days",
            "Case Type",
            "Case Status",
            "Worker",
            "Primary",
            "Lead Attorney",
            "Supervising Attorney",
            "Team",
        ]

        csv_data = []
        for case in all_case_data:
            # Format team data as a string
            team_str = "; ".join([f"{member['role']}: {member['name']}" for member in case.get("team", [])])

            csv_data.append(
                [
                    case.get("id", ""),
                    case.get("case", ""),
                    case.get("days", ""),
                    case.get("case_type", ""),
                    case.get("case_status", ""),
                    case.get("case_grade", ""),
                    case.get("case_grade_reason", ""),
                    case.get("worker", ""),
                    case.get("primary", ""),
                    case.get("lead_attorney", ""),
                    case.get("supervising_attorney", ""),
                    team_str,
                ]
            )

        return self._get_csv_response("case_review.csv", csv_data, headers)

    def get_case_review_filter_options(self, queryset):
        """Get filter options for case review page"""
        # Get organization status categories (reuse existing method)
        organization_statuses = self.get_case_status_categories(queryset)

        # Get worker filter options (reuse existing method)
        workers_filters = self.case_workers_filters_options(queryset)

        # Get case types
        case_types = (
            queryset.values_list("incident_details__incident_type", flat=True)
            .filter(incident_details__incident_type__isnull=False)
            .distinct()
            .order_by("incident_details__incident_type")
        )

        # Get case statuses
        case_statuses = (
            queryset.values_list("status", flat=True).filter(status__isnull=False).distinct().order_by("status")
        )

        organization = self.request.user.organizations.first()

        roles = UserTag.objects.filter(organization=organization).distinct()

        date_field_options = [
            {"value": "doi", "label": "Date of Incident"},
            {"value": "created_at", "label": "Created Date"},
            {"value": "updated_at", "label": "Updated Date"},
            {"value": "last_touched", "label": "Last Touched Date"},
            {"value": "statute", "label": "Statute Date"},
        ]

        return {
            "case_types": [{"value": ct, "label": ct} for ct in case_types],
            "organization_statuses": organization_statuses,
            **workers_filters,
            "date_fields": date_field_options,
            "roles": [{"value": role.name, "label": role.name, "id": role.id} for role in roles],
        }

    @action(detail=False, methods=["get"])
    def case_events_calendar(self, request):
        """Get case events calendar data"""
        # First get filtered cases based on view type (my_cases/all_cases)
        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        def format_event_data(event):
            case = event.case
            date_str = event.date.strftime("%m/%d/%Y") if event.date else ""
            if event.start_time and event.end_time:
                date_str += f" {event.start_time.strftime('%I:%M %p')} - {event.end_time.strftime('%I:%M %p')}"

            # Format case name
            case_name = f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name}"
            if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
                case_name += f" - {case.incident_details.incident_date.strftime('%m/%d/%Y')}"
            case_name += f" : {case.id}"

            # Get DOI (Date of Incident)
            doi = ""
            if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
                doi = case.incident_details.incident_date.strftime("%m/%d/%Y")

            # team data
            team_data = []
            if case.v2_workers:
                for role in ["case_manager", "lead_attorney", "case_assistant"]:
                    member = getattr(case.v2_workers, role)
                    if member:
                        team_data.append(
                            {"id": member.id, "name": f"{member.first_name} {member.last_name}", "role": role}
                        )

            return {
                "date": date_str,
                "event_name": event.title or event.event_type,
                "type": event.event_type,
                "case": case_name,
                "description": event.description or "",
                "litigation_event_status": event.status or "",
                "team": team_data,
            }

        queryset = (
            CaseEvent.objects.filter(case__in=case_queryset)
            .select_related(
                "case",
                "case__client_basic_details",
                "case__incident_details",
                "case__organization_status",
            )
            .prefetch_related("workers")
        )

        # Apply search filter
        search_query = request.query_params.get("search")
        if search_query:
            queryset = queryset.filter(
                Q(case__client_basic_details__first_name__icontains=search_query)
                | Q(case__client_basic_details__last_name__icontains=search_query)
                | Q(case__id__icontains=search_query)
                | Q(title__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(event_type__icontains=search_query)
            ).distinct()

        # Apply date range filters
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        if from_date and to_date:
            queryset = queryset.filter(date__range=[from_date, to_date])

        # Apply quick filter if specified
        quick_filter = request.query_params.get("quick_filter")
        if quick_filter:
            today = timezone.now().date()
            if quick_filter == "today":
                queryset = queryset.filter(date=today)
            elif quick_filter == "next_7_days":
                queryset = queryset.filter(date__range=[today, today + timedelta(days=7)])

        # Apply event type filter
        event_type = request.query_params.get("event_type")
        if event_type:
            queryset = queryset.filter(event_type__icontains=event_type)
        # Apply litigation event status filter
        litigation_event_status = request.query_params.get("litigation_event_status")
        if litigation_event_status:
            queryset = queryset.filter(status=litigation_event_status)

        # Calculate summary statistics
        today_count = queryset.filter(date=timezone.now().date()).count()
        next_7_days_count = queryset.filter(
            date__range=[timezone.now().date(), timezone.now().date() + timedelta(days=7)]
        ).count()

        # Create summary
        summary = {
            "today": today_count,
            "next_7_days": next_7_days_count,
            "total_events": queryset.count(),
        }

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        # Apply sorting
        sort_by = request.query_params.get("sort_by", "date")
        sort_dir = request.query_params.get("sort_dir", "asc")

        order_field = sort_by
        if sort_dir.lower() == "desc":
            order_field = f"-{sort_by}"

        queryset = queryset.order_by(order_field)

        page = paginator.paginate_queryset(queryset, request)

        if self._should_export_csv(request):
            event_data = [format_event_data(event) for event in queryset]
            return self._get_csv_response("case_events.csv", event_data)

        if page is not None:
            event_data = [format_event_data(event) for event in page]
            filter_options = self.get_case_events_calendar_filter_options(queryset)

            return paginator.get_paginated_response(
                {"summary": summary, "filter_options": filter_options, "results": event_data}
            )

    def get_case_events_calendar_filter_options(self, queryset):
        """Get filter options for case events calendar report"""
        # Get organization for the current user
        organization = self.request.user.organizations.first()

        # Get case statuses
        case_statuses = self.get_case_status_categories(self.get_base_queryset())

        # Get case types
        case_types = (
            self.get_base_queryset()
            .exclude(incident_details__incident_type__isnull=True)
            .values_list("incident_details__incident_type", flat=True)
            .distinct()
            .order_by("incident_details__incident_type")
        )

        # Get event types
        event_types = CaseEvent.EVENT_TYPE_CHOICES

        # Get workers
        from users.models import User

        workers = (
            User.objects.filter(assigned_events__in=queryset)
            .distinct()
            .order_by("first_name")
            .values("id", "first_name", "last_name")
        )

        # Event statuses
        event_statuses = (
            queryset.exclude(status__isnull=True).values_list("status", flat=True).distinct().order_by("status")
        )

        date_fields = [
            {"value": "event_date", "label": "Event Date"},
        ]

        return {
            "workers": [
                {"id": worker["id"], "name": f"{worker['first_name']} {worker['last_name']}"} for worker in workers
            ],
            "case_types": [{"value": ct, "label": ct} for ct in case_types],
            "case_status_categories": case_statuses,
            "event_types": [{"value": et[0], "label": et[1]} for et in event_types],
            "litigation_event_statuses": [{"value": status, "label": status} for status in event_statuses],
            "date_fields": date_fields,
        }

    def _calculate_case_duration(self, case):
        """Calculate the duration of a case in months"""
        try:
            if case.created_at:
                # Calculate months between case creation and now
                today = timezone.now().date()
                case_start = case.created_at.date()

                # Calculate difference in months
                months = (today.year - case_start.year) * 12 + (today.month - case_start.month)
                return months
            return 0
        except:
            return 0

    @action(detail=False, methods=["get"])
    def medpay_deposits(self, request):
        """Get medpay deposits"""
        case_queryset = self.get_base_queryset()
        if not pd.notna(request.query_params.get("case_kpi_status")):
            request._request.GET = request._request.GET.copy()
            request._request.GET["case_kpi_status"] = "open"

        case_queryset = self.apply_filter_on_case_queryset(case_queryset, skip_dates=True)

        queryset = MedPayDeposit.objects.filter(case__in=case_queryset)

        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        if from_date and to_date:
            queryset = queryset.filter(medpay_request_date__range=[from_date, to_date])

        # Calculate summary statistics
        summary = {
            "total_deposits": queryset.count(),
            "total_amount": queryset.aggregate(total=Coalesce(Sum("amount"), Decimal(0)))["total"],
            "paid_amount": queryset.filter(status=MedPayDepositStatus.PAID).aggregate(
                total=Coalesce(Sum("amount"), Decimal(0))
            )["total"],
            "pending_amount": queryset.filter(status=MedPayDepositStatus.INVOICE_RAISED).aggregate(
                total=Coalesce(Sum("amount"), Decimal(0))
            )["total"],
            "client_medpay_amount": queryset.filter(insurance_type="CLIENT_MEDPAY").aggregate(
                total=Coalesce(Sum("amount"), Decimal(0))
            )["total"],
            "third_party_medpay_amount": queryset.filter(insurance_type="THIRD_PARTY_MEDPAY").aggregate(
                total=Coalesce(Sum("amount"), Decimal(0))
            )["total"],
        }

        filter_options = self.get_medpay_deposits_filter_options(queryset)
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        def format_medpay_deposit(deposit):
            client_name = "Unknown"
            if hasattr(deposit.case, "client_basic_details") and deposit.case.client_basic_details:
                basic_details = deposit.case.client_basic_details
                client_name = f"{basic_details.first_name} {basic_details.last_name}"

            case_name = f"{deposit.case.client_basic_details.last_name}, {deposit.case.client_basic_details.first_name} - {deposit.case.incident_details.incident_date.strftime('%m/%d/%Y')} : {deposit.case.id}"

            return {
                "case": case_name,
                "medpay_type": deposit.insurance_type,
                "insurance": deposit.client_insurance.insurance_company.name
                if deposit.insurance_type == "CLIENT_MEDPAY"
                else deposit.defendant_insurance.insurance_company.name,
                "request": float(deposit.amount) if deposit.amount else 0,
                "deposited": deposit.client_trust_entry.amount if deposit.client_trust_entry else 0,
                "total_meds": TreatmentProvider.objects.filter(case=deposit.case).aggregate(
                    total=Coalesce(Sum("original_bill"), Decimal(0))
                )["total"],
                "total_costs": CaseCost.objects.filter(case=deposit.case).aggregate(
                    total=Coalesce(Sum("amount"), Decimal(0))
                )["total"],
                "status": deposit.status,
                "medpay_limits": deposit.client_insurance.medpay
                if deposit.insurance_type == "CLIENT_MEDPAY" and deposit.client_insurance
                else deposit.defendant_insurance.med_pay
                if deposit.defendant_insurance
                else None,
                "check_number": deposit.check_number,
                "medpay_request_date": deposit.medpay_request_date.strftime("%m/%d/%Y")
                if deposit.medpay_request_date
                else None,
                "medpay_deposit_date": deposit.medpay_deposit_date.strftime("%m/%d/%Y")
                if deposit.medpay_deposit_date
                else None,
                "note": deposit.note,
                "team": self._get_team_data(deposit.case.v2_workers)
                if deposit and deposit.case and deposit.case.v2_workers
                else [],
            }

        formatted_data = [format_medpay_deposit(deposit) for deposit in queryset]
        if self._should_export_csv(request):
            return self._get_csv_response("medpay_deposits.csv", formatted_data)

        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size
        start = (page - 1) * page_size + 1
        end = start + page_size - 1
        paginated_entries = formatted_data[start - 1 : end]
        return Response(
            {
                "count": len(formatted_data),
                "next": f"?page={page + 1}" if end < len(formatted_data) else None,
                "previous": f"?page={page - 1}" if start > 1 else None,
                "total_pages": (len(formatted_data) + page_size - 1) // page_size,
                "current_page": page,
                "summary": summary,
                "page_size": page_size,
                "filter_options": filter_options,
                "results": paginated_entries,
            }
        )

    def get_medpay_deposits_filter_options(self, queryset):
        """Get filter options for case costs report"""

        return {
            "organization_statuses": self.get_case_status_categories(self.get_base_queryset()),
            **self.case_workers_filters_options(self.get_base_queryset()),
            "role_options": self.get_case_worker_role_filter_options(self.get_base_queryset()),
            **self.get_case_fix_status_filter_options(),
            "date_fields": [
                {"value": "medpay_request_date", "label": "MedPay Request Date"},
            ],
        }


class CaseManagementReportViewSet(viewsets.ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    pagination_class = CaseReportPagination

    def get_base_queryset(self):
        """Get base queryset with common filters and annotations"""
        start_time = time.time()
        logger.info("Starting get_base_queryset")

        # Get organization
        organization = self.request.user.organizations.first()

        # Check if viewing my cases or all cases
        view_type = self.request.query_params.get("view", "my_cases")

        # Initial queryset
        init_start = time.time()
        queryset = Case.objects.filter(organization=organization)
        logger.info(f"Time to get initial queryset: {time.time() - init_start:.3f}s")

        # Filter for my cases if requested
        if view_type == "my_cases":
            my_cases_start = time.time()
            queryset = queryset.filter(
                Q(created_by=self.request.user)
                | Q(assigned_users=self.request.user)
                | Q(v2_workers__primary_contact=self.request.user)
                | Q(v2_workers__case_manager=self.request.user)
                | Q(v2_workers__lead_attorney=self.request.user)
                | Q(v2_workers__case_assistant=self.request.user)
            ).distinct()
            logger.info(f"Time to filter for my cases: {time.time() - my_cases_start:.3f}s")

        # Calculate days using ExpressionWrapper
        now = timezone.now()

        # Define the cutoff date for recent activity (30 days ago)
        thirty_days_ago = now - timedelta(days=30)

        # Apply select_related and annotate
        annotate_start = time.time()

        # Use a single, optimized query to find the last_touched date
        queryset = queryset.select_related(
            "v2_workers",
            "v2_workers__primary_contact",
            "v2_workers__case_manager",
            "v2_workers__lead_attorney",
            "v2_workers__case_assistant",
            "incident_details",
            "organization_status",
            "client_basic_details",
        ).annotate(
            # Calculate days as integer
            days=ExpressionWrapper((Now() - F("created_at")), output_field=DurationField()),
            # Simplify last_touched to just use updated_at
            last_touched=F("updated_at"),
        )

        logger.info(f"Time to apply select_related and annotate: {time.time() - annotate_start:.3f}s")
        logger.info(f"Total time for get_base_queryset: {time.time() - start_time:.3f}s")
        return queryset

    def get_queryset(self):
        """Get queryset with common filters and annotations"""
        return self.get_base_queryset()

    def apply_filter_on_case_queryset(self, queryset, skip_dates=False):
        """Apply filters to the case queryset"""
        # Open/Closed filter
        filters = {}
        search_query = self.request.query_params.get("search")
        case_status = self.request.query_params.get("case_kpi_status")
        # TODO: check this once
        if case_status == "open":
            queryset = queryset.exclude(
                organization_status__kpi_type__in=[
                    KPIType.case_closed,
                    KPIType.case_rejected,
                    KPIType.case_dropped,
                    KPIType.subbed_out,
                    KPIType.pending_drop,
                ]
            )
        elif case_status == "closed":
            queryset = queryset.filter(
                organization_status__kpi_type__in=[
                    KPIType.case_closed,
                    KPIType.case_rejected,
                    KPIType.case_dropped,
                    KPIType.subbed_out,
                    KPIType.pending_drop,
                ]
            )
        elif case_status == "dropped":
            queryset = queryset.filter(organization_status__kpi_type=KPIType.case_dropped)
        else:
            pass

        organization_status = self.request.query_params.get("organization_status")
        if organization_status:
            filters["organization_status__id"] = organization_status

        # Worker filter
        worker = self.request.query_params.get("worker")
        if worker:
            queryset = queryset.filter(
                Q(v2_workers__primary_contact_id=worker)
                | Q(v2_workers__case_manager_id=worker)
                | Q(v2_workers__lead_attorney_id=worker)
                | Q(v2_workers__case_assistant_id=worker)
                | Q(v2_workers__supervising_attorney_id=worker)
                | Q(v2_workers__litigation_assistant_id=worker)
                | Q(v2_workers__lien_negotiator_id=worker)
                | Q(v2_workers__intake_specialist_id=worker)
                | Q(v2_workers__investigator_id=worker)
                | Q(v2_workers__accountant_id=worker)
                | Q(v2_workers__litigation_attorney_id=worker)
            ).distinct()

        # Case Type filter (using incident_type instead)
        case_type = self.request.query_params.get("case_type")
        if case_type:
            filters["incident_details__incident_type"] = case_type

        # Organization Case Status filter
        org_status = self.request.query_params.get("status_category")
        if org_status:
            filters["organization_status__name"] = org_status

        # Case Age filter
        case_age = self.request.query_params.get("case_age")
        if case_age:
            try:
                days_ago = timezone.now() - timedelta(days=int(case_age))
                # We want cases NEWER than days_ago, so use __gte
                queryset = queryset.filter(created_at__gte=days_ago)

                # Add debug logging
                logger.info(f"Filtering for cases newer than: {days_ago}")
                logger.info(f"Cases count after age filter: {queryset.count()}")

            except ValueError:
                logger.error(f"Invalid case_age value: {case_age}")

        # Birthday Month filter
        birthday_month = self.request.query_params.get("birthday_month")
        if birthday_month:
            filters["client_basic_details__date_of_birth__month"] = birthday_month

        # Source Type filter
        source_type = self.request.query_params.get("source_type")
        if source_type:
            filters["client_basic_details__source_type"] = source_type

        # Source Detail filter
        source_detail = self.request.query_params.get("source_detail")
        if source_detail:
            filters["client_basic_details__source_detail"] = source_detail

        # Language filter
        language = self.request.query_params.get("language")
        if language:
            filters["client_basic_details__language"] = language

        # Additional checkbox filters
        has_email = self.request.query_params.get("has_email") == "true"
        if has_email:
            queryset = queryset.filter(client_contact_details__primary_email__isnull=False).exclude(
                client_contact_details__primary_email__exact=""
            )

        exclude_minors = self.request.query_params.get("exclude_minors") == "true"
        if exclude_minors:
            eighteen_years_ago = timezone.now().date() - timedelta(days=365 * 18)
            filters["client_basic_details__date_of_birth__lte"] = eighteen_years_ago

        exclude_deceased = self.request.query_params.get("exclude_deceased") == "true"
        if exclude_deceased:
            filters["client_basic_details__deceased"] = False

        # Date Range filters
        date_field = self.request.query_params.get("date_field")
        from_date = self.request.query_params.get("from_date")
        to_date = self.request.query_params.get("to_date")
        date_filter_applied = False

        if not skip_dates:
            if date_field and (from_date or to_date):
                # Map frontend date field names to model fields
                date_field_mapping = {
                    "doi": "incident_details__incident_date",
                    "created_at": "created_at",
                    "updated_at": "updated_at",
                    "last_touched": "last_touched",
                    "statute": "incident_details__statute_of_limitations",
                    "retained_date": "retained_at",
                }

                model_date_field = date_field_mapping.get(date_field)

                if model_date_field:
                    if from_date:
                        filters[f"{model_date_field}__gte"] = from_date
                    if to_date:
                        filters[f"{model_date_field}__lte"] = to_date
                    date_filter_applied = True
        else:
            logger.info("Skipping date filters")

        # Primary filter (client/contact)
        primary = self.request.query_params.get("primary")
        if primary:
            filters["v2_workers__primary_contact_id"] = primary

        # Lead Attorney filter
        lead_attorney = self.request.query_params.get("lead_attorney")
        if lead_attorney:
            filters["v2_workers__lead_attorney_id"] = lead_attorney

        case_manager = self.request.query_params.get("case_manager")
        if case_manager:
            filters["v2_workers__case_manager_id"] = case_manager

        # Supervising Attorney filter
        supervising_attorney = self.request.query_params.get("supervising_attorney")
        if supervising_attorney:
            filters["v2_workers__supervising_attorney_id"] = supervising_attorney

        # DOI (Date of Incident) filter
        doi = self.request.query_params.get("doi")
        if doi:
            filters["incident_details__incident_date"] = doi

        tag = self.request.query_params.get("role")
        if tag:
            queryset = self.case_queryset_filter_user_tag(queryset, tag)

        exclude_minors = self.request.query_params.get("exclude_minors") == "true"
        if exclude_minors:
            eighteen_years_ago = timezone.now().date() - timedelta(days=365 * 18)
            filters["client_basic_details__date_of_birth__lte"] = eighteen_years_ago

        if filters:
            filter_apply_start = time.time()
            queryset = queryset.filter(**filters)
            logger.info(f"Time to apply filters: {time.time() - filter_apply_start:.3f}s")

        # Apply text search if provided
        if search_query:
            for query in search_query.split(" "):
                queryset = queryset.filter(
                    Q(client_basic_details__first_name__icontains=query)
                    | Q(client_basic_details__last_name__icontains=query)
                    | Q(id__icontains=query)
                    | Q(client_contact_details__primary_email__icontains=query)
                    | Q(client_contact_details__phone_number_1__icontains=query)
                    | Q(v2_workers__case_manager__first_name__icontains=query)
                    | Q(v2_workers__case_manager__last_name__icontains=query)
                    | Q(v2_workers__lead_attorney__last_name__icontains=query)
                ).distinct()

            # add a new filter called kpi type
        kpi_type = self.request.query_params.get("kpi_type")
        if kpi_type:
            if kpi_type == KPIType.case_open:
                # exclude closed, rejected, dropped cases
                queryset = queryset.exclude(
                    organization_status__kpi_type__in=[
                        KPIType.case_closed,
                        KPIType.case_rejected,
                        KPIType.case_dropped,
                        KPIType.subbed_out,
                        KPIType.pending_drop,
                    ]
                )
            elif kpi_type == KPIType.case_closed:
                queryset = queryset.filter(
                    organization_status__kpi_type__in=[
                        KPIType.case_closed,
                        KPIType.case_rejected,
                        KPIType.case_dropped,
                        KPIType.subbed_out,
                        KPIType.pending_drop,
                    ]
                )
            elif kpi_type == KPIType.client_retained:
                queryset = queryset.filter(retained=True)
            elif kpi_type == KPIType.intake_ready:
                if not date_filter_applied:
                    queryset = queryset.filter(created_at__gte=timezone.now() - timedelta(days=90))
            else:
                queryset = queryset.filter(organization_status__kpi_type=kpi_type)

        return queryset

    def get_case_status_categories(self, queryset):
        """Get unique case status categories with proper formatting"""
        # Get organization status names
        status_categories = (
            queryset.values_list(
                "organization_status__name", "organization_status__display_name", "organization_status__id"
            )
            .filter(organization_status__isnull=False)
            .distinct()
            .order_by("organization_status__name")
        )

        # Create a dictionary to ensure uniqueness by name
        unique_categories = {}
        # Fix: Unpack all three values
        for name, display_name, id in status_categories:
            if name and name not in unique_categories:
                unique_categories[name] = {"display": display_name or name, "id": id}

        # Format as list of dictionaries with value/label/id
        return [{"value": name, "label": info["display"], "id": info["id"]} for name, info in unique_categories.items()]

    def case_workers_filters_options(self, queryset):
        workers = (
            queryset.values(
                "v2_workers__primary_contact_id",
                worker_name=Concat(
                    "v2_workers__primary_contact__first_name", Value(" "), "v2_workers__primary_contact__last_name"
                ),
            )
            .filter(v2_workers__primary_contact__isnull=False)
            .distinct()
            .order_by("worker_name")
        )

        lead_attorneys = (
            queryset.values(
                "v2_workers__lead_attorney_id",
                attorney_name=Concat(
                    "v2_workers__lead_attorney__first_name", Value(" "), "v2_workers__lead_attorney__last_name"
                ),
            )
            .filter(v2_workers__lead_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        # Supervising attorneys
        supervising_attorneys = (
            queryset.values(
                "v2_workers__supervising_attorney_id",
                attorney_name=Concat(
                    "v2_workers__supervising_attorney__first_name",
                    Value(" "),
                    "v2_workers__supervising_attorney__last_name",
                ),
            )
            .filter(v2_workers__supervising_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        lead_attorneys = (
            queryset.values(
                "v2_workers__lead_attorney_id",
                attorney_name=Concat(
                    "v2_workers__lead_attorney__first_name", Value(" "), "v2_workers__lead_attorney__last_name"
                ),
            )
            .filter(v2_workers__lead_attorney__isnull=False)
            .distinct()
            .order_by("attorney_name")
        )

        case_managers = (
            queryset.values(
                "v2_workers__case_manager_id",
                manager_name=Concat(
                    "v2_workers__case_manager__first_name", Value(" "), "v2_workers__case_manager__last_name"
                ),
            )
            .filter(v2_workers__case_manager__isnull=False)
            .distinct()
            .order_by("manager_name")
        )

        return {
            "workers": [
                {"id": worker["v2_workers__primary_contact_id"], "name": worker["worker_name"]} for worker in workers
            ],
            "lead_attorneys": [
                {"id": atty["v2_workers__lead_attorney_id"], "name": atty["attorney_name"]} for atty in lead_attorneys
            ],
            "supervising_attorneys": [
                {"id": atty["v2_workers__supervising_attorney_id"], "name": atty["attorney_name"]}
                for atty in supervising_attorneys
            ],
            "case_managers": [
                {"id": cm["v2_workers__case_manager_id"], "name": cm["manager_name"]} for cm in case_managers
            ],
        }

    def get_case_data(self, case):
        """Format case data for response"""
        start_time = time.time()

        # Convert duration to days
        days = case.days.days if case.days else 0

        # Get workers data
        workers = case.v2_workers if hasattr(case, "v2_workers") else None
        case_name = f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}"
        org_status = case.organization_status.name if case.organization_status else None

        team_start = time.time()
        team = self._get_team_data(workers) if workers else []
        team_time = time.time() - team_start

        result = {
            "case": case_name,
            "days": days,
            "case_type": getattr(case.incident_details, "incident_type", None),
            "status": org_status,
            "primary": workers.primary_contact.get_full_name() if workers and workers.primary_contact else None,
            "statute": case.created_at.strftime("%m/%d/%Y") if case.created_at else None,
            "last_touched": case.last_touched.strftime("%m/%d/%Y") if case.last_touched else None,
            "client_comm": getattr(case, "client_comm", None),
            "doi": case.incident_details.incident_date.strftime("%m/%d/%Y")
            if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date
            else None,
            "team": team,
        }

        total_time = time.time() - start_time
        if total_time > 0.05:  # Log only if it takes more than 50ms
            logger.info(
                f"Slow case formatting - Case ID: {case.id}, Total time: {total_time:.3f}s, Team time: {team_time:.3f}s"
            )

        return result

    def _get_team_data(self, workers):
        """Get formatted team data"""
        team = []
        for role in ["case_manager", "lead_attorney", "case_assistant"]:
            member = getattr(workers, role)
            if member:
                team.append({"id": member.id, "name": member.get_full_name(), "role": role})
        return team

    def get_average_case_age(self, queryset):
        """Calculate average case age in days"""
        avg_timedelta = queryset.aggregate(avg_age=Avg("days"))["avg_age"]
        if avg_timedelta:
            return avg_timedelta.days
        return 0

    def _should_export_csv(self, request):
        """Check if the request is for CSV export"""
        return request.query_params.get("export") == "csv"

    def _get_csv_response(self, filename, data, headers=None):
        """Create a streaming CSV response"""
        if headers is None:
            # If headers is None, data should be a list of dicts
            # Extract headers from dict keys and format them
            # Create ordered dict to maintain consistent header and value order
            ordered_keys = list(data[0].keys())
            headers = [key.replace("_", " ").title() for key in ordered_keys]
            # Convert dict data to list format preserving order
            data = [[row[key] for key in ordered_keys] for row in data]

        pseudo_buffer = Echo()
        writer = csv.writer(pseudo_buffer)

        # Create a streaming response
        response = StreamingHttpResponse((writer.writerow(row) for row in [headers] + data), content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="{filename}"'
        return response

    def get_settlement_filter_options(self, queryset):
        """Get filter options for settlement report"""
        organization = self.request.user.organizations.first()

        # Get case type options
        case_types = (
            Case.objects.filter(organization=organization)
            .values_list("incident_details__incident_type", flat=True)
            .distinct()
        )
        case_types = list(set([ct for ct in case_types if ct]))
        case_type_options = [{"value": ct, "label": ct} for ct in case_types]

        # Get case manager options
        case_managers = User.objects.filter(organizations=organization, managed_cases__isnull=False).distinct()
        case_manager_options = [{"value": cm.id, "label": f"{cm.first_name} {cm.last_name}"} for cm in case_managers]

        # Get lead attorney options
        lead_attorneys = User.objects.filter(organizations=organization, lead_attorney_cases__isnull=False).distinct()
        lead_attorney_options = [{"value": la.id, "label": f"{la.first_name} {la.last_name}"} for la in lead_attorneys]

        # Get lien negotiator options (if applicable)
        lien_negotiators = User.objects.filter(organizations=organization, groups__name="Lien Negotiator").distinct()
        lien_negotiator_options = [
            {"value": ln.id, "label": f"{ln.first_name} {ln.last_name}"} for ln in lien_negotiators
        ]

        # Get source detail options
        source_details = (
            Case.objects.filter(organization=organization)
            .values_list("client_basic_details__source_detail", flat=True)
            .distinct()
        )
        sources = list(set([sd for sd in source_details if sd]))
        source_detail_options = [{"value": sd, "label": sd} for sd in sources]

        # Get source type options
        source_types = (
            Case.objects.filter(organization=organization)
            .values_list("client_basic_details__source_type", flat=True)
            .distinct()
        )
        sources_types = list(set([sd for sd in source_types if sd]))
        source_types_options = [{"value": sd, "label": sd} for sd in sources_types]

        return {
            "case_types": case_type_options,
            "organization_statuses": self.get_case_status_categories(queryset=self.get_base_queryset()),
            "case_managers": case_manager_options,
            "lead_attorneys": lead_attorney_options,
            "lien_negotiators": lien_negotiator_options,
            "source_details": source_detail_options,  # Add source details
            "source_types": source_types_options,  # Add source types
        }

    @action(detail=False, methods=["get"])
    def settlement_management(self, request):
        """
        Return settlement management data for the current organization.
        """
        tile_filter = request.query_params.get("tile")  # Parameter for tile filtering

        if not tile_filter:
            tile_filter = "demands"

        request._request.GET = request._request.GET.copy()
        request._request.GET["kpi_type"] = KPIType.case_open

        case_queryset = self.get_base_queryset()
        case_queryset = self.apply_filter_on_case_queryset(case_queryset)

        # Calculate summary statistics and get filtered queryset based on tile
        summary, return_queryset = self._calculate_settlement_summary(case_queryset, tile_filter)

        view_logger.info(f"return_queryset: {return_queryset}")

        # Format the summary data into tiles
        tiles = [
            {
                "title": "DEMANDS",
                "total": f"${summary['demands']['total']:,.2f}",
                "fees": f"${summary['demands']['fees']:,.2f}",
                "filter_value": "demands",
                "active": tile_filter == "demands",
            },
            {
                "title": "OFFERS",
                "total": f"${summary['offers']['total']:,.2f}",
                "fees": f"${summary['offers']['fees']:,.2f}",
                "filter_value": "offers",
                "active": tile_filter == "offers",
            },
            {
                "title": "PENDING",
                "total": f"${summary['pending']['total']:,.2f}",
                "fees": f"${summary['pending']['fees']:,.2f}",
                "filter_value": "pending",
                "active": tile_filter == "pending",
            },
            {
                "title": "CHECKS IN THIS MONTH",
                "total": f"${summary['checks_this_month']['total']:,.2f}",
                "fees": f"${summary['checks_this_month']['fees']:,.2f}",
                "filter_value": "checks_this_month",
                "active": tile_filter == "checks_this_month",
            },
            {
                "title": "CHECKS IN THIS YEAR",
                "total": f"${summary['checks_this_year']['total']:,.2f}",
                "fees": f"${summary['checks_this_year']['fees']:,.2f}",
                "filter_value": "checks_this_year",
                "active": tile_filter == "checks_this_year",
            },
        ]

        # Get filter options
        filter_options = self._get_settlement_management_filter_options(queryset=self.get_base_queryset())

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size
        page_number = request.query_params.get("page", 1)
        # Calculate next/previous page links
        page_number = int(page_number)

        # Get tile type if available
        tile_type = return_queryset.get("tile_type", None) if isinstance(return_queryset, dict) else None

        # Different pagination handling based on return_queryset type
        if tile_type:
            # For dictionary-based return_querysets (tile filters)
            if tile_type == "demands":
                # Paginate tp_demands and uim_demands separately
                tp_demands = return_queryset.get("tp_demands", [])
                uim_demands = return_queryset.get("uim_demands", [])

                # Calculate total count for pagination info
                count = len(tp_demands) + len(uim_demands)

                # Simple manual pagination for the combined results

                # Format all data first
                all_data = []
                for negotiation in tp_demands:
                    case = negotiation.defendant.case
                    all_data.append(self._format_negotiation_data(case, negotiation, "Third Party"))

                for negotiation in uim_demands:
                    case = negotiation.client_insurance.case
                    all_data.append(self._format_negotiation_data(case, negotiation, "UM / UIM"))

                if self._should_export_csv(request):
                    return self._get_csv_response(f"settlement_management_{tile_type}.csv", all_data)

                start = (int(page_number) - 1) * page_size
                end = start + page_size
                all_data = all_data[start:end]

                next_link = (
                    None if page_number * page_size >= count else f"?page={page_number + 1}&page_size={page_size}"
                )
                previous_link = None if page_number <= 1 else f"?page={page_number - 1}&page_size={page_size}"

                data = {
                    "count": count,
                    "next": next_link,
                    "previous": previous_link,
                    "total_pages": (count + page_size - 1) // page_size,
                    "results": all_data,
                    "summary": summary,
                    "tiles": tiles,
                    "filter_options": filter_options,
                }

                # Then paginate the formatted data

                # Return paginated response
                return Response(data)

            elif tile_type == "offers":
                # Similar approach for offers
                tp_offers = return_queryset.get("tp_offers", [])
                uim_offers = return_queryset.get("uim_offers", [])

                count = len(tp_offers) + len(uim_offers)

                all_data = []
                for negotiation in tp_offers:
                    case = negotiation.defendant.case
                    all_data.append(self._format_negotiation_data(case, negotiation, "Third Party"))

                for negotiation in uim_offers:
                    case = negotiation.client_insurance.case
                    all_data.append(self._format_negotiation_data(case, negotiation, "UM / UIM"))

                if self._should_export_csv(request):
                    return self._get_csv_response(f"settlement_management_{tile_type}.csv", all_data)

                start = (int(page_number) - 1) * page_size
                end = start + page_size
                all_data = all_data[start:end]

                next_link = (
                    None if page_number * page_size >= count else f"?page={page_number + 1}&page_size={page_size}"
                )
                previous_link = None if page_number <= 1 else f"?page={page_number - 1}&page_size={page_size}"

                data = {
                    "count": count,
                    "next": next_link,
                    "previous": previous_link,
                    "total_pages": (count + page_size - 1) // page_size,
                    "results": all_data,
                    "summary": summary,
                    "tiles": tiles,
                    "filter_options": filter_options,
                }

                return Response(data)

            elif tile_type == "pending":
                # Similar approach for pending
                tp_pending = return_queryset.get("tp_pending", [])
                uim_pending = return_queryset.get("uim_pending", [])

                all_data = []
                for negotiation in tp_pending:
                    case = negotiation.defendant.case
                    all_data.append(self._format_negotiation_data(case, negotiation, "Third Party"))

                for negotiation in uim_pending:
                    case = negotiation.client_insurance.case
                    all_data.append(self._format_negotiation_data(case, negotiation, "UM / UIM"))

                if self._should_export_csv(request):
                    return self._get_csv_response(f"settlement_management_{tile_type}.csv", all_data)

                count = len(all_data)
                start = (int(page_number) - 1) * page_size
                end = start + page_size
                all_data = all_data[start:end]
                next_link = (
                    None if page_number * page_size >= count else f"?page={page_number + 1}&page_size={page_size}"
                )
                previous_link = None if page_number <= 1 else f"?page={page_number - 1}&page_size={page_size}"

                data = {
                    "count": count,
                    "next": next_link,
                    "previous": previous_link,
                    "total_pages": (count + page_size - 1) // page_size,
                    "results": all_data,
                    "summary": summary,
                    "tiles": tiles,
                    "filter_options": filter_options,
                }

                return Response(data)

            elif tile_type in ["checks_this_month", "checks_this_year"]:
                # For check listings
                checks = return_queryset.get("checks", [])
                count = len(checks)
                next_link = (
                    None if page_number * page_size >= count else f"?page={page_number + 1}&page_size={page_size}"
                )
                previous_link = None if page_number <= 1 else f"?page={page_number - 1}&page_size={page_size}"

                # Use normal pagination for the checks queryset
                if self._should_export_csv(request):
                    page = paginator.paginate_queryset(checks, request)
                else:
                    page = checks

                if page is not None:
                    settlement_data = []
                    for entry in page:
                        case = entry.case

                        # Format incident date
                        incident_date = "N/A"
                        if (
                            hasattr(case, "incident_details")
                            and case.incident_details
                            and case.incident_details.incident_date
                        ):
                            incident_date = case.incident_details.incident_date.strftime("%m/%d/%Y")

                        # Get case type
                        case_type = "N/A"
                        if hasattr(case, "incident_details") and case.incident_details:
                            case_type = case.incident_details.incident_type or "Third-Party"

                        # Format settlement check data

                        if entry.demand_type and entry.demand_type == "THIRD_PARTY":
                            case_negotiation = CaseNegotiation.objects.get(id=entry.third_party_negotiation.id)
                        elif entry.demand_type and entry.demand_type == "UIM":
                            case_negotiation = CaseNegotiationUIM.objects.get(id=entry.uim_negotiation.id)
                        else:
                            case_negotiation = None

                        fee_entry = ClientTrust.objects.filter(
                            case=case,
                            client_trust_entry_type="ATTORNEY_CHECK",
                            uim_negotiation_id=case_negotiation.id
                            if case_negotiation and entry.demand_type == "UIM"
                            else None,
                            third_party_negotiation_id=case_negotiation.id
                            if case_negotiation and entry.demand_type == "THIRD_PARTY"
                            else None,
                        ).first()

                        has_fee_taken = fee_entry is not None

                        settlement_data.append(
                            {
                                "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {incident_date} : {case.id}",
                                "demand_type": entry.demand_type,
                                "amount": float(entry.amount or 0),
                                "est_fees": float(case_negotiation.amount or 0) * 0.33,  # 33% fee estimate
                                "fee_taken": fee_entry.amount if has_fee_taken else 0,
                                "statute": case.incident_details.statute_of_limitations
                                if hasattr(case, "incident_details") and case.incident_details
                                else "N/A",
                                "insurance": entry.issuer_payee if hasattr(entry, "issuer_payee") else "N/A",
                                "deposit_date": entry.deposit_date.strftime("%m/%d/%Y")
                                if entry.deposit_date
                                else "N/A",
                                "check_number": entry.check_number,
                                "memo": entry.memo,
                            }
                        )

                    if self._should_export_csv(request):
                        return self._get_csv_response(f"settlement_management_{tile_type}.csv", settlement_data)
                    return Response(
                        {
                            "count": count,
                            "next": next_link,
                            "previous": previous_link,
                            "total_pages": (count + page_size - 1) // page_size,
                            "summary": summary,
                            "tiles": tiles,
                            "filter_options": filter_options,
                            "results": settlement_data,
                        }
                    )

    def _get_settlement_management_filter_options(self, queryset):
        result = {
            "organization_statuses": self.get_case_status_categories(queryset),
            **self.case_workers_filters_options(queryset),
        }
        return result

    def _calculate_settlement_summary(self, case_queryset, tile_filter):
        """Calculate summary statistics for settlement management report using negotiations data"""
        # Initialize summary values
        demands_total = Decimal(0)
        demands_fees = Decimal(0)
        offers_total = Decimal(0)
        offers_fees = Decimal(0)
        pending_total = Decimal(0)
        pending_fees = Decimal(0)
        checks_this_month_total = Decimal(0)
        checks_this_month_fees = Decimal(0)
        checks_this_year_total = Decimal(0)
        checks_this_year_fees = Decimal(0)

        # Current month and year for date filtering
        current_month = timezone.now().month
        current_year = timezone.now().year

        # Get the case IDs from the filtered queryset
        case_ids = case_queryset.values_list("id", flat=True)

        # Initialize return querysets for each tile type
        demands_queryset = None
        offers_queryset = None
        pending_queryset = None
        checks_this_month_queryset = None
        checks_this_year_queryset = None

        # DEMANDS tile: Get negotiations that are demands/counter offers
        tp_demands = (
            CaseNegotiation.objects.filter(
                defendant__case_id__in=case_ids,
                type__in=["INITIAL_DEMAND", "COUNTER_OFFER"],
                status="SENT",
                is_archived=False,
            )
            .exclude(id__in=CaseNegotiation.objects.filter(previous_offer__isnull=False).values("previous_offer_id"))
            .select_related(
                "defendant__case", "defendant__case__client_basic_details", "defendant__case__incident_details"
            )
        )

        view_logger.info(f"tp_demands: {tp_demands}")

        uim_demands = (
            CaseNegotiationUIM.objects.filter(
                client_insurance__case_id__in=case_ids,
                type__in=["INITIAL_DEMAND", "COUNTER_OFFER"],
                status="SENT",
                is_archived=False,
            )
            .exclude(id__in=CaseNegotiationUIM.objects.filter(previous_offer__isnull=False).values("previous_offer_id"))
            .select_related(
                "client_insurance__case",
                "client_insurance__case__client_basic_details",
                "client_insurance__case__incident_details",
            )
        )

        # Calculate totals for demands
        demands_total = sum(tp_demands.values_list("amount", flat=True)) + sum(
            uim_demands.values_list("amount", flat=True)
        )
        demands_fees = demands_total * Decimal("0.33")  # 33% fee estimate

        # OFFERS tile: Get negotiations that are offers
        tp_offers = (
            CaseNegotiation.objects.filter(
                defendant__case_id__in=case_ids,
                type__in=["OFFER", "ACCEPTED_OFFER"],
                status="RECEIVED",
                is_archived=False,
            )
            .exclude(id__in=CaseNegotiation.objects.filter(previous_offer__isnull=False).values("previous_offer_id"))
            .select_related(
                "defendant__case", "defendant__case__client_basic_details", "defendant__case__incident_details"
            )
        )

        uim_offers = (
            CaseNegotiationUIM.objects.filter(
                client_insurance__case_id__in=case_ids,
                type__in=["OFFER"],
                status="RECEIVED",
                is_archived=False,
            )
            .exclude(id__in=CaseNegotiationUIM.objects.filter(previous_offer__isnull=False).values("previous_offer_id"))
            .select_related(
                "client_insurance__case",
                "client_insurance__case__client_basic_details",
                "client_insurance__case__incident_details",
            )
        )

        # Calculate totals for offers
        offers_total = sum(tp_offers.values_list("amount", flat=True)) + sum(
            uim_offers.values_list("amount", flat=True)
        )
        offers_fees = offers_total * Decimal("0.33")  # 33% fee estimate

        # PENDING tile: Get accepted offers without settlement checks
        tp_pending = CaseNegotiation.objects.filter(
            defendant__case_id__in=case_ids, type="ACCEPTED_OFFER", amount__gt=0, status="RECEIVED", is_archived=False
        ).select_related(
            "defendant__case", "defendant__case__client_basic_details", "defendant__case__incident_details"
        )

        uim_pending = CaseNegotiationUIM.objects.filter(
            client_insurance__case_id__in=case_ids,
            type="ACCEPTED_OFFER",
            amount__gt=0,
            status="RECEIVED",
            is_archived=False,
        ).select_related(
            "client_insurance__case",
            "client_insurance__case__client_basic_details",
            "client_insurance__case__incident_details",
        )

        # Get case IDs that have settlement checks
        cases_with_checks = (
            ClientTrust.objects.filter(case_id__in=case_ids, client_trust_entry_type="SETTLEMENT_CHECK")
            .values_list("case_id", flat=True)
            .distinct()
        )

        # Filter pending negotiations to only include those without settlement checks
        tp_pending = tp_pending.exclude(defendant__case_id__in=cases_with_checks)
        uim_pending = uim_pending.exclude(client_insurance__case_id__in=cases_with_checks)

        # Calculate totals for pending
        pending_total = sum(tp_pending.values_list("amount", flat=True)) + sum(
            uim_pending.values_list("amount", flat=True)
        )
        pending_fees = pending_total * Decimal("0.33")  # 33% fee estimate

        # CHECKS tiles: Get settlement check client trust entries
        checks_this_month_queryset = (
            ClientTrust.objects.filter(
                case_id__in=case_ids,
                client_trust_entry_type="SETTLEMENT_CHECK",
                deposit_date__month=current_month,
                deposit_date__year=current_year,
                demand_type__in=["THIRD_PARTY", "UIM"],
            )
            .filter(Q(third_party_negotiation__isnull=False) | Q(uim_negotiation__isnull=False))
            .select_related("case", "case__client_basic_details", "case__incident_details")
        )

        checks_this_year_queryset = (
            ClientTrust.objects.filter(
                case_id__in=case_ids,
                client_trust_entry_type="SETTLEMENT_CHECK",
                deposit_date__year=current_year,
                demand_type__in=["THIRD_PARTY", "UIM"],
            )
            .filter(Q(third_party_negotiation__isnull=False) | Q(uim_negotiation__isnull=False))
            .select_related("case", "case__client_basic_details", "case__incident_details")
        )

        # Calculate totals for checks
        checks_this_month_total = sum(checks_this_month_queryset.values_list("amount", flat=True))
        checks_this_month_fees = checks_this_month_total * Decimal("0.33")  # 33% fee estimate

        checks_this_year_total = sum(checks_this_year_queryset.values_list("amount", flat=True))
        checks_this_year_fees = checks_this_year_total * Decimal("0.33")  # 33% fee estimate

        # Determine which queryset to return based on tile filter
        return_queryset = case_queryset  # Default

        if tile_filter == "demands":
            # Return both TP and UIM demands using a union
            # Since they're different models, we need to handle them differently
            return_queryset = {"tp_demands": tp_demands, "uim_demands": uim_demands, "tile_type": "demands"}
        elif tile_filter == "offers":
            return_queryset = {"tp_offers": tp_offers, "uim_offers": uim_offers, "tile_type": "offers"}
        elif tile_filter == "pending":
            return_queryset = {"tp_pending": tp_pending, "uim_pending": uim_pending, "tile_type": "pending"}
        elif tile_filter == "checks_this_month":
            return_queryset = {"checks": checks_this_month_queryset, "tile_type": "checks_this_month"}
        elif tile_filter == "checks_this_year":
            return_queryset = {"checks": checks_this_year_queryset, "tile_type": "checks_this_year"}

        # Format the summary
        summary = {
            "demands": {"total": float(demands_total), "fees": float(demands_fees)},
            "offers": {"total": float(offers_total), "fees": float(offers_fees)},
            "issues": {"total": 0, "fees": 0},  # Placeholder for issues tile
            "pending": {"total": float(pending_total), "fees": float(pending_fees)},
            "checks_this_month": {
                "total": float(checks_this_month_total),
                "fees": float(checks_this_month_fees),
            },
            "checks_this_year": {
                "total": float(checks_this_year_total),
                "fees": float(checks_this_year_fees),
            },
        }

        return summary, return_queryset

    def _format_settlement_management_data(self, return_queryset):
        """Format settlement data for the management report using negotiations data"""
        settlement_data = []

        # Handle different return_queryset structures based on tile type
        tile_type = return_queryset.get("tile_type", None)

        if tile_type == "demands":
            # Process TP demands
            for negotiation in return_queryset.get("tp_demands", []):
                case = negotiation.defendant.case
                settlement_data.append(self._format_negotiation_data(case, negotiation, "TP Demand"))

            # Process UIM demands
            for negotiation in return_queryset.get("uim_demands", []):
                case = negotiation.client_insurance.case
                settlement_data.append(self._format_negotiation_data(case, negotiation, "UIM Demand"))

        elif tile_type == "offers":
            # Process TP offers
            for negotiation in return_queryset.get("tp_offers", []):
                case = negotiation.defendant.case
                settlement_data.append(self._format_negotiation_data(case, negotiation, "TP Offer"))

            # Process UIM offers
            for negotiation in return_queryset.get("uim_offers", []):
                case = negotiation.client_insurance.case
                settlement_data.append(self._format_negotiation_data(case, negotiation, "UIM Offer"))

        elif tile_type == "pending":
            # Process TP pending
            for negotiation in return_queryset.get("tp_pending", []):
                case = negotiation.defendant.case
                settlement_data.append(self._format_negotiation_data(case, negotiation, "TP Pending"))

            # Process UIM pending
            for negotiation in return_queryset.get("uim_pending", []):
                case = negotiation.client_insurance.case
                settlement_data.append(self._format_negotiation_data(case, negotiation, "UIM Pending"))

        elif tile_type in ["checks_this_month", "checks_this_year"]:
            # Process settlement checks
            for entry in return_queryset.get("checks", []):
                case = entry.case

                # Format incident date
                incident_date = "N/A"
                if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
                    incident_date = case.incident_details.incident_date.strftime("%m/%d/%Y")

                # Get case type
                case_type = "N/A"
                if hasattr(case, "incident_details") and case.incident_details:
                    case_type = case.incident_details.incident_type or "Third-Party"

                # Format settlement check data
                settlement_data.append(
                    {
                        "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {incident_date} : {case.id}",
                        "type": case_type,
                        "statute": case.incident_details.statute_of_limitations
                        if hasattr(case, "incident_details") and case.incident_details
                        else "N/A",
                        "insurance": entry.meta_data.get("insurance", "N/A") if hasattr(entry, "meta_data") else "N/A",
                        "policy": entry.meta_data.get("policy", None) if hasattr(entry, "meta_data") else None,
                        "deposit_date": entry.deposit_date.strftime("%m/%d/%Y") if entry.deposit_date else "N/A",
                        "amount": float(entry.amount or 0),
                        "est_fees": float(entry.amount or 0) * 0.33,  # 33% fee estimate
                    }
                )
        else:
            # Default case handling (when no specific tile is selected)
            for case in return_queryset:
                # Get insurance information
                insurance_info = None
                policy_info = None
                try:
                    insurance_info = (
                        case.insurances.first().insurance_company.name if case.insurances.first() else "N/A"
                    )
                    policy_info = case.insurances.first().policy_number if case.insurances.first() else None
                except:
                    pass

                # Format incident date
                incident_date = "N/A"
                if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
                    incident_date = case.incident_details.incident_date.strftime("%m/%d/%Y")

                # Get case type
                case_type = "N/A"
                if hasattr(case, "incident_details") and case.incident_details:
                    case_type = case.incident_details.incident_type or "Third-Party"

                # Calculate total settlement amount from latest negotiations
                # This code is unchanged from original
                latest_tp_negotiations = (
                    case.v2_defendants.filter(negotiations__isnull=False)
                    .annotate(
                        latest_amount=Subquery(
                            CaseNegotiation.objects.filter(defendant_id=OuterRef("id"))
                            .order_by("-created_at")
                            .values("amount")[:1]
                        )
                    )
                    .aggregate(total=Coalesce(Sum("latest_amount"), Decimal(0)))["total"]
                )

                latest_uim_negotiations = (
                    case.insurances.filter(negotiations_uim__isnull=False)
                    .annotate(
                        latest_amount=Subquery(
                            CaseNegotiationUIM.objects.filter(client_insurance_id=OuterRef("id"))
                            .order_by("-created_at")
                            .values("amount")[:1]
                        )
                    )
                    .aggregate(total=Coalesce(Sum("latest_amount"), Decimal(0)))["total"]
                )

                total_amount = float(latest_tp_negotiations + latest_uim_negotiations)
                est_fees = total_amount * 0.33

                # Get medical bills from settlement calculation if available
                total_meds = 0
                try:
                    settlement_calc = case.settlement_calculations.first()
                    if settlement_calc:
                        total_meds = float(settlement_calc.medical_bills or 0)
                except:
                    pass

                settlement_data.append(
                    {
                        "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {incident_date} : {case.id}",
                        "type": case_type,
                        "statute": case.incident_details.statute_of_limitations
                        if hasattr(case, "incident_details") and case.incident_details
                        else "N/A",
                        "insurance": insurance_info,
                        "policy": policy_info,
                        "total_meds": total_meds,
                        "amount": total_amount,
                        "est_fees": est_fees,
                    }
                )

        return settlement_data

    def _format_negotiation_data(self, case, negotiation, negotiation_type):
        """Helper method to format negotiation data consistently"""
        # Format incident date
        incident_date = "N/A"
        if hasattr(case, "incident_details") and case.incident_details and case.incident_details.incident_date:
            incident_date = case.incident_details.incident_date.strftime("%m/%d/%Y")

        # Get case type
        case_type = "N/A"
        if hasattr(case, "incident_details") and case.incident_details:
            case_type = case.incident_details.incident_type or "Third-Party"

        # Get insurance information
        insurance_info = None
        policy_info = None

        if "UIM" in negotiation_type:
            # For UIM negotiations, get insurance from the client_insurance
            try:
                insurance_info = negotiation.client_insurance.insurance_company.name
                policy_info = negotiation.client_insurance.policy_number
            except:
                insurance_info = "N/A"
        else:
            # For TP negotiations, get insurance from defendant or case
            try:
                if (
                    hasattr(negotiation, "defendant")
                    and negotiation.defendant
                    and hasattr(negotiation.defendant, "insurance")
                ):
                    insurance_info = negotiation.defendant.insurance
                else:
                    insurance_info = (
                        case.insurances.first().insurance_company.name if case.insurances.first() else "N/A"
                    )
                    policy_info = case.insurances.first().policy_number if case.insurances.first() else None
            except:
                insurance_info = "N/A"

        # Get medical bills if available
        total_meds = 0
        try:
            total_meds = TreatmentProvider.objects.filter(case=case).aggregate(total_meds=Sum("original_bill"))[
                "total_meds"
            ]
        except:
            pass

        # Format and return negotiation data
        return {
            "case": f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {incident_date} : {case.id}",
            "statute": case.incident_details.statute_of_limitations
            if hasattr(case, "incident_details") and case.incident_details
            else "N/A",
            "amount": float(negotiation.amount or 0),
            "est_fees": float(negotiation.amount or 0) * 0.33,  # 33% fee estimate
            "insurance": insurance_info,
            "policy": policy_info,
            "total_meds": total_meds,
            "demand_type": negotiation_type,
            "created_date": negotiation.created_at.strftime("%m/%d/%Y") if negotiation.created_at else "N/A",
            "status": negotiation.status,
            "notes": negotiation.notes,
        }

    def _generate_settlement_management_csv(self, queryset):
        """Generate CSV export for settlement management report"""
        # Format all settlements for CSV export that match the current filters
        all_settlement_data = self._format_settlement_management_data(queryset)

        # Prepare CSV data
        headers = [
            "Case",
            "Type",
            "Statute",
            "Insurance",
            "Policy",
            "Total Meds",
            "Amount",
            "Est. Fees",
        ]

        csv_data = []
        for item in all_settlement_data:
            csv_data.append(
                [
                    item.get("case", ""),
                    item.get("type", ""),
                    item.get("statute", ""),
                    item.get("insurance", ""),
                    item.get("policy", ""),
                    item.get("total_meds", ""),
                    item.get("amount", ""),
                    item.get("est_fees", ""),
                ]
            )

        return self._get_csv_response("settlement_management.csv", csv_data, headers)

    @action(detail=False, methods=["get"])
    def litigation_management(self, request):
        """
        Return litigation management data for the current organization.
        """
        organization = self.request.user.organizations.first()

        # Get filter parameters
        assigned_worker = request.query_params.get("assigned_worker")
        event_type = request.query_params.get("event_type")
        status_filter = request.query_params.get("status", "open_pending")  # default to open & pending
        search_query = request.query_params.get("search")
        tile_filter = request.query_params.get("tile")  # New parameter for tile filtering

        # If a tile is clicked, it overrides the status_filter
        if tile_filter:
            if tile_filter == "open_pending":
                status_filter = "open_pending"
            elif tile_filter == "complete":
                status_filter = "complete"

        # Start with all litigation events for the organization
        queryset = CaseEvent.objects.filter(case__organization=organization).select_related(
            "case",
            "case__client_basic_details",
            "case__incident_details",
            "case__v2_workers__lead_attorney",
        )

        if assigned_worker:
            queryset = queryset.filter(
                Q(case__v2_workers__lead_attorney_id=assigned_worker)
                | Q(case__v2_workers__litigation_attorney_id=assigned_worker)
                | Q(assigned_to_id=assigned_worker)
            )

        if event_type:
            queryset = queryset.filter(event_type=event_type)

        # Apply search
        if search_query:
            queryset = queryset.filter(
                Q(case__client_basic_details__first_name__icontains=search_query)
                | Q(case__client_basic_details__last_name__icontains=search_query)
                | Q(case__id__icontains=search_query)
                | Q(title__icontains=search_query)
                | Q(description__icontains=search_query)
            ).distinct()

        # Calculate summary statistics for the current filtered results
        summary = self._calculate_litigation_summary(queryset, status_filter)

        # Calculate the counts for both open/pending and complete events
        all_events = CaseEvent.objects.filter(case__organization=organization)
        open_pending_count = all_events.filter(Q(status__isnull=True) | ~Q(status="COMPLETED")).count()

        now = timezone.now()
        if status_filter == "open_pending":
            new_queryset = queryset.filter(
                Q(date__gt=now.date())  # Future date
                | Q(date=now.date(), end_time__gt=now.time())  # Today but end time not passed
            )

        elif status_filter == "complete":
            new_queryset = queryset.filter(
                Q(date__lt=now.date())  # Past date
                | Q(date=now.date(), end_time__lte=now.time())  # Today and end time passed
            )

        open_pending_count = queryset.filter(
            Q(date__gt=now.date())  # Future date
            | Q(date=now.date(), end_time__gt=now.time())  # Today but end time not passed
        ).count()
        complete_count = queryset.filter(
            Q(date__lt=now.date())  # Past date
            | Q(date=now.date(), end_time__lte=now.time())  # Today and end time passed
        ).count()

        # Format the summary data into tiles
        tiles = [
            {
                "title": "OPEN & PENDING",
                "event_count": open_pending_count,
                "filter_value": "open_pending",
                "active": status_filter == "open_pending",
            },
            {
                "title": "COMPLETE",
                "event_count": complete_count,
                "filter_value": "complete",
                "active": status_filter == "complete",
            },
        ]

        # Get filter options
        filter_options = self._get_litigation_management_filter_options(
            base_queryset=self.get_base_queryset(), queryset=queryset
        )

        # Handle pagination
        page_size = int(request.query_params.get("page_size", self.pagination_class.page_size))
        paginator = self.paginator
        paginator.page_size = page_size

        if not self._should_export_csv(request):
            page = paginator.paginate_queryset(new_queryset, request)
        else:
            page = new_queryset

        if page is not None:
            litigation_data = self._format_litigation_management_data(page)

            if self._should_export_csv(request):
                return self._get_csv_response("litigation_management.csv", litigation_data)

            return paginator.get_paginated_response(
                {"summary": summary, "tiles": tiles, "filter_options": filter_options, "results": litigation_data}
            )

        # Format all results (no pagination)
        litigation_data = self._format_litigation_management_data(new_queryset)
        return Response(
            {"summary": summary, "tiles": tiles, "filter_options": filter_options, "results": litigation_data}
        )

    def _calculate_litigation_summary(self, queryset, status_filter):
        """Calculate summary statistics for litigation management report"""
        # Get total counts from the current filtered queryset
        current_count = queryset.count()

        now = timezone.now()
        if status_filter == "open_pending":
            queryset = queryset.filter(
                Q(date__gt=now.date())  # Future date
                | Q(date=now.date(), end_time__gt=now.time())  # Today but end time not passed
            )
        elif status_filter == "complete":
            queryset = queryset.filter(
                Q(date__lt=now.date())  # Past date
                | Q(date=now.date(), end_time__lte=now.time())  # Today and end time passed
            )

        return {
            "open_pending": current_count if status_filter == "open_pending" else 0,
            "complete": current_count if status_filter == "complete" else 0,
            "current_tab": status_filter,
            "current_count": current_count,
        }

    def _get_litigation_management_filter_options(self, base_queryset, queryset):
        """Get filter options for litigation management report"""
        # Get event types
        event_types = queryset.values_list("event_type", flat=True).distinct()
        event_types = list(set([et for et in event_types if et]))

        return {
            "event_types": [{"value": et, "label": et} for et in event_types],
            **self.case_workers_filters_options(base_queryset),
            "organization_statuses": self.get_case_status_categories(base_queryset),
        }

    def _format_litigation_management_data(self, events):
        """Format litigation data for the management report"""
        litigation_data = []

        for event in events:
            case = event.case

            # Format start/end dates
            if event.date:
                date_str = event.date.strftime("%m/%d/%Y")
                if event.start_time and event.end_time:
                    date_str += f" {event.start_time.strftime('%I:%M %p')} - {event.end_time.strftime('%I:%M %p')}"
            else:
                date_str = ""

            # Get case type
            case_type = None
            if hasattr(case, "incident_details") and case.incident_details:
                case_type = case.incident_details.incident_type or None

            # Format case name
            case_name = f"{case.client_basic_details.last_name}, {case.client_basic_details.first_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')} : {case.id}"
            address = event.address or None

            litigation_data.append(
                {
                    "case": case_name,
                    "date": date_str,
                    "case_type": case_type,
                    "event": event.event_type or "Deadline",
                    "description": event.description or "",
                    "address": address,
                    "case_name": case.case_name if hasattr(case, "case_name") else case_name,
                    "status": event.status or "PENDING",
                }
            )

        return litigation_data

    def _get_status_indicator(self, event):
        return event.status
