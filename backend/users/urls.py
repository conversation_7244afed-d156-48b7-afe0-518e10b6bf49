from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    AcceptOrganizationInviteView,
    ChangePasswordView,
    CustomTokenObtainPairView,
    ForgotPasswordView,
    OneDriveViewSet,
    OrganizationDetailView,
    OrganizationInviteViewSet,
    OrganizationUsersView,
    RemoveOrganizationUserView,
    ResetPasswordView,
    TerminateUserView,
    UpdateOrganizationUserView,
    UpdateUserTagsView,
    UserProfileView,
    UserTagViewSet,
    ValidateEmailVerificationView,
    VerifyTokenView,
)
from .views_onedrive_personal import OneDrivePersonalViewSet

# Create a router for organization invites viewset
router = DefaultRouter()
router.register(r"organizations/current/invites", OrganizationInviteViewSet, basename="organization-invites")
router.register(r"organizations/current/onedrive", OneDriveViewSet, basename="organization-onedrive")
router.register(
    r"organizations/current/onedrive-personal", OneDrivePersonalViewSet, basename="organization-onedrive-personal"
)
router.register(r"organizations/current/user-tags", UserTagViewSet, basename="user-tags")
# Original URLs
urlpatterns = [
    # Authentication endpoints
    path("login/", CustomTokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    # Optional: endpoint to get user's own profile
    path("users/me/", UserProfileView.as_view(), name="user_profile"),
    path("change-password/", ChangePasswordView.as_view(), name="change_password"),
    path("verify/", VerifyTokenView.as_view(), name="token_verify"),
    # Password reset endpoints
    path("forgot-password/", ForgotPasswordView.as_view(), name="forgot_password"),
    path("reset-password", ResetPasswordView.as_view(), name="reset_password"),
    path(
        "validate-email/<uidb64>/<token>/",
        ValidateEmailVerificationView.as_view(),
        name="validate_email",
    ),
    path("nylas/", include("users.nylas_integration.urls", namespace="nylas")),
    path("twilio/", include("users.twilio_integration.urls", namespace="twilio")),
    # New Organization Management URLs
    path("organizations/current/", OrganizationDetailView.as_view(), name="organization-detail"),
    path("organizations/current/users/", OrganizationUsersView.as_view(), name="organization-users"),
    path(
        "organizations/current/users/<int:pk>/",
        RemoveOrganizationUserView.as_view(),
        name="remove-organization-user",
    ),
    path("organizations/current/users/<int:pk>/tags/", UpdateUserTagsView.as_view(), name="update-user-tags"),
    path(
        "organizations/current/users/<int:pk>/update/",
        UpdateOrganizationUserView.as_view(),
        name="update-organization-user",
    ),
    path(
        "organizations/invites/<uuid:token>/",
        AcceptOrganizationInviteView.as_view(),
        name="accept-organization-invite",
    ),
    # Include organization invites router URLs
    path("", include(router.urls)),
    path("organizations/current/", include("users.communication_templates.urls")),
    path("terminate-user/", TerminateUserView.as_view(), name="terminate-user"),
]
