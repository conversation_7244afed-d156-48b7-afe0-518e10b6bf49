from django.contrib.auth.password_validation import validate_password
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from storage_service.services.factory import StorageType

from .models import (
    Organization,
    OrganizationInvite,
    OrganizationProfile,
    User,
    UserEmailSignature,
    UserTag,
)


class UserBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "first_name", "last_name"]
        read_only_fields = ["id", "email", "first_name", "last_name"]


class UserSerializer(serializers.ModelSerializer):
    organizations = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            "id",
            "name",
            "email",
            "role",
            "subscription_type",
            "organizations",
            "verified",
        )
        read_only_fields = (
            "id",
            "email",
            "role",
            "subscription_type",
            "organizations",
            "verified",
        )
        ref_name = "User_users"

    def get_organizations(self, obj):
        # Get the first organization since users can only be in one organization
        organization = obj.organizations.first()
        if organization:
            return {
                "id": str(organization.id),  # Convert UUID to string
                "name": organization.name,
            }
        return None

    def get_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()

    def update(self, instance, validated_data):
        # Split name into first_name and last_name if provided
        name = validated_data.pop("name", None)
        if name:
            name_parts = name.split(" ", 1)
            instance.first_name = name_parts[0]
            instance.last_name = name_parts[1] if len(name_parts) > 1 else ""

        return super().update(instance, validated_data)


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        # Convert email to lowercase before validation
        if "email" in attrs:
            attrs["email"] = attrs["email"].lower()

        data = super().validate(attrs)

        # Get the token
        token = self.get_token(self.user)

        # Add custom claims to the token payload
        token["name"] = f"{self.user.first_name} {self.user.last_name}"
        token["email"] = self.user.email
        token["user_id"] = str(self.user.id)  # Convert UUID to string
        token["role"] = self.user.role
        token["subscription_type"] = self.user.subscription_type
        token["verified"] = self.user.verified
        token["tags"] = [tag.name for tag in self.user.tags.all()]

        # Add organization details
        organization = self.user.organizations.first()
        if organization:
            token["organization"] = {
                "id": str(organization.id),  # Convert UUID to string
                "name": organization.name,
            }

        # Update the response data
        data["access"] = str(token.access_token)
        data["refresh"] = str(token)

        # Add the same claims to the response data for consistency
        data["name"] = token["name"]
        data["email"] = token["email"]
        data["user_id"] = token["user_id"]
        data["role"] = token["role"]
        data["subscription_type"] = token["subscription_type"]
        data["verified"] = token["verified"]
        data["tags"] = token["tags"]
        if organization:
            data["organization"] = token["organization"]

        return data


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["confirm_password"]:
            raise serializers.ValidationError({"error": "Password fields didn't match."})

        # Validate password strength
        try:
            validate_password(attrs["new_password"])
        except Exception as e:
            raise serializers.ValidationError({"error": list(e)})

        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    organization = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "username",
            "first_name",
            "last_name",
            "role",
            "verified",
            "date_joined",
            "last_login",
            "organization",
        ]
        read_only_fields = [
            "id",
            "email",
            "username",
            "role",
            "verified",
            "date_joined",
            "last_login",
            "organization",
        ]

    def get_organization(self, obj):
        organization = obj.organizations.first()
        if organization:
            return {"id": organization.id, "name": organization.name}
        return None

    def update(self, instance, validated_data):
        # Update first_name and last_name if provided
        if "first_name" in validated_data:
            instance.first_name = validated_data["first_name"]
        if "last_name" in validated_data:
            instance.last_name = validated_data["last_name"]
        instance.save()
        return instance


class ResetPasswordEmailSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        # Convert email to lowercase before validation
        return value.lower() if value else None


class ResetPasswordConfirmSerializer(serializers.Serializer):
    new_password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, data):
        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError("Passwords do not match.")
        return data


class OrganizationProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrganizationProfile
        fields = ["address", "phone", "email", "state", "signature"]


class OrganizationSerializer(serializers.ModelSerializer):
    profile = OrganizationProfileSerializer()

    class Meta:
        model = Organization
        fields = ["id", "name", "description", "profile"]

    def update(self, instance, validated_data):
        profile_data = validated_data.pop("profile", {})
        # Update organization fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update profile fields
        profile = instance.profile
        for attr, value in profile_data.items():
            setattr(profile, attr, value)
        profile.save()

        return instance


class OrganizationUserSerializer(serializers.ModelSerializer):
    tags = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "role",
            "is_active",
            "last_login",
            "date_joined",
            "tags",
        ]

    def get_tags(self, obj):
        # Force evaluation and ordering of tags
        return [{"id": tag.id, "name": tag.name} for tag in obj.tags.all().order_by("id")]


class UpdateOrganizationUserSerializer(serializers.ModelSerializer):
    """Serializer for updating organization user details by admin"""

    class Meta:
        model = User
        fields = [
            "first_name",
            "last_name",
            "role",
            "is_active",
        ]

    def validate_role(self, value):
        """Validate that the role is one of the allowed choices"""
        valid_roles = [choice[0] for choice in User.ROLE_CHOICES]
        if value not in valid_roles:
            raise serializers.ValidationError(f"Invalid role. Must be one of: {', '.join(valid_roles)}")
        return value

    def validate(self, attrs):
        """Additional validation to prevent certain changes"""
        instance = self.instance
        new_role = attrs.get("role")
        new_is_active = attrs.get("is_active")

        # Prevent changing admin role if this is the only admin in the organization
        if instance and instance.role == "admin" and new_role is not None and new_role != "admin":
            organization = instance.organizations.first()
            if organization:
                admin_count = organization.users.filter(role="admin").count()
                if admin_count <= 1:
                    raise serializers.ValidationError("Cannot change role of the only admin user in the organization")

        # Prevent deactivating the only admin in the organization
        if instance and instance.role == "admin" and new_is_active is False:
            organization = instance.organizations.first()
            if organization:
                active_admin_count = organization.users.filter(role="admin", is_active=True).count()
                if active_admin_count <= 1:
                    raise serializers.ValidationError(
                        "Cannot deactivate the only active admin user in the organization"
                    )

        return attrs


class OrganizationInviteSerializer(serializers.ModelSerializer):
    invited_by_email = serializers.EmailField(source="invited_by.email", read_only=True)
    organization_name = serializers.CharField(source="organization.name", read_only=True)

    class Meta:
        model = OrganizationInvite
        fields = [
            "id",
            "email",
            "role",
            "status",
            "created_at",
            "expires_at",
            "invited_by_email",
            "organization_name",
        ]
        read_only_fields = [
            "status",
            "created_at",
            "expires_at",
            "invited_by_email",
            "organization_name",
        ]

    def create(self, validated_data):
        # Add organization and invited_by from context
        validated_data["organization"] = self.context["organization"]
        validated_data["invited_by"] = self.context["invited_by"]
        return super().create(validated_data)

    def validate_email(self, value):
        # Convert email to lowercase before validation
        return value.lower() if value else None


class OneDriveStorageSerializer(serializers.ModelSerializer):
    """Serializer for OneDrive storage settings"""

    class Meta:
        model = Organization
        fields = [
            "storage_type",
            "onedrive_access_token",
            "onedrive_refresh_token",
            "onedrive_token_expires_at",
        ]
        read_only_fields = [
            "onedrive_access_token",
            "onedrive_refresh_token",
            "onedrive_token_expires_at",
        ]

    def validate_storage_type(self, value):
        """Validate storage type"""
        if value not in [choice[0] for choice in StorageType.CHOICES]:
            raise serializers.ValidationError(
                f"Invalid storage type. Must be one of: {', '.join([choice[0] for choice in StorageType.CHOICES])}"
            )
        return value


class UserTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserTag
        fields = [
            "id",
            "name",
            "description",
            "is_default",
            "platform_sections",
            "platform_subsections",
            "admin_sections",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["is_default", "created_at", "updated_at"]

    def validate_platform_sections(self, value):
        """Validate platform sections against the enum"""
        from utils.common.platform_section_enums import PlatformSection

        valid_sections = [section.value for section in PlatformSection]
        invalid_sections = set(value) - set(valid_sections)
        if invalid_sections:
            raise serializers.ValidationError(f"Invalid platform sections: {invalid_sections}")
        return value

    def validate_platform_subsections(self, value):
        """Validate platform subsections against the enum"""
        from utils.common.platform_section_enums import PlatformSection, get_section_subsections

        valid_sections = [section.value for section in PlatformSection]

        for section, subsections in value.items():
            if section not in valid_sections:
                raise serializers.ValidationError(f"Invalid section in subsections: {section}")

            # Get valid subsections for this section
            valid_subsections = [s.value for s in get_section_subsections(PlatformSection(section))]
            invalid_subsections = set(subsections) - set(valid_subsections)
            if invalid_subsections:
                raise serializers.ValidationError(f"Invalid subsections for {section}: {invalid_subsections}")

        return value

    def validate_admin_sections(self, value):
        """Validate admin sections against the enum"""
        from utils.common.platform_section_enums import AdminSubsection

        valid_sections = [section.value for section in AdminSubsection]
        invalid_sections = set(value) - set(valid_sections)
        if invalid_sections:
            raise serializers.ValidationError(f"Invalid admin sections: {invalid_sections}")
        return value


class UserEmailSignatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserEmailSignature
        fields = ["id", "name", "content", "is_default", "created_at", "updated_at"]
        read_only_fields = ["created_at", "updated_at"]

    def create(self, validated_data):
        # Set the user from the context
        validated_data["user"] = self.context["request"].user

        # If this signature is being set as default, unset others
        if validated_data.get("is_default", False):
            UserEmailSignature.objects.filter(user=validated_data["user"]).update(is_default=False)

        return super().create(validated_data)

    def update(self, instance, validated_data):
        # If this signature is being set as default, unset others
        if validated_data.get("is_default", False) and not instance.is_default:
            UserEmailSignature.objects.filter(user=instance.user).update(is_default=False)

        return super().update(instance, validated_data)
