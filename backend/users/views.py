from urllib.parse import urlencode

import requests
from django.apps import apps
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from jwt.exceptions import InvalidTokenError
from rest_framework import generics, permissions, serializers, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import AuthenticationFailed, NotFound, PermissionDenied
from rest_framework.filters import SearchFilter
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from storage_service.services.factory import StorageType
from utils.send_email_verification_mail import send_email_verification_mail
from utils.send_forgot_password_link import send_forgot_password_link_brevo
from utils.utils import LoggerMixin

from users.models import Organization, OrganizationInvite, User, UserTag

from .serializers import (
    ChangePasswordSerializer,
    CustomTokenObtainPairSerializer,
    OrganizationInviteSerializer,
    OrganizationSerializer,
    OrganizationUserSerializer,
    ResetPasswordConfirmSerializer,
    ResetPasswordEmailSerializer,
    UpdateOrganizationUserSerializer,
    UserProfileSerializer,
    UserSerializer,
    UserTagSerializer,
)


class CustomTokenObtainPairView(LoggerMixin, TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        try:
            # First validate credentials using parent class
            response = super().post(request, *args, **kwargs)

            # Get the user from the validated credentials
            email = request.data.get("email")
            email = email.lower() if email else None
            user = User.objects.get(email=email)

            # Check if user belongs to any organization
            if not user.organizations.exists():
                self.logger.warning(f"Login denied for user {email}: No organization membership")
                raise AuthenticationFailed(
                    detail="User must be a member of an organization to login. Please contact your administrator."
                )

            self.logger.info(f"Successful login for user: {email}")
            return response

        except AuthenticationFailed:
            # Re-raise authentication failures
            raise
        except Exception as e:
            self.logger.error(f"Login failed for user: {request.data.get('email')} - {str(e)}")
            raise


class UserProfileView(LoggerMixin, APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserProfileSerializer

    def get(self, request):
        try:
            self.logger.info(f"Profile accessed by user: {request.user.email}")
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data)
        except Exception as e:
            self.logger.error(f"Error accessing profile for user {request.user.email}: {str(e)}")
            return Response({"error": "Failed to retrieve profile"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        try:
            self.logger.info(f"Profile update requested by user: {request.user.email}")
            serializer = UserProfileSerializer(request.user, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                self.logger.info(f"Profile updated successfully for user: {request.user.email}")
                return Response(serializer.data)

            self.logger.warning(f"Invalid profile update data for user {request.user.email}: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            self.logger.error(f"Error updating profile for user {request.user.email}: {str(e)}")
            return Response({"error": "Failed to update profile"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChangePasswordView(LoggerMixin, APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ChangePasswordSerializer

    def post(self, request):
        try:
            serializer = self.serializer_class(data=request.data)

            if serializer.is_valid():
                user = request.user

                # Check old password
                if not user.check_password(serializer.validated_data["old_password"]):
                    self.logger.warning(f"Failed password change attempt for user {user.email}: Invalid old password")
                    return Response({"error": "Invalid old password"}, status=status.HTTP_400_BAD_REQUEST)

                # Set new password
                user.set_password(serializer.validated_data["new_password"])
                user.save()

                self.logger.info(f"Password successfully changed for user {user.email}")
                return Response({"success": "Password updated successfully"}, status=status.HTTP_200_OK)

            self.logger.warning(f"Password change validation failed for user {request.user.email}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            self.logger.error(f"Error changing password for user {request.user.email}: {str(e)}")
            return Response(
                {"error": "An error occurred while changing password"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class VerifyTokenView(LoggerMixin, APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # Token is already verified by IsAuthenticated permission
            # Just serialize and return the user data
            serializer = CustomTokenObtainPairSerializer.get_token(request.user)
            user_serializer = UserSerializer(request.user)

            response_data = {
                "access": str(serializer.access_token),
                "refresh": str(serializer),
                "user": user_serializer.data,
            }

            self.logger.info(f"Token verified successfully for user: {request.user.email}")
            return Response(response_data)

        except InvalidTokenError as e:
            self.logger.warning(f"Invalid token verification attempt: {str(e)}")
            return Response({"error": "Invalid token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            self.logger.error(f"Error verifying token: {str(e)}")
            return Response(
                {"error": "An error occurred while verifying token"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ForgotPasswordView(LoggerMixin, generics.GenericAPIView):
    permission_classes = (AllowAny,)
    serializer_class = ResetPasswordEmailSerializer

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"]

        try:
            user = User.objects.get(email=email)

            # Generate token with explicit timestamp
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))

            self.logger.info(f"Generated token: {token} for user: {user.email}")

            # Construct reset link
            frontend_domain = settings.FRONTEND_DOMAIN
            reset_link = f"{frontend_domain}/auth/reset-password?uid={uid}&token={token}"

            # Log the complete reset link for debugging
            self.logger.debug(f"Reset link: {reset_link}")

            # Send the email
            send_forgot_password_link_brevo(email, user.username, reset_link, self.logger)

            return Response({"detail": "Password reset link has been sent to your email."}, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            self.logger.warning(f"Password reset attempted for non-existent email: {email}")
            return Response({"detail": "If an account exists with this email, a password reset link has been sent."})


class SendEmailVerificationView(LoggerMixin, APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))

        # Construct the verification link using the frontend URL
        frontend_domain = settings.FRONTEND_DOMAIN
        verification_link = f"{frontend_domain}/verify-email?uid={uid}&token={token}"

        # Send the verification email
        send_email_verification_mail(user.email, user.username, verification_link)

        return Response({"success": "Verification email sent"}, status=status.HTTP_200_OK)


class ValidateEmailVerificationView(LoggerMixin, APIView):
    permission_classes = [AllowAny]  # Override any global permission settings
    authentication_classes = []  # Override any global authentication settings

    def get(self, request, uidb64, token):
        try:
            # Decode the UID and retrieve the user
            uid = urlsafe_base64_decode(uidb64).decode()
            user = User.objects.get(pk=uid)

            # Use the token generator to check the token
            if default_token_generator.check_token(user, token):
                # Mark the user as verified
                user.verified = True
                user.save()

                return Response({"success": "Email verified successfully"}, status=status.HTTP_200_OK)
            else:
                return Response({"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response({"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordView(LoggerMixin, generics.GenericAPIView):
    permission_classes = (AllowAny,)
    serializer_class = ResetPasswordConfirmSerializer

    def get(self, request):
        # Get token and uid from query params
        uidb64 = request.query_params.get("uid")
        token = request.query_params.get("token")

        # Log received token information
        self.logger.info(f"Received token validation request - UID: {uidb64}, Token: {token}")

        try:
            # Decode the UID
            uid = urlsafe_base64_decode(uidb64).decode()
            user = User.objects.get(pk=uid)

            # Validate token
            if default_token_generator.check_token(user, token):
                self.logger.info(f"Token is valid for user: {user.email}")
                return Response({"valid": True}, status=status.HTTP_200_OK)
            else:
                self.logger.warning(f"Invalid token for user: {user.email}")
                return Response({"valid": False}, status=status.HTTP_400_BAD_REQUEST)

        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            self.logger.error(f"Invalid UID or token: {str(e)}")
            return Response({"valid": False}, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        try:
            # Get and validate token/uid
            uidb64 = request.query_params.get("uid")
            token = request.query_params.get("token")

            # Log received token information
            self.logger.info(f"Received reset request - UID: {uidb64}, Token: {token}")

            # Decode the UID
            try:
                uid = urlsafe_base64_decode(uidb64).decode()
                user = User.objects.get(pk=uid)
            except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
                self.logger.error(f"Invalid UID: {str(e)}")
                return Response({"error": "Invalid reset link"}, status=status.HTTP_400_BAD_REQUEST)

            # Validate token
            if default_token_generator.check_token(user, token):
                self.logger.info(f"Token validated successfully for user: {user.email}")
                # Validate request data
                serializer = self.get_serializer(data=request.data)
                if not serializer.is_valid():
                    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

                # Update password
                new_password = serializer.validated_data["new_password"]
                user.set_password(new_password)
                user.save()

                return Response({"success": "Password reset successful"}, status=status.HTTP_200_OK)
            else:
                # Log token validation failure details
                self.logger.warning(
                    f"Token validation failed for user: {user.email}\n"
                    f"Token: {token}\n"
                    f"User last login: {user.last_login}\n"
                    f"Password hash: {user.password[:10]}..."
                )
                return Response({"error": "Invalid or expired reset link"}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            self.logger.error(f"Password reset failed: {str(e)}")
            return Response({"error": "Password reset failed"}, status=status.HTTP_400_BAD_REQUEST)


# Organization Management Views
class OrganizationDetailView(LoggerMixin, generics.RetrieveUpdateAPIView):
    serializer_class = OrganizationSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Get the user's organization"""
        try:
            return self.request.user.organizations.first()
        except Organization.DoesNotExist:
            raise NotFound("User is not a member of any organization")


class OrganizationUsersView(LoggerMixin, generics.ListAPIView):
    serializer_class = OrganizationUserSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None
    filter_backends = [SearchFilter]
    search_fields = ["email", "first_name", "last_name", "username"]

    def get_queryset(self):
        """Get all users in the organization"""
        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")

        return organization.users.all().order_by("email")


# Add new view for updating user tags
class UpdateUserTagsView(LoggerMixin, APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        if request.user.role != "admin":
            raise PermissionDenied("Only admin users can manage user tags")

        organization = request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")

        try:
            user = organization.users.get(pk=pk)
        except User.DoesNotExist:
            raise NotFound("User not found in organization")

        tag_ids = request.data.get("tag_ids", [])
        print(f"tag ids from request {tag_ids}")

        # Ensure tag_ids is a list and contains valid integers
        try:
            tag_ids = [int(id) for id in tag_ids if str(id).isdigit()]
            print(f"tag ids after conversion {tag_ids}")
        except (ValueError, TypeError):
            return Response({"error": "Invalid tag IDs provided"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # Get all valid tags that belong to the organization
                print(f" tag ids {tag_ids}")
                valid_tags = UserTag.objects.filter(organization=organization, id__in=tag_ids)

                print(f"Valid tags: {valid_tags}")
                # Clear existing tags and set new ones
                user.tags.clear()
                if valid_tags.exists():
                    user.tags.add(*valid_tags)

                user.save()

                # Fetch fresh user data with tags
                user.refresh_from_db()
                serializer = OrganizationUserSerializer(user)
                return Response(serializer.data)

        except Exception as e:
            self.logger.error(f"Error updating tags for user {user.id}: {str(e)}")
            return Response({"error": f"Failed to update tags: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)


class UpdateOrganizationUserView(LoggerMixin, generics.UpdateAPIView):
    """View for updating organization user details by admin"""

    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        return UpdateOrganizationUserSerializer

    def get_object(self):
        """Get the user to update"""
        if not self.request.user.role == "admin":
            raise PermissionDenied("Only admin users can update organization users")

        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")

        try:
            user = organization.users.get(id=self.kwargs["pk"])
            return user
        except User.DoesNotExist:
            raise NotFound("User not found in organization")

    def update(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                self.logger.info(f"User {instance.email} updated successfully by admin {request.user.email}")

                # Return updated user data using OrganizationUserSerializer
                response_serializer = OrganizationUserSerializer(instance)
                return Response(response_serializer.data)

            self.logger.warning(f"Invalid user update data for user {instance.email}: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            self.logger.error(f"Error updating user: {str(e)}")
            return Response({"error": "Failed to update user"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RemoveOrganizationUserView(LoggerMixin, generics.DestroyAPIView):
    permission_classes = [IsAuthenticated]

    def get_object(self):
        if not self.request.user.role == "admin":
            raise PermissionDenied("Only admin users can remove organization users")

        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")

        try:
            user = organization.users.get(id=self.kwargs["pk"])
            if user.role == "admin":
                raise ValidationError("Cannot remove admin users")
            return user
        except User.DoesNotExist:
            raise NotFound("User not found in organization")

    def validate_transfer_user(self, user_id, organization, request=None):
        """Validate the user to transfer resources to"""
        if not user_id:
            raise ValidationError("Transfer user ID is required")

        try:
            # Validate transfer user exists and belongs to the organization
            transfer_user = organization.users.get(id=user_id)

            # Validate transfer user is from the same organization as the admin
            # Use the provided request or fall back to self.request
            admin_request = request or self.request
            admin_org = admin_request.user.organizations.first()
            if admin_org.id != organization.id:
                raise ValidationError("Transfer user must belong to the same organization as the admin")

            return transfer_user
        except User.DoesNotExist:
            raise ValidationError("Transfer user not found in organization")

    def transfer_resources(self, from_user, to_user, organization):
        """Transfer all resources from one user to another"""
        try:
            with transaction.atomic():
                # Case Management
                Case = apps.get_model("case_management", "Case")
                cases = Case.objects.filter(organization=organization)

                # Update case assignments and created_by
                cases.filter(created_by=from_user).update(created_by=to_user)

                # Handle many-to-many relationships for cases using a more efficient approach
                # Get all case IDs where the from_user is assigned
                case_ids_with_from_user = cases.filter(assigned_users=from_user).values_list("id", flat=True)

                # For these cases, add to_user (if not already assigned)
                for case_id in case_ids_with_from_user:
                    Case.objects.get(id=case_id).assigned_users.add(to_user)

                # Then remove from_user from all cases
                for case_id in case_ids_with_from_user:
                    Case.objects.get(id=case_id).assigned_users.remove(from_user)

                # Case Workers
                CaseWorkers = apps.get_model("case_management_v2", "CaseWorkers")
                worker_fields = [
                    "primary_contact",
                    "case_manager",
                    "lead_attorney",
                    "case_assistant",
                    "lien_negotiator",
                    "supervising_attorney",
                    "intake_specialist",
                    "investigator",
                    "accountant",
                    "litigation_attorney",
                    "litigation_assistant",
                ]

                for field in worker_fields:
                    CaseWorkers.objects.filter(case__organization=organization, **{field: from_user}).update(
                        **{field: to_user}
                    )

                # Case Tasks
                Task = apps.get_model("case_management", "Task")
                # Update assigned tasks
                Task.objects.filter(case__organization=organization, assigned_to=from_user).update(assigned_to=to_user)
                # Update created tasks
                Task.objects.filter(case__organization=organization, created_by=from_user).update(created_by=to_user)

                # Optimize tagged users in tasks
                # Get all task IDs where from_user is tagged
                task_ids_with_from_user = Task.objects.filter(
                    case__organization=organization, tagged_users=from_user
                ).values_list("id", flat=True)

                # Add to_user to these tasks (if not already tagged)

                for task_id in task_ids_with_from_user:
                    Task.objects.get(id=task_id).tagged_users.add(to_user)

                # Remove from_user from all tasks
                for task_id in task_ids_with_from_user:
                    Task.objects.get(id=task_id).tagged_users.remove(from_user)

                # Case Notes
                CaseNote = apps.get_model("case_management", "CaseNote")
                # Update created notes
                CaseNote.objects.filter(case__organization=organization, created_by=from_user).update(
                    created_by=to_user
                )

                # Optimize tagged users in notes
                # Get all note IDs where from_user is tagged
                note_ids_with_from_user = CaseNote.objects.filter(
                    case__organization=organization, tagged_users=from_user
                ).values_list("id", flat=True)

                # Add to_user to these notes (if not already tagged)
                for note_id in note_ids_with_from_user:
                    CaseNote.objects.get(id=note_id).tagged_users.add(to_user)

                # Remove from_user from all notes
                for note_id in note_ids_with_from_user:
                    CaseNote.objects.get(id=note_id).tagged_users.remove(from_user)

                # Lead Management
                Lead = apps.get_model("lead_management", "Lead")
                # Update assigned leads
                Lead.objects.filter(organization=organization, assignee=from_user).update(assignee=to_user)
                # Update created leads
                Lead.objects.filter(organization=organization, created_by=from_user).update(created_by=to_user)

                # Lead Notes
                LeadNote = apps.get_model("lead_management", "LeadNote")
                # Update created notes
                LeadNote.objects.filter(lead__organization=organization, created_by=from_user).update(
                    created_by=to_user
                )

                # Optimize tagged users in lead notes
                # Get all lead note IDs where from_user is tagged
                lead_note_ids_with_from_user = LeadNote.objects.filter(
                    lead__organization=organization, tagged_users=from_user
                ).values_list("id", flat=True)

                # Add to_user to these notes (if not already tagged)
                for note_id in lead_note_ids_with_from_user:
                    LeadNote.objects.get(id=note_id).tagged_users.add(to_user)

                # Remove from_user from all notes
                for note_id in lead_note_ids_with_from_user:
                    LeadNote.objects.get(id=note_id).tagged_users.remove(from_user)

                # Lead Tasks
                LeadTask = apps.get_model("lead_management", "LeadTask")
                # Update assigned tasks
                LeadTask.objects.filter(lead__organization=organization, assigned_to=from_user).update(
                    assigned_to=to_user
                )
                # Update created tasks
                LeadTask.objects.filter(lead__organization=organization, created_by=from_user).update(
                    created_by=to_user
                )
                # Update overwritten tasks
                LeadTask.objects.filter(lead__organization=organization, overwritten_by=from_user).update(
                    overwritten_by=to_user
                )

                # Optimize tagged users in lead tasks
                # Get all lead task IDs where from_user is tagged
                lead_task_ids_with_from_user = LeadTask.objects.filter(
                    lead__organization=organization, tagged_users=from_user
                ).values_list("id", flat=True)

                # Add to_user to these tasks (if not already tagged)
                for task_id in lead_task_ids_with_from_user:
                    LeadTask.objects.get(id=task_id).tagged_users.add(to_user)

                # Remove from_user from all tasks
                for task_id in lead_task_ids_with_from_user:
                    LeadTask.objects.get(id=task_id).tagged_users.remove(from_user)

                self.logger.info(
                    f"Successfully transferred resources from user {from_user.email} to user {to_user.email} "
                    f"in organization {organization.name}"
                )

        except Exception as e:
            self.logger.error(f"Error transferring resources: {str(e)}")
            raise ValidationError(f"Failed to transfer resources: {str(e)}")

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            organization = request.user.organizations.first()

            # Get the user to transfer resources to
            transfer_user_id = request.data.get("transfer_to_user_id")
            transfer_user = self.validate_transfer_user(transfer_user_id, organization)

            # Transfer all resources
            self.transfer_resources(instance, transfer_user, organization)

            # Remove user from organization
            organization.users.remove(instance)

            return Response(status=status.HTTP_204_NO_CONTENT)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class OrganizationInviteViewSet(LoggerMixin, viewsets.ModelViewSet):
    serializer_class = OrganizationInviteSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination for this view

    def get_queryset(self):
        if not self.request.user.role == "admin":
            raise PermissionDenied("Only admin users can manage invites")

        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")

        return organization.invites.filter(status="pending").order_by("-created_at")

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["organization"] = self.request.user.organizations.first()
        context["invited_by"] = self.request.user
        return context

    @action(detail=True, methods=["post"])
    def revoke(self, request, pk=None):
        invite = self.get_object()
        invite.revoke()
        return Response({"status": "revoked"})

    @action(detail=True, methods=["post"])
    def resend(self, request, pk=None):
        """Resend invitation email for an existing invite"""
        try:
            invite = self.get_object()

            # Check if invite is still pending
            if invite.status != "pending":
                return Response(
                    {"error": f"Cannot resend invitation that is {invite.status}"}, status=status.HTTP_400_BAD_REQUEST
                )

            # Check if invite is expired
            if invite.is_expired:
                return Response(
                    {"error": "This invitation has expired. Please create a new invitation."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Resend invitation email
            token = str(invite.token)
            frontend_domain = settings.FRONTEND_DOMAIN
            invite_link = f"{frontend_domain}/join-organization?token={token}"

            from utils.send_organization_invite import send_organization_invite_brevo

            send_organization_invite_brevo(
                email=invite.email,
                username=invite.email.split("@")[0],  # Use email prefix as username
                organization_name=invite.organization.name,
                role=invite.get_role_display(),
                invite_link=invite_link,
                logger=self.logger,
            )

            return Response(
                {"message": "Invitation email resent successfully", "invite": self.get_serializer(invite).data}
            )

        except Exception as e:
            self.logger.error(f"Failed to resend invitation email: {str(e)}")
            return Response(
                {"error": "Failed to resend invitation email"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):
        try:
            # Check if there's an existing pending invite
            organization = self.request.user.organizations.first()
            email = request.data.get("email")
            existing_invite = OrganizationInvite.objects.filter(
                organization=organization, email=email, status="pending"
            ).first()

            if existing_invite:
                # Return the existing invite with a message
                serializer = self.get_serializer(existing_invite)
                return Response(
                    {"message": "An invitation has already been sent to this email", "invite": serializer.data},
                    status=status.HTTP_200_OK,
                )

            # If no existing invite, proceed with creation
            return super().create(request, *args, **kwargs)

        except Exception as e:
            self.logger.error(f"Error creating invite: {str(e)}")
            return Response({"error": "Failed to create invitation"}, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        try:
            invite = serializer.save()

            # Send invitation email
            token = str(invite.token)
            frontend_domain = settings.FRONTEND_DOMAIN
            invite_link = f"{frontend_domain}/join-organization?token={token}"

            from utils.send_organization_invite import send_organization_invite_brevo

            send_organization_invite_brevo(
                email=invite.email,
                username=invite.email.split("@")[0],  # Use email prefix as username
                organization_name=invite.organization.name,
                role=invite.get_role_display(),
                invite_link=invite_link,
                logger=self.logger,
            )
        except Exception as e:
            # If email sending fails, delete the invite and re-raise the error
            if "invite" in locals():
                invite.delete()
            raise serializers.ValidationError(f"Failed to send invitation email: {str(e)}")


class AcceptOrganizationInviteView(LoggerMixin, generics.GenericAPIView):
    permission_classes = []  # Public endpoint
    serializer_class = serializers.Serializer  # Empty serializer

    def get(self, request, token):
        try:
            invite = OrganizationInvite.objects.get(token=token, status="pending")

            if invite.is_expired:
                return Response({"error": "This invitation has expired"}, status=status.HTTP_400_BAD_REQUEST)

            # Check if user exists
            try:
                user = User.objects.get(email=invite.email)
            except User.DoesNotExist:
                # Return info needed to create account
                return Response(
                    {
                        "email": invite.email,
                        "organization": invite.organization.name,
                        "role": invite.role,
                        "needs_account": True,
                    }
                )

            # Validate user can accept invite
            if user.organizations.exists():
                return Response(
                    {"error": "User already belongs to an organization"}, status=status.HTTP_400_BAD_REQUEST
                )

            return Response(
                {
                    "email": invite.email,
                    "organization": invite.organization.name,
                    "role": invite.role,
                    "needs_account": False,
                }
            )

        except OrganizationInvite.DoesNotExist:
            return Response({"error": "Invalid or expired invitation"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request, token):
        try:
            invite = OrganizationInvite.objects.get(token=token, status="pending")

            if invite.is_expired:
                return Response({"error": "This invitation has expired"}, status=status.HTTP_400_BAD_REQUEST)

            # Get or create user
            user = request.user if request.user.is_authenticated else None

            if not user:
                # Create new user if needed
                password = request.data.get("password")
                if not password:
                    return Response({"error": "Password is required"}, status=status.HTTP_400_BAD_REQUEST)

                try:
                    user = User.objects.create_user(
                        email=invite.email,
                        username=invite.email,  # Use email as username
                        password=password,
                        role=invite.role,
                    )
                except Exception as e:
                    return Response({"error": f"Failed to create user: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                # Accept invitation
                invite.accept(user)

                # Return tokens for authentication
                refresh = RefreshToken.for_user(user)
                return Response(
                    {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    }
                )
            except ValidationError as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        except OrganizationInvite.DoesNotExist:
            return Response({"error": "Invalid or expired invitation"}, status=status.HTTP_404_NOT_FOUND)


class OneDriveViewSet(viewsets.ViewSet):
    """ViewSet for OneDrive integration"""

    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Get the user's organization"""
        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")
        return organization

    def _is_admin(self, user, organization):
        """Check if user is an admin in their organization"""
        # Check if user is in the organization
        if not organization.users.filter(id=user.id).exists():
            return False
        # Check if user has admin role
        return user.role in ["admin", "managing_partner"]

    @action(detail=False, methods=["get"])
    def connect(self, request):
        """Start OneDrive OAuth flow"""
        organization = self.get_object()

        # Only organization admins can connect OneDrive
        if not self._is_admin(request.user, organization):
            return Response(
                {"detail": "Only organization admins can connect OneDrive"}, status=status.HTTP_403_FORBIDDEN
            )

        # Build authorization URL
        params = {
            "client_id": settings.ONEDRIVE_CLIENT_ID,
            "response_type": "code",
            "redirect_uri": settings.ONEDRIVE_REDIRECT_URI,
            "scope": " ".join(settings.ONEDRIVE_SCOPES),
            "response_mode": "query",
            "state": str(organization.id),  # Pass org ID in state
        }

        auth_url = f"{settings.ONEDRIVE_AUTHORITY}/oauth2/v2.0/authorize?{urlencode(params)}"
        return Response({"auth_url": auth_url})

    @action(detail=False, methods=["get"], permission_classes=[AllowAny])  # Make this endpoint publicly accessible
    def callback(self, request):
        """Handle OneDrive OAuth callback"""
        # Get organization from state parameter instead of authenticated user
        state = request.query_params.get("state")
        try:
            organization = Organization.objects.get(id=state)
        except Organization.DoesNotExist:
            return Response({"error": "Invalid organization"}, status=status.HTTP_400_BAD_REQUEST)

        error = request.query_params.get("error")
        if error:
            return Response(
                {"error": error, "error_description": request.query_params.get("error_description")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        code = request.query_params.get("code")
        if not code:
            return Response({"error": "No authorization code received"}, status=status.HTTP_400_BAD_REQUEST)

        # Exchange code for tokens
        token_url = f"{settings.ONEDRIVE_AUTHORITY}/oauth2/v2.0/token"
        token_data = {
            "client_id": settings.ONEDRIVE_CLIENT_ID,
            "client_secret": settings.ONEDRIVE_CLIENT_SECRET,
            "code": code,
            "redirect_uri": settings.ONEDRIVE_REDIRECT_URI,
            "grant_type": "authorization_code",
            "scope": " ".join(settings.ONEDRIVE_SCOPES),
        }

        try:
            response = requests.post(token_url, data=token_data)
            response.raise_for_status()
            tokens = response.json()

            # Update organization with tokens
            organization.storage_type = StorageType.ONEDRIVE
            organization.onedrive_access_token = tokens["access_token"]
            organization.onedrive_refresh_token = tokens["refresh_token"]
            organization.onedrive_token_expires_at = timezone.now() + timezone.timedelta(seconds=tokens["expires_in"])
            organization.save()

            return Response({"message": "OneDrive connected successfully", "storage_type": organization.storage_type})

        except requests.RequestException as e:
            return Response(
                {"error": f"Failed to exchange code for tokens: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["post"])
    def disconnect(self, request):
        """Disconnect OneDrive"""
        organization = self.get_object()

        # Only organization admins can disconnect OneDrive
        if not self._is_admin(request.user, organization):
            return Response(
                {"detail": "Only organization admins can disconnect OneDrive"}, status=status.HTTP_403_FORBIDDEN
            )

        # Reset OneDrive settings
        organization.storage_type = StorageType.S3
        organization.onedrive_access_token = None
        organization.onedrive_refresh_token = None
        organization.onedrive_token_expires_at = None
        organization.save()

        return Response({"message": "OneDrive disconnected successfully"})

    @action(detail=False, methods=["get"])
    def status(self, request):
        """Get OneDrive connection status"""
        organization = self.get_object()

        is_connected = (
            organization.storage_type == StorageType.ONEDRIVE
            and organization.onedrive_access_token is not None
            and organization.onedrive_refresh_token is not None
            and organization.onedrive_token_expires_at is not None
        )

        response_data = {
            "is_connected": is_connected,
        }

        # Only include token info if connected and user is admin
        if is_connected and self._is_admin(request.user, organization):
            response_data["tokens"] = {
                "access_token": organization.onedrive_access_token,
                "refresh_token": organization.onedrive_refresh_token,
                "expires_at": organization.onedrive_token_expires_at.isoformat()
                if organization.onedrive_token_expires_at
                else None,
            }

        return Response(response_data)


class UserTagViewSet(LoggerMixin, viewsets.ModelViewSet):
    serializer_class = UserTagSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")
        return UserTag.objects.filter(organization=organization)

    def check_permissions(self, request):
        super().check_permissions(request)
        # Allow read operations for all authenticated users
        if request.method not in ["GET", "HEAD", "OPTIONS"]:
            if not request.user.role == "admin":
                raise PermissionDenied("Only admin users can manage user tags")

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        serializer.save(organization=organization)

    def perform_update(self, serializer):
        organization = self.request.user.organizations.first()
        serializer.save(organization=organization)

    @action(detail=False, methods=["get"])
    def my_tags(self, request):
        """Get tags for the requesting user"""
        try:
            user = request.user
            serializer = UserTagSerializer(user.tags.all(), many=True)
            return Response(serializer.data)
        except Exception as e:
            self.logger.error(f"Error fetching tags for user {user.id}: {str(e)}")
            return Response({"error": "Failed to fetch user tags"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TerminateUserView(LoggerMixin, APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Terminate a user by transferring their responsibilities to another user
        and marking them as inactive.
        """
        if not request.user.role == "admin":
            raise PermissionDenied("Only admin users can terminate organization users")

        organization = request.user.organizations.first()
        if not organization:
            raise NotFound("User is not a member of any organization")

        user_id = request.data.get("user_id")
        transfer_to_user_id = request.data.get("transfer_to_user_id")

        if not user_id or not transfer_to_user_id:
            return Response(
                {"error": "Both user_id and transfer_to_user_id are required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get the user to terminate
            user_to_terminate = organization.users.get(id=user_id)

            # Prevent terminating admin users
            if user_to_terminate.role == "admin":
                return Response({"error": "Cannot terminate admin users"}, status=status.HTTP_400_BAD_REQUEST)

            # Get the user to transfer responsibilities to
            transfer_to_user = organization.users.get(id=transfer_to_user_id)

            # Reuse the RemoveOrganizationUserView's transfer_resources method
            remove_view = RemoveOrganizationUserView()
            remove_view.validate_transfer_user(transfer_to_user_id, organization, request=request)
            remove_view.transfer_resources(user_to_terminate, transfer_to_user, organization)

            # Send notification to the target user
            self.send_responsibility_transfer_notification(user_to_terminate, transfer_to_user)

            # Remove user from organization
            organization.users.remove(user_to_terminate)

            # Mark user as inactive (soft delete)
            user_to_terminate.is_active = False
            user_to_terminate.save()

            return Response(
                {"message": f"User {user_to_terminate.email} has been terminated successfully"},
                status=status.HTTP_200_OK,
            )

        except User.DoesNotExist:
            return Response({"error": "User not found in organization"}, status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            self.logger.error(f"Error terminating user: {str(e)}")
            return Response(
                {"error": f"Failed to terminate user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def send_responsibility_transfer_notification(self, from_user, to_user):
        """Send notification to target user about transferred responsibilities"""
        try:
            Notification = apps.get_model("notifications", "Notification")

            # Create notification for target user
            Notification.objects.create(
                user=to_user,
                title="Responsibilities Transferred",
                message=f"You have been assigned responsibilities previously handled by {from_user.name or from_user.email}.",
                notification_type="user_termination",
                data={
                    "from_user_id": from_user.id,
                    "from_user_email": from_user.email,
                    "from_user_name": from_user.name or from_user.email,
                },
            )

            self.logger.info(f"Sent responsibility transfer notification to {to_user.email}")
        except Exception as e:
            self.logger.error(f"Failed to send notification: {str(e)}")
            # Don't raise exception here, as this is not critical to the termination process
