import argparse
import os
import sys

import boto3
import django
from botocore.exceptions import ClientError, NoCredentialsError

# Add the parent directory to sys.path to allow imports from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Use the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()
from django.conf import settings

# Access variables
AWS_REGION_NAME = settings.AWS_S3_REGION_NAME
AWS_ACCESS_KEY_ID = settings.AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY = settings.AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME = "raw-orgs-data"  # settings.AWS_STORAGE_BUCKET_NAME


def file_exists_in_s3(bucket, s3_key):
    try:
        bucket.Object(s3_key).load()
        return True
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False
        raise


def upload_files_to_s3(folder_path, env, migration_label, org_id):
    session = boto3.Session(
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        region_name=AWS_REGION_NAME,
    )

    s3 = session.resource("s3", AWS_REGION_NAME)
    bucket = s3.Bucket(AWS_STORAGE_BUCKET_NAME)

    if not os.path.isdir(folder_path):
        print(f"Error: '{folder_path}' is not a valid directory.")
        return

    print(f"Uploading files from '{folder_path}' to S3...")

    for root, dirs, files in os.walk(folder_path):
        total = len(files)
        current = 0
        for file in files:
            print(f"========= processing case file {current} out of {total}")
            current += 1
            if file.endswith(".zip"):
                local_file_path = os.path.join(root, file)
                s3_key = f"{env}/{org_id}/{migration_label}/{file}"

                # Check if file already exists in S3
                if file_exists_in_s3(bucket, s3_key):
                    print(f"Skipping {file} - already exists in S3")
                    continue

                try:
                    bucket.upload_file(local_file_path, s3_key)
                    print(f"Uploaded: {file} -> s3://{AWS_STORAGE_BUCKET_NAME}/{s3_key}")
                except NoCredentialsError:
                    print("Error: AWS credentials not found.")
                    return
                except Exception as e:
                    print(f"Failed to upload {file}: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Upload zipped criminal case photos to S3.")
    parser.add_argument("folder_path", help="Path to the folder containing .zip files.")
    parser.add_argument("env", help="Environment name (e.g., dev, prod).")
    parser.add_argument("migration_label", help="Migration label (e.g., migration_casepeer_data).")
    parser.add_argument("org_id", help="Organization ID.")

    args = parser.parse_args()

    upload_files_to_s3(
        folder_path=args.folder_path,
        env=args.env,
        migration_label=args.migration_label,
        org_id=args.org_id,
    )
