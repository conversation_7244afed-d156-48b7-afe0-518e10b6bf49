#!/usr/bin/env python
"""
Production-ready Casepeer Photo Uploader Script
Uploads photos from zip files in S3 to case's storage folder.
Designed to handle multiple cases with proper logging and analytics.
"""

import argparse
import csv
import os
import re
import sys
import zipfile
from datetime import datetime
from io import BytesIO

import boto3
import django

# Add the parent directory to sys.path to allow imports from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Use the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from case_management.models import Case
from django.conf import settings
from storage_service.services.factory import StorageServiceFactory, StorageType

# ==================== CONFIGURATION ====================
# Access variables from Django settings
AWS_REGION_NAME = settings.AWS_S3_REGION_NAME
AWS_ACCESS_KEY_ID = settings.AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY = settings.AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME = "raw-orgs-data"  # settings.AWS_STORAGE_BUCKET_NAME

# Logging configuration
LOG_FILE_PATH = "casepeer_upload_log.csv"  # CSV file for analytics
ENABLE_DEBUG_LOGGING = True  # Set to False for production runs

# ==================== LOGGING SETUP ====================


def setup_logging():
    """Initialize CSV logging file with headers if it doesn't exist"""
    if not os.path.exists(LOG_FILE_PATH):
        with open(LOG_FILE_PATH, "w", newline="") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(
                [
                    "timestamp",
                    "zip_filename",
                    "case_id",
                    "case_name",
                    "storage_root_folder_id",
                    "casepeer_folder_id",
                    "zip_file_count",
                    "files_uploaded",
                    "files_failed",
                    "upload_success",
                    "error_message",
                    "execution_time_seconds",
                ]
            )


def log_result(
    zip_filename,
    case_id,
    case_name,
    storage_root_folder_id,
    casepeer_folder_id,
    zip_file_count,
    files_uploaded,
    files_failed,
    upload_success,
    error_message,
    execution_time,
):
    """Log the upload result to CSV file"""
    with open(LOG_FILE_PATH, "a", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(
            [
                datetime.now().isoformat(),
                zip_filename,
                case_id,
                case_name,
                storage_root_folder_id,
                casepeer_folder_id,
                zip_file_count,
                files_uploaded,
                files_failed,
                upload_success,
                error_message,
                execution_time,
            ]
        )


def debug_print(message):
    """Print debug messages only if debug logging is enabled"""
    if ENABLE_DEBUG_LOGGING:
        print(f"[DEBUG] {message}")


# ==================== UTILITY FUNCTIONS ====================


def extract_case_id_from_zip(zip_filename):
    """Extract case ID from zip filename using regex"""
    # Pattern: 1189637-CASE-C716B7C4_photos.zip -> CASE-C716B7C4
    pattern = r"\d+-(.+?)_photos\.zip"
    match = re.search(pattern, zip_filename)
    if match:
        return match.group(1)
    else:
        raise ValueError(f"Could not extract case ID from filename: {zip_filename}")


def get_zip_file_info(zip_data):
    """Get information about files in the zip from BytesIO data"""
    try:
        with zipfile.ZipFile(zip_data, "r") as zip_ref:
            file_list = [f for f in zip_ref.namelist() if not f.endswith("/")]
            return file_list
    except Exception as e:
        raise Exception(f"Error reading zip file: {str(e)}")


def get_s3_files(s3_client, bucket, prefix):
    """Get list of zip files from S3 with given prefix"""
    try:
        response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)
        return [obj["Key"] for obj in response.get("Contents", []) if obj["Key"].endswith(".zip")]
    except Exception as e:
        raise Exception(f"Error listing S3 files: {str(e)}")


# ==================== MAIN UPLOAD FUNCTION ====================


def process_zip_file(s3_client, bucket, s3_key, storage_service, casepeer_folder_name):
    """Process a single zip file from S3"""
    start_time = datetime.now()
    zip_filename = os.path.basename(s3_key)

    # Initialize variables for logging
    case_id = None
    case_name = ""
    storage_root_folder_id = ""
    casepeer_folder_id = ""
    zip_file_count = 0
    files_uploaded = 0
    files_failed = 0
    upload_success = False
    error_message = ""

    try:
        print(f"\n=== Processing {zip_filename} ===")
        print(f"Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Step 1: Extract case ID from zip filename
        try:
            case_id = extract_case_id_from_zip(zip_filename)
            print(f"✓ Extracted Case ID: {case_id}")
        except ValueError as e:
            error_message = str(e)
            print(f"✗ {error_message}")
            return

        # Step 2: Get the case
        try:
            case = Case.objects.get(id=case_id)
            case_name = case.name
            storage_root_folder_id = case.storage_root_folder_id or ""
            print(f"✓ Found case: {case_name}")
            debug_print(f"Storage Root: {storage_root_folder_id}")
        except Case.DoesNotExist:
            error_message = f"Case {case_id} not found"
            print(f"✗ {error_message}")
            return

        if not case.storage_root_folder_id:
            error_message = f"Case {case_id} does not have storage structure"
            print(f"✗ {error_message}")
            return

        # Step 3: Create or find casepeer folder
        try:
            contents = storage_service.get_folder_contents(case.storage_root_folder_id)
            casepeer_folder = None

            for item in contents:
                if item.get("name") == casepeer_folder_name and "folder" in item.get("type", ""):
                    casepeer_folder = item
                    break

            if casepeer_folder:
                casepeer_folder_id = casepeer_folder["id"]
                print(f"✓ Found existing folder: {casepeer_folder_name}")
            else:
                print(f"Creating folder: {casepeer_folder_name}")
                folder_result = storage_service.create_folder(
                    parent_folder_id=case.storage_root_folder_id, folder_name=casepeer_folder_name
                )
                casepeer_folder_id = folder_result["id"]
                print(f"✓ Created folder: {casepeer_folder_name}")

        except Exception as e:
            error_message = f"Error with casepeer folder: {str(e)}"
            print(f"✗ {error_message}")
            return

        # Step 4: Download and process zip file
        try:
            # Download the zip file from S3
            response = s3_client.get_object(Bucket=AWS_STORAGE_BUCKET_NAME, Key=s3_key)
            zip_data = BytesIO(response["Body"].read())

            # Get list of files in the zip
            file_list = get_zip_file_info(zip_data)
            zip_file_count = len(file_list)
            print(f"✓ Zip file contains {zip_file_count} files")

            if zip_file_count == 0:
                print("✓ Zip file is empty, nothing to upload")
                upload_success = True
                return

        except Exception as e:
            error_message = f"Error reading zip file: {str(e)}"
            print(f"✗ {error_message}")
            return

        # Step 5: Upload files
        print(f"Uploading {zip_file_count} files...")

        try:
            # Reset the BytesIO position to the beginning
            zip_data.seek(0)

            with zipfile.ZipFile(zip_data, "r") as zip_ref:
                for i, file_path in enumerate(file_list, 1):
                    filename = os.path.basename(file_path)
                    if not filename:
                        continue

                    print(f"  [{i}/{zip_file_count}] {filename}...", end=" ")

                    try:
                        # Extract file to memory
                        file_data = zip_ref.read(file_path)
                        file_obj = BytesIO(file_data)

                        # Check file size (limit to 15MB)
                        file_size = len(file_data)
                        if file_size > 15 * 1024 * 1024:  # 15MB
                            print(f"SKIPPED (too large: {file_size / (1024 * 1024):.1f}MB)")
                            files_failed += 1
                            continue

                        # Upload to storage service
                        upload_result = storage_service.upload_file(
                            folder_id=casepeer_folder_id, file_name=filename, file_content=file_obj
                        )
                        print("✓")
                        files_uploaded += 1

                    except Exception as upload_error:
                        print(f"✗ ({str(upload_error)})")
                        files_failed += 1

                upload_success = True
                print(f"✓ Upload completed: {files_uploaded} uploaded, {files_failed} failed")

        except Exception as e:
            error_message = f"Error during upload: {str(e)}"
            print(f"✗ {error_message}")
            return

    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"
        print(f"✗ {error_message}")

    finally:
        # Calculate execution time
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # Log results
        log_result(
            zip_filename,
            case_id,
            case_name,
            storage_root_folder_id,
            casepeer_folder_id,
            zip_file_count,
            files_uploaded,
            files_failed,
            upload_success,
            error_message,
            execution_time,
        )

        print(f"Completed in {execution_time:.2f} seconds")


def upload_casepeer_photos(env, org_id, migration_label, casepeer_folder_name="casepeer_photos"):
    """Main function to upload photos from S3 zip files to case storage"""
    try:
        print("Starting Casepeer Photo Uploader...")
        print(f"Environment: {env}")
        print(f"Organization ID: {org_id}")
        print(f"Migration Label: {migration_label}")

        # Initialize S3 client
        session = boto3.Session(
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION_NAME,
        )
        s3_client = session.client("s3")
        s3_resource = session.resource("s3")
        bucket = s3_resource.Bucket(AWS_STORAGE_BUCKET_NAME)

        # Initialize storage service
        storage_service = StorageServiceFactory.get_storage_service(StorageType.S3, org_id=org_id)
        print("✓ Storage service initialized")

        # Get list of zip files from S3
        prefix = f"{env}/{org_id}/{migration_label}/"
        zip_files = get_s3_files(s3_client, AWS_STORAGE_BUCKET_NAME, prefix)

        if not zip_files:
            print(f"No zip files found in {prefix}")
            return

        print(f"Found {len(zip_files)} zip files to process")

        # Process each zip file
        for s3_key in zip_files:
            process_zip_file(s3_client, bucket, s3_key, storage_service, casepeer_folder_name)

        print(f"\nAll files processed. Results logged to: {LOG_FILE_PATH}")

    except Exception as e:
        print(f"Error: {str(e)}")


# ==================== MAIN EXECUTION ====================

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Upload Casepeer photos from S3 to case storage.")
    parser.add_argument("env", help="Environment name (e.g., dev, prod).")
    parser.add_argument("org_id", help="Organization ID.")
    parser.add_argument("migration_label", help="Migration label (e.g., migration_casepeer_data).")
    parser.add_argument(
        "--folder-name", default="casepeer_photos", help="Name of the folder to store photos (default: casepeer_photos)"
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging()

    # Run the upload
    upload_casepeer_photos(
        env=args.env, org_id=args.org_id, migration_label=args.migration_label, casepeer_folder_name=args.folder_name
    )
