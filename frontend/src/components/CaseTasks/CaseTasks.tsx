"use client";

import { useState, useEffect } from "react";
import {
  useTasksListQuery,
  useCreateTaskMutation,
  useDeleteTaskMutation,
  useUpdateTaskMutation,
  useSyncTaskMutation,
} from "@/services/case-management/noteTaskService";
import caseManagementService from "@/services/caseManagementService";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import RichTextEditor from "@/components/ui/RichTextEditor";
import RichTextViewer from "@/components/ui/RichTextViewer";
import {
  Trash2,
  Plus,
  Search,
  User2,
  CheckCircle2,
  Circle,
  Clock,
  AlertCircle,
  Pencil,
  Calendar,
  Loader2,
  MoreVertical,
  Repeat,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { format, parse } from "date-fns";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useInView } from "react-intersection-observer";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import {
  Task,
  ListResponse,
  TaskStatus,
  TaskStatusConfig,
  UserDetail,
} from "@/type/case-management/noteTaskTypes";
import { UserDetails } from "@/type/caseManagement";
import { useToast } from "@/hooks/use-toast";
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import { NotesTasksSectionTypes } from "@/type/case-management/noteTaskTypes";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import SyncCaseDialog from "../CaseOverview/components/SyncCaseDialog";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

interface CaseTasksProps {
  caseId: string;
  taskFor?: NotesTasksSectionTypes;
  defendantId?: string;
}

const StatusIcons = {
  [TaskStatus.PENDING]: Circle,
  [TaskStatus.IN_PROGRESS]: Clock,
  [TaskStatus.COMPLETED]: CheckCircle2,
  [TaskStatus.REOPENED]: AlertCircle,
};

const ICON_CLASSES = "h-4 w-4";

export function CaseTasks({ caseId, taskFor = 'Case', defendantId }: CaseTasksProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedAssignee, setSelectedAssignee] = useState<string>();
  const [selectedDueDate, setSelectedDueDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<TaskStatus>(
    TaskStatus.PENDING
  );
  const [availableUsers, setAvailableUsers] = useState<UserDetails[]>([]);
  const { ref: loadMoreRef, inView } = useInView();
  const [editingTask, setEditingTask] = useState<{
    id: number;
    title: string;
    description: string;
    assigned_to_id?: number;
    tagged_user_ids: number[];
    due_date?: string;
  } | null>(null);
  const [selectedTaggedUsers, setSelectedTaggedUsers] = useState<number[]>([]);
  const [filterAssignee, setFilterAssignee] = useState<string>();
  const { data: linkedCasesData } = useLinkedCasesQuery(caseId);
  const [selectedTaskForSync, setSelectedTaskForSync] = useState<number | null>(null);
  const [showSyncDialog, setShowSyncDialog] = useState(false);
  const [selectedCaseIds, setSelectedCaseIds] = useState<string[]>([]);
  const { toast } = useToast();

  const allLinkedCases = [
    ...(linkedCasesData?.direct_cases || []),
    ...(linkedCasesData?.indirect_cases || [])
  ];

  const {
    data: tasksData,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useTasksListQuery(caseId, taskFor, defendantId);
  const createTaskMutation = useCreateTaskMutation(caseId, taskFor);
  const deleteTaskMutation = useDeleteTaskMutation(caseId);
  const updateTaskMutation = useUpdateTaskMutation(caseId, taskFor);
  const syncTaskMutation = useSyncTaskMutation(caseId);

  // Fetch available users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const users = await caseManagementService.getAvailableUsers(caseId);
        setAvailableUsers(users);
      } catch (error) {
        console.error("Failed to fetch users:", error);
      }
    };
    fetchUsers();
  }, [caseId]);

  // Load more when scrolled to bottom
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleDateChange = (value: string) => {
    setSelectedDate(value);
  };

  const handleDueDateChange = (value: string) => {
    if (value) {
      const [day, month, year] = value.split("-");
      const currentDateTime = selectedDueDate
        ? new Date(selectedDueDate)
        : new Date();

      const newDate = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        currentDateTime.getHours(),
        currentDateTime.getMinutes()
      );

      if (!isNaN(newDate.getTime())) {
        setSelectedDueDate(newDate.toISOString());
      }
    }
  };

  const handleEditingDueDateChange = (value: string) => {
    if (value) {
      const parsedDate = parse(value, "dd-MM-yyyy", new Date());
      const formattedDate = format(parsedDate, "yyyy-MM-dd");
      setEditingTask((prev) =>
        prev ? { ...prev, due_date: formattedDate } : null
      );
    } else {
      setEditingTask((prev) =>
        prev ? { ...prev, due_date: undefined } : null
      );
    }
  };

  const handleSubmit = async () => {
    if (!title.trim()) return;

    try {
      const createdTask = await createTaskMutation.mutateAsync({
        case: caseId,
        title: title,
        description: description,
        status: selectedStatus,
        assigned_to_id: selectedAssignee ? parseInt(selectedAssignee) : undefined,
        tagged_user_ids: selectedTaggedUsers,
        due_date: selectedDueDate,
        task_for: taskFor,
        ...(taskFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
      });

      if (showSyncDialog) {
        await syncTaskMutation.mutateAsync({
          source_task_id: createdTask.id,
          target_case_ids: selectedCaseIds
        });
      }

      setTitle("");
      setDescription("");
      setSelectedDueDate("");
      setSelectedAssignee(undefined);
      setSelectedTaggedUsers([]);
      setSelectedStatus(TaskStatus.PENDING);
      setIsExpanded(false);
      setShowSyncDialog(false);
    } catch (error) {
      console.error("Failed to create task:", error);
      toast({
        title: "Error",
        description: "Failed to create task",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (taskId: number) => {
    try {
      await deleteTaskMutation.mutateAsync(taskId.toString());
    } catch (error) {
      console.error("Failed to delete task:", error);
    }
  };

  const handleStatusChange = async (taskId: number, newStatus: TaskStatus) => {
    try {
      const task = tasksData?.pages[0].results.find((t) => t.id === taskId);
      if (!task) return;

      await updateTaskMutation.mutateAsync({
        taskId: taskId.toString(),
        data: {
          case: caseId,
          status: newStatus,
          title: task.title,
          task_for: taskFor,
          ...(taskFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
        },
      });
    } catch (error) {
      console.error("Failed to update task status:", error);
    }
  };

  const handleEdit = async () => {
    if (!editingTask) return;

    try {
      const updatedTask = await updateTaskMutation.mutateAsync({
        taskId: editingTask.id.toString(),
        data: {
          case: caseId,
          title: editingTask.title,
          description: editingTask.description,
          assigned_to_id: editingTask.assigned_to_id,
          tagged_user_ids: editingTask.tagged_user_ids,
          due_date: editingTask.due_date,
          task_for: taskFor,
          ...(taskFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
        },
      });

      if (showSyncDialog) {
        await syncTaskMutation.mutateAsync({
          source_task_id: updatedTask.id,
          target_case_ids: selectedCaseIds
        });
      }

      setEditingTask(null);
      setShowSyncDialog(false);
    } catch (error) {
      console.error("Failed to update task:", error);
      toast({
        title: "Error",
        description: "Failed to update task",
        variant: "destructive",
      });
    }
  };

  const allTasks =
    tasksData?.pages.flatMap((page: ListResponse<Task>) => page.results) || [];
  const filteredTasks = allTasks.filter((task: Task) => {
    const matchesSearch = searchQuery
      ? task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description?.toLowerCase().includes(searchQuery.toLowerCase())
      : true;
    const matchesDate = selectedDate
      ? task.due_date &&
      format(new Date(task.due_date), "dd-MM-yyyy") === selectedDate
      : true;
    const matchesAssignee = filterAssignee
      ? task.assigned_to?.id.toString() === filterAssignee
      : true;
    return matchesSearch && matchesDate && matchesAssignee;
  });

  // Helper function to format user name
  const formatUserName = (user?: UserDetails | UserDetail | null): string => {
    if (!user) return "";
    if ("name" in user) {
      return user.name?.trim() || user.email || "";
    }
    const userDetail = user as UserDetail;
    return userDetail.name || userDetail.email || "";
  };

  const handleSync = async (selectedCaseIds: string[]) => {
    try {
      if (!selectedTaskForSync) {
        toast({
          title: "Error",
          description: "No task selected for syncing",
          variant: "destructive",
        });
        return;
      }

      if (!selectedCaseIds.length) {
        toast({
          title: "Error",
          description: "No cases selected for syncing",
          variant: "destructive",
        });
        return;
      }

      await syncTaskMutation.mutateAsync({
        source_task_id: selectedTaskForSync,
        target_case_ids: selectedCaseIds
      });

      setShowSyncDialog(false);
      toast({
        title: "Success",
        description: "Task synchronized successfully",
      });
    } catch (error) {
      console.error("Error syncing task:", error);
      toast({
        title: "Error",
        description: "Failed to sync task",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        Loading tasks...
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col min-h-0">
      <div className="flex-none p-4 border-b bg-white">
        <div className="flex flex-col items-stretch gap-2 mb-4">
          <div className="flex flex-wrap gap-2">
            <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
              <CustomDateInput
                value={selectedDate}
                onChange={handleDateChange}
                minDate={new Date()}
                className="w-full"
                error={false}
                placeholder="Filter by date"
              />
            </div>

            <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
              <Select value={filterAssignee} onValueChange={setFilterAssignee}>
                <SelectTrigger className="w-full bg-white rounded-full">
                  <div className="flex items-center gap-2">
                    <User2 className={ICON_CLASSES} />
                    {filterAssignee ? (
                      formatUserName(
                        availableUsers.find(
                          (u) => u.id.toString() === filterAssignee
                        )
                      )
                    ) : (
                      <span className="text-muted-foreground">
                        Assignee
                      </span>
                    )}
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <div className="flex flex-col">
                    <div className="max-h-[200px] overflow-y-auto">
                      {availableUsers.map((user) => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {formatUserName(user)}
                        </SelectItem>
                      ))}
                    </div>
                    {filterAssignee && (
                      <Button
                        variant="ghost"
                        className="mt-2"
                        onClick={() => setFilterAssignee(undefined)}
                      >
                        Clear Selection
                      </Button>
                    )}
                  </div>
                </SelectContent>
              </Select>
            </div>

            <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px] relative">
              <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-muted-foreground" />
              </div>
              <Input
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 rounded-full w-full"
              />
            </div>
          </div>

          {!isExpanded ? (
            <button
              onClick={() => setIsExpanded(true)}
              className="w-full flex items-center gap-2 p-3 border rounded-lg text-muted-foreground hover:bg-gray-50 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add task</span>
            </button>
          ) : (
            <div className="border rounded-lg p-4 space-y-4 bg-white">
              <div className="max-h-[60vh] overflow-y-auto pr-2 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <Input
                  placeholder="Title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="rounded-lg"
                />
                <div className="border rounded-lg overflow-hidden">
                  <CustomCKEditor
                    initialValue={description}
                    onChange={(value: string) => {
                      setDescription(value);
                    }}
                    placeholder="Add description"
                    minHeight="100px"
                    className="min-h-[100px] resize-none bg-gray-50"
                  />
                </div>
                <div className="flex flex-wrap gap-2">
                  <Select
                    value={selectedAssignee}
                    onValueChange={setSelectedAssignee}
                  >
                    <SelectTrigger className="w-[200px] bg-white rounded-lg">
                      <div className="flex items-center gap-2">
                        <User2 className={ICON_CLASSES} />
                        {selectedAssignee ? (
                          formatUserName(
                            availableUsers.find(
                              (u) => u.id.toString() === selectedAssignee
                            )
                          )
                        ) : (
                          <span className="text-muted-foreground">Assign to</span>
                        )}
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <div className="max-h-[200px] overflow-y-auto">
                        {availableUsers.map((user) => (
                          <SelectItem key={user.id} value={user.id.toString()}>
                            <div className="flex items-center gap-2">
                              {selectedAssignee === user.id.toString() && "✓"}
                              {formatUserName(user)}
                            </div>
                          </SelectItem>
                        ))}
                      </div>
                    </SelectContent>
                  </Select>

                  <Select
                    onValueChange={(value) => {
                      const userId = parseInt(value);
                      setSelectedTaggedUsers((prev) =>
                        prev.includes(userId)
                          ? prev.filter((id) => id !== userId)
                          : [...prev, userId]
                      );
                    }}
                  >
                    <SelectTrigger className="w-[200px] bg-white rounded-lg">
                      <div className="flex items-center gap-2">
                        <User2 className={ICON_CLASSES} />
                        <span>Tag users</span>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      {availableUsers.map((user) => (
                        <SelectItem
                          key={user.id}
                          value={user.id.toString()}
                        >
                          <div className="flex items-center gap-2">
                            {selectedTaggedUsers.includes(user.id) && "✓"}
                            {formatUserName(user)}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <CustomDateInput
                    value={selectedDueDate ? selectedDueDate : ""}
                    onChange={handleDueDateChange}
                    minDate={new Date()}
                    className="w-[200px]"
                    error={false}
                    placeholder="Due date"
                  />
                </div>

                {selectedTaggedUsers.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {selectedTaggedUsers.map((userId) => {
                      const user = availableUsers.find((u) => u.id === userId);
                      return (
                        user && (
                          <Badge
                            key={userId}
                            variant="secondary"
                            className="flex items-center gap-1 rounded-full"
                          >
                            {formatUserName(user)}
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                setSelectedTaggedUsers((prev) =>
                                  prev.filter((id) => id !== userId)
                                );
                              }}
                              className="ml-1 hover:text-red-500"
                            >
                              ×
                            </button>
                          </Badge>
                        )
                      );
                    })}
                  </div>
                )}

                <div className="flex justify-end gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsExpanded(false)}
                    className="rounded-full"
                  >
                    Cancel
                  </Button>
                {linkedCasesData && (linkedCasesData.direct_cases.length > 0 || linkedCasesData.indirect_cases.length > 0) && (
                  <Button
                    variant="outline"
                    onClick={() => setShowSyncDialog(true)}
                    className="rounded-full"
                  >
                    Sync Linked Cases
                  </Button>
                )}
                  <Button
                    onClick={handleSubmit}
                    disabled={!title.trim() || createTaskMutation.isPending}
                    className="rounded-full"
                  >
                    {createTaskMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Done"
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto min-h-0 bg-gray-50">
        <div className="p-4 space-y-3">
          {filteredTasks.map((task) => (
            <div
              key={task.id}
              className="group border rounded-lg p-4 hover:shadow-sm bg-white transition-shadow"
            >
              <div className="flex items-start gap-4">
                <div className="mt-1">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button className="focus:outline-none">
                        {(() => {
                          const StatusIcon = StatusIcons[task.status];
                          return (
                            <StatusIcon
                              className={cn(
                                "h-5 w-5",
                                TaskStatusConfig[task.status].color
                              )}
                            />
                          );
                        })()}
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      {Object.values(TaskStatus).map((status) => (
                        <DropdownMenuItem
                          key={status}
                          onClick={() => handleStatusChange(task.id, status)}
                          className={cn(
                            "flex items-center gap-2",
                            status === task.status && "font-bold"
                          )}
                        >
                          {(() => {
                            const StatusIcon = StatusIcons[status];
                            return (
                              <StatusIcon
                                className={cn(
                                  "h-4 w-4",
                                  TaskStatusConfig[status].color
                                )}
                              />
                            );
                          })()}
                          <span>{TaskStatusConfig[status].label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex-1">
                  {editingTask?.id === task.id ? (
                    <div className="space-y-4">
                      <Input
                        value={editingTask.title}
                        onChange={(e) =>
                          setEditingTask((prev) =>
                            prev
                              ? {
                                ...prev,
                                title: e.target.value,
                              }
                              : null
                          )
                        }
                        className="font-medium rounded-lg"
                        placeholder="Task title"
                      />
                      <div className="border rounded-lg overflow-hidden">
                        <RichTextEditor
                          value={editingTask.description}
                          onChange={(value) =>
                            setEditingTask((prev) =>
                              prev
                                ? {
                                  ...prev,
                                  description: value,
                                }
                                : null
                            )
                          }
                          placeholder="Task description"
                          className="min-h-[100px]"
                        />
                      </div>
                      <div className="flex flex-wrap items-start gap-2">
                        <Select
                          value={editingTask.assigned_to_id?.toString()}
                          onValueChange={(value) =>
                            setEditingTask((prev) =>
                              prev
                                ? {
                                  ...prev,
                                  assigned_to_id: value
                                    ? parseInt(value)
                                    : undefined,
                                }
                                : null
                            )
                          }
                        >
                          <SelectTrigger className="w-[200px] bg-white rounded-lg">
                            <div className="flex items-center gap-2">
                              <User2 className={ICON_CLASSES} />
                              {editingTask.assigned_to_id ? (
                                formatUserName(
                                  availableUsers.find(
                                    (u) => u.id === editingTask.assigned_to_id
                                  )
                                )
                              ) : (
                                <span className="text-muted-foreground">
                                  Assign to
                                </span>
                              )}
                            </div>
                          </SelectTrigger>
                          <SelectContent>
                            <div className="max-h-[200px] overflow-y-auto">
                              {availableUsers.map((user) => (
                                <SelectItem
                                  key={user.id}
                                  value={user.id.toString()}
                                >
                                  <div className="flex items-center gap-2">
                                    {editingTask.assigned_to_id === user.id &&
                                      "✓"}
                                    {formatUserName(user)}
                                  </div>
                                </SelectItem>
                              ))}
                            </div>
                          </SelectContent>
                        </Select>

                        <Select
                          onValueChange={(value) => {
                            const userId = parseInt(value);
                            setEditingTask((prev) =>
                              prev
                                ? {
                                  ...prev,
                                  tagged_user_ids:
                                    prev.tagged_user_ids.includes(userId)
                                      ? prev.tagged_user_ids.filter(
                                        (id) => id !== userId
                                      )
                                      : [...prev.tagged_user_ids, userId],
                                }
                                : null
                            );
                          }}
                        >
                          <SelectTrigger className="w-[200px] bg-white rounded-lg">
                            <div className="flex items-center gap-2">
                              <User2 className={ICON_CLASSES} />
                              <span>Tag users</span>
                            </div>
                          </SelectTrigger>
                          <SelectContent>
                            {availableUsers.map((user) => (
                              <SelectItem
                                key={user.id}
                                value={user.id.toString()}
                              >
                                <div className="flex items-center gap-2">
                                  {editingTask.tagged_user_ids.includes(
                                    user.id
                                  ) && "✓"}
                                  {formatUserName(user)}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <CustomDateInput
                          value={
                            editingTask.due_date
                              ? format(
                                new Date(editingTask.due_date),
                                "dd-MM-yyyy"
                              )
                              : ""
                          }
                          onChange={handleEditingDueDateChange}
                          minDate={new Date()}
                          className="w-[200px]"
                          error={false}
                          placeholder="Due date"
                        />
                      </div>

                      {editingTask.tagged_user_ids.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {editingTask.tagged_user_ids.map((userId) => {
                            const user = availableUsers.find(
                              (u) => u.id === userId
                            );
                            return (
                              user && (
                                <Badge
                                  key={userId}
                                  variant="secondary"
                                  className="flex items-center gap-1 rounded-full"
                                >
                                  {formatUserName(user)}
                                  <button
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setEditingTask((prev) =>
                                        prev
                                          ? {
                                            ...prev,
                                            tagged_user_ids:
                                              prev.tagged_user_ids.filter(
                                                (id) => id !== userId
                                              ),
                                          }
                                          : null
                                      );
                                    }}
                                    className="ml-1 hover:text-red-500"
                                  >
                                    ×
                                  </button>
                                </Badge>
                              )
                            );
                          })}
                        </div>
                      )}

                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => setEditingTask(null)}
                          className="rounded-full"
                        >
                          Cancel
                        </Button>
                        {linkedCasesData && (linkedCasesData.direct_cases.length > 0 || linkedCasesData.indirect_cases.length > 0) && (
                          <Button
                            variant="outline"
                            onClick={() => setShowSyncDialog(true)}
                            className="rounded-full"
                          >
                            Sync Linked Cases
                          </Button>
                        )}
                        <Button onClick={handleEdit} className="rounded-full">
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="font-medium">{task.title}</div>
                      {task.description && (
                        <div className="mt-1 text-sm text-gray-600">
                          <RichTextViewer data={task.description} />
                        </div>
                      )}
                      <div className="mt-2 flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          {task.due_date && (
                            <div className="flex items-center gap-1">
                              <Calendar className={ICON_CLASSES} />
                              {format(new Date(task.due_date), "dd MMM yyyy")}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="secondary"
                            className={TaskStatusConfig[task.status].color}
                          >
                            {TaskStatusConfig[task.status].label}
                          </Badge>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="invisible group-hover:visible h-8 w-8"
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {/* {allLinkedCases.length > 0 && (
                                <DropdownMenuItem
                                  className="gap-2"
                                  onClick={() => {
                                    setSelectedTaskForSync(task.id);
                                    setShowSyncDialog(true);
                                  }}
                                  disabled={syncTaskMutation.isPending}
                                >
                                  {syncTaskMutation.isPending ? (
                                    <>
                                      <span className="loading loading-spinner loading-xs mr-2"></span>
                                      Syncing...
                                    </>
                                  ) : (
                                    <>
                                      <Repeat className="h-4 w-4" />
                                      <span>Sync Task</span>
                                    </>
                                  )}
                                </DropdownMenuItem>
                              )} */}
                              <DropdownMenuItem
                                onClick={() =>
                                  setEditingTask({
                                    id: task.id,
                                    title: task.title,
                                    description: task.description || "",
                                    assigned_to_id: task.assigned_to?.id,
                                    tagged_user_ids: task.tagged_users.map((u) => u.id),
                                    due_date: task.due_date,
                                  })
                                }
                                className="gap-2"
                              >
                                <Pencil className="h-4 w-4" />
                                <span>Edit</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDelete(task.id)}
                                className="gap-2 text-red-600"
                              >
                                <Trash2 className="h-4 w-4" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      {task.tagged_users.length > 0 && (
                        <div className="mt-2 flex gap-1">
                          {task.tagged_users.map((user: UserDetail) => (
                            <Badge key={user.id} variant="secondary">
                              {formatUserName(user)}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}

          <div ref={loadMoreRef} className="h-4">
            {isFetchingNextPage && (
              <div className="text-center text-sm text-gray-500">
                Loading more tasks...
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add SyncCaseDialog at the end of the component */}
      <SyncCaseDialog
        caseId={caseId}
        syncType="tasks"
        isOpen={showSyncDialog}
        onClose={() => {
          setShowSyncDialog(false);
          setSelectedCaseIds([]);
        }}
        onSync={(selectedIds) => {
          setSelectedCaseIds(selectedIds);
          if (editingTask) {
            handleEdit();
          } else {
            handleSubmit();
          }
        }}
        isSyncing={syncTaskMutation.isPending}
      />
    </div>
  );
}
