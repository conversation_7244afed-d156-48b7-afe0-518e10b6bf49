import React, { <PERSON>, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Edit, Plus, Trash2, User, Users, Repeat } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import AddEditCaseParty from "@/app/case/caseparty/components/AddEditCaseParty";
import { PartyType, PartyStatus } from "@/type/case-management/partyTypes";
import ExpertWitnessList from "@/app/case/caseparty/components/ExpertWitnessList";
import ManageEmailTemplate from "@/components/ManageEmailTemplate";
import { useCasePartiesQuery, useCaseExpertWitnessesQuery, useDeleteCasePartyMutation } from "@/services/case-management/partyService";
import { CaseParty } from "@/type/case-management/partyTypes";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import SelectWitness from "@/app/case/caseparty/components/SelectWitness";
import { KPICard } from "@/components/ui/kpi-card";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { AddEditCost } from "./AddEditCost";
import { ContactType } from "@/type/case-management/orgTypes";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import { AddressLink } from "@/components/gMap/address-link";
import { usePartySyncMutation } from '@/services/case-management/caseSyncService';
import SyncCaseDialog from './SyncCaseDialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";

interface WitnessDetailsProps {
  caseId: string;
}

const WitnessDetails: FC<WitnessDetailsProps> = ({ caseId }) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedParty, setSelectedParty] = useState<CaseParty | null>(null);
  const [showSyncDialog, setShowSyncDialog] = useState(false);
  const [selectedWitnessForSync, setSelectedWitnessForSync] = useState<number | null>(null);

  const { data: parties = [], isLoading: isLoadingParties, refetch: refetchParties } = useCasePartiesQuery(caseId);
  const { data: expertWitnesses = [], isLoading: isLoadingExpertWitnesses, refetch: refetchExpertWitnesses } = useCaseExpertWitnessesQuery(caseId);
  const deletePartyMutation = useDeleteCasePartyMutation(caseId, selectedParty?.id || 0);
  const syncParty = usePartySyncMutation(caseId);

  const handleWitnessSyncComplete = () => {
    refetchParties();
  };

  const handleExpertWitnessSyncComplete = () => {
    refetchExpertWitnesses();
  };

  const handleSync = async (selectedCaseIds: string[]) => {
    try {
      if (!selectedCaseIds.length) {
        toast({
          title: "Error",
          description: "No cases selected for syncing",
          variant: "destructive",
        });
        return;
      }

      if (!selectedWitnessForSync) {
        toast({
          title: "Error",
          description: "No witness selected for syncing",
          variant: "destructive",
        });
        return;
      }

      await syncParty.mutateAsync({
        party_id: selectedWitnessForSync,
        target_case_ids: selectedCaseIds
      });

      setShowSyncDialog(false);
      handleWitnessSyncComplete();
      toast({
        title: "Success",
        description: "Witness data synchronized successfully",
      });
    } catch (error) {
      console.error("Error syncing witness:", error);
      toast({
        title: "Error",
        description: "Failed to sync witness data",
        variant: "destructive",
      });
    }
  };

  if (isLoadingParties || isLoadingExpertWitnesses) {
    return <div className="min-h-screen bg-gray-50 p-8">Loading...</div>;
  }

  const witnesses = parties.filter((party: CaseParty) => party.contact?.party_type === PartyType.WITNESS);

  const handleDeleteParty = (party: CaseParty) => {
    setSelectedParty(party);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedParty) {
      try {
        await deletePartyMutation.mutateAsync();
        setDeleteDialogOpen(false);
        setSelectedParty(null);
      } catch (error) {
        console.error('Error deleting party:', error);
      }
    }
  };

  return (
    <>
      <div className="flex items-end justify-end mb-6">
        {/* <div className="flex items-center gap-1.5">
          <User className="h-4 w-4 text-[#060216]" />
          <h2 className="text-[14px] font-medium text-[#060216]">
            Witnesses ({witnesses.length})
          </h2>
        </div> */}
        <AddEditCaseParty
          caseId={caseId}
          defaultValues={{
            party_type: PartyType.WITNESS,
            status: PartyStatus.PENDING_CONTACT,
            party_contact: ""
          }}
          trigger={
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4" />
              Witness
            </Button>
          }
        />
        <SelectWitness
          caseId={caseId}
        />
      </div>

      <div className="grid grid-cols-4 gap-4 mb-6">
        <KPICard
          icon={User}
          title="Witnesses"
          metrics={{
            primary: {
              type: 'count',
              value: witnesses.length
            },
          }}
        />
        <KPICard
          icon={Users}
          title="Expert Witnesses"
          metrics={{
            primary: {
              type: 'count',
              value: expertWitnesses.length
            },
          }}
        />
      </div>

      <Tabs defaultValue="witness" className="w-full">
        <TabsList>
          <TabsTrigger value="witness">Witness ({witnesses.length})</TabsTrigger>
          <TabsTrigger value="expert-witness">Expert Witness ({expertWitnesses.length})</TabsTrigger>
        </TabsList>
        <TabsContent value="witness">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Witnesses</h2>
          </div>
          {witnesses.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No witnesses found.</div>
          ) : (
            <div className="space-y-6">
              {witnesses.map((party, index) => (
                <div key={party.id} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-1.5">
                      <User className="w-4 h-4" />
                      <h2 className="text-[14px] font-medium leading-5">Witness - {index + 1}</h2>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className={`${syncParty.isPending ? 'cursor-not-allowed' : ''}`}>
                              <Button
                                variant="outline"
                                className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
                                onClick={() => {
                                  setSelectedWitnessForSync(party.contact?.id || 0);
                                  setShowSyncDialog(true);
                                }}
                                disabled={syncParty.isPending}
                              >
                                {syncParty.isPending ? (
                                  <>
                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                  </>
                                ) : (
                                  <>
                                    <Repeat className="h-4 w-4 mr-2" />
                                  </>
                                )}
                              </Button>
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            Sync Witness Data
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider> */}
                      <AddEditCaseParty
                        caseId={caseId}
                        isEdit={true}
                        defaultValues={{
                          status: party.status,
                          description: party.description,
                          party_contact: party.contact?.id?.toString(),
                          law_firm: party.law_firm?.id?.toString(),
                          attorney: party.attorney?.id?.toString(),
                          party_type: party.contact.party_type,
                        }}
                        partyId={party.id.toString()}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50 h-8 w-8"
                          >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 h-8 w-8"
                        onClick={() => handleDeleteParty(party)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600 cursor-pointer" />
                      </Button>
                      <ManageEmailTemplate
                        caseId={caseId}
                        contextType="witness"
                        witness_id={party.id.toString()}
                      />
                    </div>
                  </div>

                  <InfoFieldGroup
                    className="mb-5"
                    caseId={caseId}
                    fields={[
                      {
                        label: "Name",
                        value: `${party.contact?.first_name || ''} ${party.contact?.last_name || ''}`,
                      },
                      {
                        label: "Type",
                        value: "Witness",
                      },
                      {
                        label: "Status",
                        value: party.status || '—'
                      },
                      {
                        label: "Primary Phone",
                        value: party.contact?.phone || '—',
                        isPhone: true
                      },
                      {
                        label: "Secondary Phone",
                        value: party.contact?.cell || '—',
                        isPhone: true
                      },
                      {
                        label: "Email",
                        value: party.contact?.email || '—',
                        isMail: true
                      },
                      {
                        label: "Address",
                        value: <AddressLink address={{
                          street1: party.contact?.street1 || undefined,
                          street2: party.contact?.street2 || undefined,
                          city: party.contact?.city || undefined,
                          state: party.contact?.state || undefined,
                          zip_code: party.contact?.zip_code || undefined
                        }} />
                      }
                    ]}
                  />

                  <div className="bg-[#F7F7F7] border border-black/[0.08] rounded-lg p-4">
                    <div className="space-y-0.5">
                      <p className="text-[#060216] opacity-50 text-sm font-medium">Notes</p>
                      <p className="text-[#060216] text-sm leading-5 font-semibold">
                        {party.description ? <RichTextViewer data={party.description} /> : '—'}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
        <TabsContent value="expert-witness">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Expert Witnesses</h2>
            <div className="flex items-center gap-2">
              <AddEditCost
                isEdit={false}
                defaultContactType={ContactType.EXPERT_WITNESS}
                onSuccess={() => { }}
              />
            </div>
          </div>
          <ExpertWitnessList
            caseId={caseId}
            handleExpertWitnessSyncComplete={handleExpertWitnessSyncComplete}
          />
        </TabsContent>
      </Tabs>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        title="Delete Witness"
        description={`Are you sure you want to delete ${selectedParty?.contact?.first_name} ${selectedParty?.contact?.last_name}? This action cannot be undone.`}
      />

      <SyncCaseDialog
        caseId={caseId}
        syncType="parties"
        isOpen={showSyncDialog}
        onClose={() => setShowSyncDialog(false)}
        onSync={handleSync}
        isSyncing={syncParty.isPending}
      />
    </>
  );
};

export default WitnessDetails; 