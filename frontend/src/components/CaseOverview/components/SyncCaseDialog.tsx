'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { AlertCircle, Loader2, Repeat, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from '@/components/ui/checkbox';
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert';

interface StatusDetails {
    display_name?: string;
    status?: string;
}

const formSchema = z.object({
    search: z.string(),
});

export type SyncType = 
  | "status" 
  | "defendants" 
  | "notes" 
  | "tasks" 
  | "userCards" 
  | "client" 
  | "incident" 
  | "parties" 
  | "expert-witnesses" 
  | "providers" 
  | "workers" 
  | "attorney-liens" 
  | "misc-liens"
  | "client-insurance";

interface SyncCaseDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onSync: (selectedCaseIds: string[]) => void;
    isSyncing: boolean;
    caseId: string;
    syncType: SyncType;
}

interface SearchParams {
    search: string;
}

export default function SyncCaseDialog({
    isOpen,
    onClose,
    onSync,
    isSyncing,
    caseId,
    syncType
}: SyncCaseDialogProps) {
    const [searchParams, setSearchParams] = useState<SearchParams>({
        search: "",
    });
    const [selectedCases, setSelectedCases] = useState<Set<string>>(new Set());

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            search: "",
        },
    });

    // Reset all fields when dialog closes
    const handleClose = () => {
        form.reset();
        setSearchParams({ search: "" });
        setSelectedCases(new Set());
        onClose();
    };

    // Reset fields when dialog opens/closes
    useEffect(() => {
        if (!isOpen) {
            form.reset();
            setSearchParams({ search: "" });
            setSelectedCases(new Set());
        }
    }, [isOpen, form]);

    // Fetch linked cases using the hook
    const { data: linkedCasesData, isLoading, isError } = useLinkedCasesQuery(caseId);
    
    // Wrap linkedCases initialization in useMemo
    const linkedCases = useMemo(() => 
        linkedCasesData?.direct_cases || [],
        [linkedCasesData]
    );

    function onSubmit(values: z.infer<typeof formSchema>) {
        setSearchParams((prev: SearchParams) => ({
            ...prev,
            search: values.search
        }));
    }

    useEffect(() => {
        const timer = setTimeout(() => {
            if (searchParams.search) {
                form.handleSubmit(onSubmit)();
            }
        }, 500);

        return () => clearTimeout(timer);
    }, [searchParams.search, form]);

    const filteredCases = useMemo(() => {
        if (!searchParams.search) return linkedCases;
        
        const searchLower = searchParams.search.toLowerCase();
        return linkedCases.filter(caseItem => 
            caseItem.case_name?.toLowerCase().includes(searchLower) ||
            caseItem.name_of_client?.toLowerCase().includes(searchLower) ||
            (caseItem.accident_date && format(new Date(caseItem.accident_date), 'MM/dd/yyyy').includes(searchLower))
        );
    }, [linkedCases, searchParams.search]);

    const headers = [
        { key: 'checkbox', label: '' },
        { key: 'case_name', label: 'Case Name', width: 'w-[300px]' },
        { key: 'client', label: 'Client' },
        { key: 'date_of_loss', label: 'Date of Loss' },
        { key: 'status', label: 'Status' }
    ];

    const getStatusDisplay = (statusDetails: StatusDetails | string | null | undefined): string => {
        if (!statusDetails) return "—";
        if (typeof statusDetails === 'string') return statusDetails;
        if (typeof statusDetails === 'object') {
            return statusDetails.display_name || statusDetails.status || "—";
        }
        return "—";
    };

    const handleCheckboxChange = (caseId: string) => {
        setSelectedCases((prev: Set<string>) => {
            const newSelected = new Set(prev);
            if (newSelected.has(caseId)) {
                newSelected.delete(caseId);
            } else {
                newSelected.add(caseId);
            }
            return newSelected;
        });
    };

    const handleSync = () => {
        // Get the selected case IDs and ensure they exist in linkedCases
        const validSelectedCases = Array.from(selectedCases).filter(caseId => 
            linkedCases.some(caseItem => caseItem.id === caseId)
        ) as string[];

        // Only proceed if we have valid selected cases
        if (validSelectedCases.length > 0) {
            onSync(validSelectedCases);
        }
    };

    const isSelected = (caseId: string) => selectedCases.has(caseId);

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
                        Sync {syncType.charAt(0).toUpperCase() + syncType.slice(1)} Data
                    </DialogTitle>
                    <DialogDescription>
                        Select the cases you want to sync {syncType} data with
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Search Form */}
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="grid grid-cols-1 gap-4">
                                <FormField
                                    control={form.control}
                                    name="search"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <div className="flex items-center">
                                                    <Search className="w-4 h-4 inline-block mr-2" />
                                                    <Input
                                                        {...field}
                                                        className="bg-white"
                                                        placeholder="Search by case name, client name, or date"
                                                        onChange={(e) => {
                                                            field.onChange(e);
                                                            setSearchParams((prev: SearchParams) => ({
                                                                ...prev,
                                                                search: e.target.value
                                                            }));
                                                        }}
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </form>
                    </Form>

                    {/* Loading State */}
                    {isLoading && (
                        <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                        </div>
                    )}

                    {/* Error State */}
                    {isError && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                                Failed to load linked cases. Please try again later.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Linked Cases Table */}
                    {!isLoading && !isError && (
                        <div className="max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                            <Table>
                                <TableHeader className="sticky top-0 bg-white z-10">
                                    <TableRow className="bg-[#060216]/5">
                                        {headers.map(header => (
                                            <TableHead
                                                key={header.key}
                                                className={`${header.width || ''} text-[#060216]/50`}
                                            >
                                                {header.label}
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredCases.map((caseItem) => (
                                        <TableRow
                                            key={caseItem.id}
                                            className="hover:bg-[#060216]/5"
                                        >
                                            <TableCell className="text-[#060216]">
                                                <Checkbox
                                                    checked={isSelected(caseItem.id)}
                                                    onCheckedChange={() => handleCheckboxChange(caseItem.id)}
                                                />
                                            </TableCell>
                                            <TableCell className="font-medium text-[#060216]">
                                                {caseItem.case_name}
                                            </TableCell>
                                            <TableCell className="text-[#060216]">
                                                {caseItem.name_of_client}
                                            </TableCell>
                                            <TableCell className="text-[#060216]">
                                                {caseItem.accident_date ?
                                                    format(new Date(caseItem.accident_date), 'MM/dd/yyyy')
                                                    : "—"}
                                            </TableCell>
                                            <TableCell className="text-[#060216]">
                                                {getStatusDisplay(caseItem.organization_status_details)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-2">
                        <Button
                            variant="outline"
                            type="button"
                            onClick={handleClose}
                            disabled={isSyncing}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="button"
                            onClick={handleSync}
                            disabled={isSyncing || selectedCases.size === 0}
                            className="bg-green-600 hover:bg-green-700 text-white"
                        >
                            {isSyncing ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Syncing...
                                </>
                            ) : (
                                <>
                                    <Repeat className="h-4 w-4 mr-2" />
                                    Sync Selected Cases ({selectedCases.size})
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
