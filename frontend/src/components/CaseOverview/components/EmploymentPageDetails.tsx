'use client';

import { <PERSON><PERSON> } from '@/components/ui/button'
import { FileText, Building, ShieldCheck, Edit, Plus, Trash2, Upload } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AddEditEmployer } from '@/app/case/employment/components/AddEditEmployer';
import { AddEditWorkerCompensation } from '@/app/case/employment/components/AddEditWorkerCompensation';
import { useEmployersQuery, useDeleteEmployerMutation } from '@/services/case-management/employerService';
import { Skeleton } from '@/components/ui/skeleton';
import { DeleteConfirmationDialog } from '@/components/common/DeleteConfirmationDialog';
import { useState, useRef, ChangeEvent } from 'react';
import { AddressLink } from '@/components/gMap/address-link';
import { CaseSection, useUploadBySectionMutation } from '@/services/case-management/newDocumentManagement';
import { MailLink } from '@/components/ui/mail-link';
import { InfoFieldGroup } from '@/components/ui/InfoField';
import { KPICard } from '@/components/ui/kpi-card';
import CKViewer from '@/components/ckeditor/CKViewer';


// import { AttorneyResponse } from '@/type/case-management/orgTypes';
// import { EmployerResponse } from '@/type/case-management/employerTypes';

// const formatAttorneyName = (attorney: AttorneyResponse | undefined) => {
//     if (!attorney) return '';
//     return [attorney.first_name, attorney.middle_name, attorney.last_name]
//         .filter(Boolean)
//         .join(' ');
// };

function EmploymentPageDetails({ caseId }: { caseId: string }) {
    const { data: employers, isLoading, error } = useEmployersQuery(caseId);
    const [employerToDelete, setEmployerToDelete] = useState<{ id: string; name: string } | null>(null);
    const deleteMutation = useDeleteEmployerMutation(caseId);
    const uploadBySection = useUploadBySectionMutation();

    // Create a ref object to store all file input refs
    const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

    const setFileInputRef = (employerId: string) => (el: HTMLInputElement | null) => {
        if (el) {
            fileInputRefs.current[employerId] = el;
        }
    };

    const handleDelete = (employerId: string) => {
        deleteMutation.mutate(employerId);
        setEmployerToDelete(null);
    };

    const handleFileUpload = async (employerId: string, event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            await uploadBySection.mutateAsync({
                case_id: caseId,
                section: CaseSection.LOSS_OF_INCOME,
                file: file,
                employment_record_id: employerId
            });

            // Clear the file input
            if (event.target) {
                event.target.value = '';
            }
        } catch (error) {
            console.error("Upload error:", error);
        }
    };

    const handleUploadClick = (employerId: string) => {
        fileInputRefs.current[employerId]?.click();
    };

    // Calculate total lost wages from actual data
    const totalLostWages = employers?.reduce((sum, employer) => {
        const wages = employer.total_lost_wages ? parseFloat(employer.total_lost_wages) : 0;
        return sum + wages;
    }, 0) || 0;

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="text-center text-red-600">
                    Failed to load employers. Please try again later.
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen">
            <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    <span className="text-sm font-medium">Employer</span>
                </div>
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2 text-emerald-600">
                        <AddEditEmployer caseId={caseId} isEdit={false} />
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-4 gap-4 mb-10">
                <KPICard
                    icon={ShieldCheck}
                    title="TOTAL LOST WAGES"
                    metrics={{
                        primary: {
                            type: 'monetary',
                            value: isLoading ? 0 : totalLostWages || 0
                        }
                    }}
                    isCurrency={true}
                    variant="dashboard"
                    bgColor="bg-gray-50"
                    description={isLoading ? "Loading..." : undefined}
                />
            </div>

            {isLoading ? (
                // Loading skeletons
                [...Array(2)].map((_, index) => (
                    <Card key={index} className="w-full mb-4">
                        <CardContent className="p-6">
                            <Skeleton className="h-24 w-full" />
                        </CardContent>
                    </Card>
                ))
            ) : employers && employers.length > 0 ? (
                employers.map((employer) => (
                    <Card key={employer.id} className="w-full mb-6 bg-white rounded-lg shadow">
                        <CardContent className="p-6">
                            <div className="flex justify-between items-center mb-4">
                                <div className="flex items-center gap-2">
                                    <Building className="h-5 w-5" />
                                    <span className="text-sm font-medium">Employer</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-gray-500">Position:</span>
                                    <span className="text-sm text-blue-600">{employer.position || "—"}</span>
                                </div>
                            </div>
                            <div className="flex items-center justify-end gap-2">
                                <AddEditEmployer caseId={caseId} isEdit={true} initialData={employer} />
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => setEmployerToDelete({
                                        id: employer.id.toString(),
                                        name: employer.company_name
                                    })}
                                    className="hover:bg-red-100"
                                >
                                    <Trash2 className="h-4 w-4 text-red-600" />
                                </Button>
                                <input
                                    type="file"
                                    ref={setFileInputRef(employer.id.toString())}
                                    onChange={(e) => handleFileUpload(employer.id.toString(), e)}
                                    className="hidden"
                                    accept=".pdf,.doc,.docx"
                                    disabled={uploadBySection.isPending}
                                />
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            className="hover:bg-emerald-50 h-8 w-8 rounded-full"
                                            disabled={uploadBySection.isPending}
                                        >
                                            <FileText className="h-4 w-4 text-emerald-600" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent
                                        align="end"
                                        className="w-[220px] shadow-lg rounded-lg border border-zinc-200"
                                    >
                                        <DropdownMenuItem
                                            className="hover:bg-emerald-50 cursor-pointer py-2 text-emerald-600"
                                            onClick={() => handleUploadClick(employer.id.toString())}
                                            disabled={uploadBySection.isPending}
                                        >
                                            {uploadBySection.isPending ? (
                                                <>
                                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                                    Uploading...
                                                </>
                                            ) : (
                                                <>
                                                    <Upload className="mr-2 h-4 w-4 stroke-[1.5]" />
                                                    <span>Upload Loss of Wages</span>
                                                </>
                                            )}
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                            {/* Company Name Section */}
                            <InfoFieldGroup
                                fields={[
                                    {
                                        label: "Company Name",
                                        value: employer.company_name || "—",
                                    },
                                    {
                                        label: "Primary",
                                        value: employer.phone || "—",
                                        isPhone: true
                                    },
                                    {
                                        label: "Fax",
                                        value: employer.fax || "—",
                                        isPhone: true
                                    },
                                    {
                                        label: "Address",
                                        value: <AddressLink
                                            address={{
                                                street1: employer.street1 || undefined,
                                                street2: employer.street2 || undefined,
                                                city: employer.city || undefined,
                                                state: employer.state || undefined,
                                                zip_code: employer.zip_code || undefined
                                            }}
                                        >
                                            {employer.street1 ? undefined : '—'}
                                        </AddressLink>
                                    },
                                    {
                                        label: "Wage",
                                        value: employer.wage ? `$${employer.wage} / Hr` : '—'
                                    },
                                    {
                                        label: "Time missed",
                                        value: employer.weeks_missed && employer.hours_missed ? `${employer.weeks_missed}w ${employer.hours_missed}h` : '—'
                                    },
                                    {
                                        label: "Total lost wages",
                                        value: employer.total_lost_wages ? `$${employer.total_lost_wages}` : '—'
                                    }
                                ]}
                            />

                            {/* Notes Section */}
                            <div className="bg-[#F7F7F7] p-2 rounded-lg border border-[#00000014] mt-4">
                                <div className="flex flex-col px-2 py-2">
                                    <span className="text-[#06021699] text-xs leading-[21px] font-medium font-Manrope mb-[2px]">
                                        Description
                                    </span>
                                    <div className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
                                        {employer.description ? <CKViewer content={employer.description} /> : "—"}
                                    </div>
                                </div>
                            </div>

                            <div className="border-t border-gray-300 my-4"></div>

                            {/* Worker Compensation Section */}
                            {employer.workers_compensation ? (
                                <div className="mt-8">
                                    <div className="flex justify-between items-center mb-6">
                                        <div className="flex items-center gap-2">
                                            <Building className="h-5 w-5" />
                                            <span className="text-sm font-medium">Worker Compensation</span>
                                        </div>
                                        <div className="text-sm text-blue-600">
                                            Status: {employer.workers_compensation.status || '—'}
                                        </div>
                                    </div>

                                    {/* Status, Law Firm, Attorney Grid */}
                                    <InfoFieldGroup
                                        fields={[
                                            {
                                                label: "Law Firm",
                                                value: employer.workers_compensation.law_firm && typeof employer.workers_compensation.law_firm === 'object'
                                                    ? employer.workers_compensation.law_firm.office_name
                                                    : employer.workers_compensation.law_firm || '—'
                                            },
                                            {
                                                label: "Attorney",
                                                value: employer.workers_compensation.attorney && typeof employer.workers_compensation.attorney === 'object'
                                                    ? `${employer.workers_compensation.attorney.first_name} ${employer.workers_compensation.attorney.last_name}`
                                                    : employer.workers_compensation.attorney || '—'
                                            }
                                        ]}
                                        columns={3}
                                        className="mb-6"
                                    />

                                    {/* Notes Section */}
                                    <div className="bg-[#F7F7F7] p-2 rounded-lg border border-[#00000014] mb-4">
                                        <div className="flex flex-col px-2 py-2">
                                            <span className="text-[#06021699] text-xs leading-[21px] font-medium font-Manrope mb-[2px]">
                                                Notes
                                            </span>
                                            <div className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
                                                {employer.workers_compensation.note ? <CKViewer content={employer.workers_compensation.note} /> : "—"}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Contact Information Grid */}
                                    <InfoFieldGroup
                                        fields={[
                                            {
                                                label: "Primary",
                                                value: employer.workers_compensation.law_firm && typeof employer.workers_compensation.law_firm === 'object'
                                                    ? employer.workers_compensation.law_firm.phone || '—'
                                                    : '—',
                                                isPhone: true
                                            },
                                            {
                                                label: "Fax",
                                                value: employer.workers_compensation.law_firm && typeof employer.workers_compensation.law_firm === 'object'
                                                    ? employer.workers_compensation.law_firm.fax || '—'
                                                    : '—',
                                                isPhone: true
                                            },
                                            {
                                                label: "Email",
                                                value: <MailLink email={employer.workers_compensation.law_firm && typeof employer.workers_compensation.law_firm === 'object'
                                                    ? employer.workers_compensation.law_firm.email || "—"
                                                    : "—"} caseId={caseId} />
                                            }
                                        ]}
                                        columns={3}
                                        className="mb-6"
                                    />

                                    {/* Insurance Company Section */}
                                    {employer.workers_compensation.insurance_company && (
                                        <div className="mt-6">
                                            <div className="flex items-center gap-2 mb-4">
                                                <ShieldCheck className="h-5 w-5 text-gray-500" />
                                                <span className="text-sm font-medium">
                                                    Insurance - {typeof employer.workers_compensation.insurance_company === 'object'
                                                        ? employer.workers_compensation.insurance_company.name
                                                        : employer.workers_compensation.insurance_company}
                                                </span>
                                            </div>

                                            <InfoFieldGroup
                                                fields={[
                                                    {
                                                        label: "Policy",
                                                        value: employer.workers_compensation.insurance_policy_number || '—'
                                                    },
                                                    {
                                                        label: "Claim",
                                                        value: employer.workers_compensation.insurance_claim_number || '—'
                                                    },
                                                    {
                                                        label: "Phone",
                                                        value: typeof employer.workers_compensation.insurance_company === 'object'
                                                            ? employer.workers_compensation.insurance_company.phone || '—'
                                                            : '—',
                                                        isPhone: true
                                                    }
                                                ]}
                                                columns={3}
                                            />
                                        </div>
                                    )}

                                    {/* Actions */}
                                    <div className="flex justify-end gap-2 mt-4">
                                        <AddEditWorkerCompensation
                                            isEdit
                                            caseId={caseId}
                                            employerId={employer.id.toString()}
                                            initialData={{
                                                ...employer.workers_compensation,
                                                law_firm: employer.workers_compensation.law_firm && typeof employer.workers_compensation.law_firm === 'object'
                                                    ? employer.workers_compensation.law_firm.id
                                                    : employer.workers_compensation.law_firm || undefined,
                                                attorney: employer.workers_compensation.attorney && typeof employer.workers_compensation.attorney === 'object'
                                                    ? employer.workers_compensation.attorney.id
                                                    : employer.workers_compensation.attorney || undefined,
                                                insurance_company: employer.workers_compensation.insurance_company && typeof employer.workers_compensation.insurance_company === 'object'
                                                    ? employer.workers_compensation.insurance_company.id
                                                    : employer.workers_compensation.insurance_company || undefined,
                                            }}
                                        >
                                            <Button variant="ghost" size="icon">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                        </AddEditWorkerCompensation>
                                    </div>
                                </div>
                            ) : (
                                <div className="mt-8">
                                    <div className="flex justify-between items-center mb-6">
                                        <div className="flex items-center gap-2">
                                            <Building className="h-5 w-5" />
                                            <span className="text-sm font-medium">Worker Compensation</span>
                                        </div>
                                        <AddEditWorkerCompensation
                                            caseId={caseId}
                                            employerId={employer.id.toString()}
                                        >
                                            <Button variant="outline" size="sm">
                                                <Plus className="h-4 w-4 mr-2" />
                                                Add Worker Compensation
                                            </Button>
                                        </AddEditWorkerCompensation>
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        No worker compensation information added yet.
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                ))
            ) : (
                <div className="text-center text-gray-500 mt-8">
                    No employers found. Add an employer to get started.
                </div>
            )}
            <DeleteConfirmationDialog
                open={!!employerToDelete}
                onOpenChange={(open) => !open && setEmployerToDelete(null)}
                onConfirm={() => employerToDelete && handleDelete(employerToDelete.id)}
                title="Delete Employer"
                description={`Are you sure you want to delete ${employerToDelete?.name}? This will also delete any associated worker's compensation information. This action cannot be undone.`}
            />
        </div>
    );
}

export default EmploymentPageDetails;