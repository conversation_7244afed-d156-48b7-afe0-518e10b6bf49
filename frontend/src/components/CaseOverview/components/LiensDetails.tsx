"use client"

import { notFound } from 'next/navigation'
import { Trash2, Monitor, CircleDollarSign, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { AddEditAtorneyLiens } from '@/app/case/liens/components/AddEditAtorneyLiens'
import { AddEditMiscLiens } from '@/app/case/liens/components/AddEditMiscLiens'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent } from "@/components/ui/card"
import { useAttorneyLiensQuery, useMiscLiensQuery, useDeleteAttorneyLienMutation, useDeleteMiscLienMutation, useUpdateAttorneyLienFinalStatusMutation, useUpdateMiscLienFinalStatusMutation } from "@/services/case-management/lienService"
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService'
import { useState } from "react"
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog"
import { KPICard } from "@/components/ui/kpi-card"
import { Checkbox } from '@/components/ui/checkbox'
import { formatDateForDisplay, formatDateForApi } from "@/utils/dateUtils"
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { SyncCase } from '@/components/syncCase'
import { useToast } from "@/hooks/use-toast"

interface CaseLienPageProps {
    caseId: string
}

export default function LiensDetails({ caseId }: CaseLienPageProps) {
    const { data: attorneyLiens = [] } = useAttorneyLiensQuery(caseId);
    const { data: miscLiens = [] } = useMiscLiensQuery(caseId);
    const [selectedLienId, setSelectedLienId] = useState<string>("");
    const [deleteAttorneyDialogOpen, setDeleteAttorneyDialogOpen] = useState(false);
    const [deleteMiscDialogOpen, setDeleteMiscDialogOpen] = useState(false);
    const { toast } = useToast();
    const { data: linkedCasesData } = useLinkedCasesQuery(caseId);

    const allLinkedCases = [
        ...(linkedCasesData?.direct_cases || []),
        ...(linkedCasesData?.indirect_cases || [])
    ];

    const updateAttorneyLienFinalStatus = useUpdateAttorneyLienFinalStatusMutation(caseId, selectedLienId);
    const updateMiscLienFinalStatus = useUpdateMiscLienFinalStatusMutation(caseId, selectedLienId);

    const deleteAttorneyLien = useDeleteAttorneyLienMutation(caseId, selectedLienId);
    const deleteMiscLien = useDeleteMiscLienMutation(caseId, selectedLienId);

    const handleAttorneyLienStatusChange = (lienId: string, checked: boolean, fee_amount: string) => {
        setSelectedLienId(lienId);
        console.log("attorney", checked)
        updateAttorneyLienFinalStatus.mutate({
            is_final: checked,
            final_lien_date: checked ? formatDateForApi(new Date()) : undefined,
            final_amount: checked ? fee_amount : undefined
        });
    };

    const handleMiscLienStatusChange = (lienId: string, checked: boolean, lien_amount: string) => {
        setSelectedLienId(lienId);
        console.log("misc", checked)
        updateMiscLienFinalStatus.mutate({
            is_final: checked,
            final_lien_date: checked ? formatDateForApi(new Date()) : undefined,
            final_amount: checked ? lien_amount : undefined
        });
    };

    const { attorneyLiensTotal, miscLiensTotal } = {
        attorneyLiensTotal: attorneyLiens.reduce((acc, lien) => acc + parseFloat(lien.fee_amount || "0"), 0),
        miscLiensTotal: miscLiens.reduce((acc, lien) => acc + parseFloat(lien.lien_amount || "0"), 0)
    };

    const handleDeleteAttorneyLien = async () => {
        try {
            await deleteAttorneyLien.mutateAsync();
            setDeleteAttorneyDialogOpen(false);
        } catch (error) {
            console.error('Failed to delete attorney lien:', error);
        }
    };

    const handleDeleteMiscLien = async () => {
        try {
            await deleteMiscLien.mutateAsync();
            setDeleteMiscDialogOpen(false);
        } catch (error) {
            console.error('Failed to delete misc lien:', error);
        }
    };

    const handleAttorneyLienDelete = (id: string) => {
        setSelectedLienId(id);
        setDeleteAttorneyDialogOpen(true);
    };

    const handleMiscLienDelete = (id: string) => {
        setSelectedLienId(id);
        setDeleteMiscDialogOpen(true);
    };

    if (!caseId) {
        notFound()
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">Liens Overview</h2>
                <div className="flex items-center gap-2">
                    <AddEditAtorneyLiens type="attorney" />
                    <AddEditMiscLiens />
                </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
                <KPICard
                    icon={Monitor}
                    title="Attorney Liens"
                    metrics={{
                        primary: {
                            type: 'count',
                            value: attorneyLiens.length
                        }
                    }}
                    bgColor="bg-[#F7F7F7]"
                />
                <KPICard
                    icon={CircleDollarSign}
                    title="Total Attorney Liens"
                    metrics={{
                        primary: {
                            type: 'monetary',
                            value: attorneyLiensTotal
                        }
                    }}
                    bgColor="bg-[#F7F7F7]"
                    isCurrency={true}
                />
                <KPICard
                    icon={FileText}
                    title="Misc Liens"
                    metrics={{
                        primary: {
                            type: 'count',
                            value: miscLiens.length
                        }
                    }}
                    bgColor="bg-[#F7F7F7]"
                />
                <KPICard
                    icon={CircleDollarSign}
                    title="Total Misc Liens"
                    metrics={{
                        primary: {
                            type: 'monetary',
                            value: miscLiensTotal
                        }
                    }}
                    bgColor="bg-[#F7F7F7]"
                    isCurrency={true}
                />
            </div>

            <Tabs defaultValue="attorney" className="w-full">
                <TabsList className="w-full">
                    <TabsTrigger value="attorney">Attorney Liens</TabsTrigger>
                    <TabsTrigger value="misc">Misc Liens</TabsTrigger>
                </TabsList>
                <TabsContent value="attorney">
                    <Card className="border-none">
                        <CardContent>
                            <div className="divide-y divide-gray-100">
                                {attorneyLiens.map((lien) => (
                                    <div key={lien.id} className="py-4 first:pt-0 last:pb-0">
                                        <div className="grid grid-cols-[2fr_2fr_2fr_2fr_100px] items-center gap-4">
                                            <div className="space-y-1">
                                                <div className="text-xs text-gray-500">Name</div>
                                                <div className="text-sm font-semibold">{lien.law_firm.office_name}</div>
                                            </div>
                                            <div className="space-y-1">
                                                <div className="text-xs text-gray-500">Attorney</div>
                                                <div className="text-sm font-semibold">
                                                    {lien.attorney ? `${lien.attorney.first_name || ''} ${lien.attorney.last_name || ''}`.trim() || '—' : '—'}
                                                </div>
                                            </div>
                                            <div className="space-y-1">
                                                <div className="text-xs text-gray-500">Fee Amount</div>
                                                <div className="text-sm font-semibold">
                                                    <CurrencyDisplay amount={lien.fee_amount} />
                                                </div>
                                            </div>
                                            <div className="space-y-1">
                                                <Checkbox
                                                    id={`final-lien-${lien.id}`}
                                                    checked={lien.final_lien}
                                                    onCheckedChange={(checked) => {
                                                        if (lien.id) {
                                                            handleAttorneyLienStatusChange(lien.id.toString(), checked as boolean, lien.fee_amount);
                                                        }
                                                    }}
                                                />
                                                <label
                                                    htmlFor={`final-lien-${lien.id}`}
                                                    className="text-sm font-medium text-gray-700 capitalize ml-2 cursor-pointer select-none"
                                                >
                                                    Final Lien
                                                </label>
                                                {lien.final_lien && (
                                                    <>
                                                        {lien.final_lien_date && (
                                                            <div className="text-sm font-semibold">
                                                                {formatDateForDisplay(lien.final_lien_date)}
                                                            </div>
                                                        )}
                                                        {lien.final_amount && (
                                                            <div className="text-sm font-semibold">
                                                                <CurrencyDisplay amount={lien.final_amount} />
                                                            </div>
                                                        )}
                                                    </>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                {/* {allLinkedCases.length > 0 && (
                                                    <SyncCase
                                                        caseId={caseId}
                                                        syncType="attorney-liens"
                                                        sourceId={lien.id}
                                                        onSyncComplete={() => {
                                                            toast({
                                                                title: "Success",
                                                                description: "Attorney lien synchronized successfully",
                                                            });
                                                        }}
                                                        onSyncError={(error) => {
                                                            toast({
                                                                title: "Error",
                                                                description: error.message || "Failed to sync attorney lien",
                                                                variant: "destructive",
                                                            });
                                                        }}
                                                    />
                                                )} */}
                                                <AddEditAtorneyLiens
                                                    isEdit={true}
                                                    type="attorney"
                                                    selectedLien={lien}
                                                />
                                                <button
                                                    onClick={() => lien.id && handleAttorneyLienDelete(lien.id.toString())}
                                                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                                                >
                                                    <Trash2 className="w-4 h-4 text-red-500" />
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
                <TabsContent value="misc">
                    <Card className="border-none">
                        <CardContent>
                            <div className="divide-y divide-gray-100">
                                {miscLiens.map((lien) => (
                                    <div key={lien.id} className="py-4 first:pt-0 last:pb-0">
                                        <div className="flex items-center justify-between">
                                            <div className="grid grid-cols-4 gap-8 flex-1">
                                                {/* Company Column */}
                                                <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500 opacity-70">Company</span>
                                                    <span className="text-sm font-semibold">
                                                        {lien.lien_holder.company}
                                                    </span>
                                                </div>

                                                {/* Name Column */}
                                                <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500 opacity-70">Name</span>
                                                    <span className="text-sm font-semibold">
                                                        {lien.lien_name}
                                                    </span>
                                                </div>

                                                {/* Fee Amount Column */}
                                                <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500 opacity-70">Fee Amount</span>
                                                    <span className="text-sm font-semibold">
                                                        <CurrencyDisplay amount={lien.lien_amount} />
                                                    </span>
                                                </div>

                                                {/* Final Lien Status */}
                                                <div className="space-y-1">
                                                    <Checkbox
                                                        id={`final-lien-${lien.id}`}
                                                        checked={lien.final_lien}
                                                        onCheckedChange={(checked) => {
                                                            if (lien.id) {
                                                                handleMiscLienStatusChange(lien.id.toString(), checked as boolean, lien.lien_amount);
                                                            }
                                                        }}
                                                    />
                                                    <label
                                                        htmlFor={`final-lien-${lien.id}`}
                                                        className="text-sm font-medium text-gray-700 capitalize ml-2 cursor-pointer select-none"
                                                    >
                                                        Final Lien
                                                    </label>
                                                    {lien.final_lien && (
                                                        <>
                                                            {lien.final_lien_date && (
                                                                <div className="text-sm font-semibold">
                                                                    {formatDateForDisplay(lien.final_lien_date)}
                                                                </div>
                                                            )}
                                                            {lien.final_amount && (
                                                                <div className="text-sm font-semibold">
                                                                    <CurrencyDisplay amount={lien.final_amount} />
                                                                </div>
                                                            )}
                                                        </>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Actions */}
                                            <div className="flex items-center gap-2">
                                                {/* {allLinkedCases.length > 0 && (
                                                    <SyncCase
                                                        caseId={caseId}
                                                        syncType="misc-liens"
                                                        sourceId={lien.id}
                                                        onSyncComplete={() => {
                                                            toast({
                                                                title: "Success",
                                                                description: "Miscellaneous lien synchronized successfully",
                                                            });
                                                        }}
                                                        onSyncError={(error) => {
                                                            toast({
                                                                title: "Error",
                                                                description: error.message || "Failed to sync miscellaneous lien",
                                                                variant: "destructive",
                                                            });
                                                        }}
                                                    />
                                                )} */}
                                                <AddEditMiscLiens
                                                    isEdit={true}
                                                    selectedLien={lien}
                                                />
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => lien.id && handleMiscLienDelete(lien.id.toString())}
                                                >
                                                    <Trash2 className="h-4 w-4 text-red-500" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            <DeleteConfirmationDialog
                open={deleteAttorneyDialogOpen}
                onOpenChange={setDeleteAttorneyDialogOpen}
                onConfirm={handleDeleteAttorneyLien}
                text="Attorney Lien"
            />

            <DeleteConfirmationDialog
                open={deleteMiscDialogOpen}
                onOpenChange={setDeleteMiscDialogOpen}
                onConfirm={handleDeleteMiscLien}
                text="Miscellaneous Lien"
            />
        </div>
    );
}
