'use client';

import React, { useState } from 'react';
import {
    Edit,
    LucideIcon,
    UserCog,
    Scale,
    UserCheck,
    HandshakeIcon,
    GavelIcon,
    UserPlus,
    Search,
    Calculator,
    Briefcase,
    FileText,
    Users,
    Camera,
    Repeat,
} from 'lucide-react';
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button";
import caseManagementService from '@/services/caseManagementService';
import { CaseWorker, UserDetails, WorkerRole } from '@/type/caseManagement';
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import { useWorkerSyncMutation } from '@/services/case-management/caseSyncService';
import { useToast } from "@/hooks/use-toast";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import SyncCaseDialog from "../../CaseOverview/components/SyncCaseDialog";

interface WorkerProps {
    caseId: string;
}

const workerRoles: (WorkerRole & { icon: LucideIcon })[] = [
    { key: 'primary_contact', label: 'Primary', icon: UserCheck },
    { key: 'case_manager', label: 'Case Manager', icon: UserCog },
    { key: 'lead_attorney', label: 'Lead Attorney', icon: Scale },
    { key: 'case_assistant', label: 'Case Assistant', icon: FileText },
    { key: 'lien_negotiator', label: 'Lien Negotiator', icon: HandshakeIcon },
    { key: 'supervising_attorney', label: 'Supervising Attorney', icon: GavelIcon },
    { key: 'intake_specialist', label: 'Intake', icon: UserPlus },
    { key: 'investigator', label: 'Investigator', icon: Search },
    { key: 'accountant', label: 'Accountant', icon: Calculator },
    { key: 'litigation_attorney', label: 'Litigation Attorney', icon: Scale },
    { key: 'litigation_assistant', label: 'Litigation Assistant', icon: Briefcase },
    { key: 'accident_recreation_worker', label: 'Accident Recreation', icon: Camera },
];

export function Worker({ caseId }: WorkerProps) {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [showSyncDialog, setShowSyncDialog] = useState(false);
    const [pendingChanges, setPendingChanges] = useState<Record<string, number | null>>({});
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
    const { toast } = useToast();

    const {
        data: caseWorkers,
        isLoading: isLoadingWorkers,
        refetch: refetchWorkers
    } = caseManagementService.useCaseWorkers(caseId);

    const {
        data: availableUsers,
        isLoading: isLoadingUsers
    } = caseManagementService.useAvailableUsers(caseId);

    const { data: linkedCasesData } = useLinkedCasesQuery(caseId);
    const workerSync = useWorkerSyncMutation(caseId);

    const allLinkedCases = [
        ...(linkedCasesData?.direct_cases || []),
        ...(linkedCasesData?.indirect_cases || [])
    ];

    const updateWorkersMutation = caseManagementService.useUpdateCaseWorkers();

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            if (!selectedCaseIds.length) {
                toast({
                    title: "Error",
                    description: "No cases selected for syncing",
                    variant: "destructive",
                });
                return;
            }

            if (!caseWorkers?.id) {
                toast({
                    title: "Error",
                    description: "Worker information not available for syncing",
                    variant: "destructive",
                });
                return;
            }

            // First update the workers with current form values
            if (Object.keys(pendingChanges).length > 0) {
                // Convert null values to undefined as per API requirements
                const cleanedData = Object.entries(pendingChanges).reduce((acc, [key, value]) => {
                    if (value !== null) {
                        acc[key] = value;
                    }
                    return acc;
                }, {} as Record<string, number>);

                await updateWorkersMutation.mutateAsync({
                    caseId,
                    data: cleanedData
                });
            }

            // Then sync to selected cases
            await workerSync.mutateAsync({
                source_worker_id: caseWorkers.id,
                target_case_ids: selectedCaseIds
            });

            setShowSyncDialog(false);
            setOpen(false);
            setPendingChanges({});
            refetchWorkers();

            toast({
                title: "Success",
                description: "Worker assignments synced successfully",
            });
        } catch (error) {
            console.error("Error syncing worker:", error);
            toast({
                title: "Error",
                description: "Failed to sync worker assignments",
                variant: "destructive",
            });
        }
    };

    if (isLoadingWorkers || isLoadingUsers) {
        return <Skeleton className="w-full h-[400px]" />;
    }

    const handleWorkerUpdate = (roleKey: string, userId: string | null) => {
        // Clear previous validation error for this role
        setValidationErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[roleKey];
            return newErrors;
        });

        setPendingChanges(prev => ({
            ...prev,
            [roleKey]: userId ? parseInt(userId) : null
        }));
    };

    const handleSaveChanges = () => {
        if (Object.keys(pendingChanges).length > 0) {
            // Convert null values to undefined as per API requirements
            const cleanedData = Object.entries(pendingChanges).reduce((acc, [key, value]) => {
                if (value !== null) {
                    acc[key] = value;
                }
                return acc;
            }, {} as Record<string, number>);

            updateWorkersMutation.mutate({
                caseId,
                data: cleanedData
            });
        }
        setPendingChanges({});
        setValidationErrors({});
        setOpen(false);
    };

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = Object.keys(pendingChanges).length > 0;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(true);
            }
        }
    };

    const handleClose = () => {
        setPendingChanges({});
        setValidationErrors({});
        setOpen(false);
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCancelClick = () => {
        const isDirty = Object.keys(pendingChanges).length > 0;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const workerFields = workerRoles.map((role) => {
        const workerDetails = role.key === 'litigation_firm'
            ? null
            : caseWorkers?.[`${role.key}_details` as keyof CaseWorker];
        const displayName = role.key === 'litigation_firm'
            ? caseWorkers?.litigation_firm
            : workerDetails
                ? (workerDetails as UserDetails).name
                : '—';
        
        return {
            icon: role.icon as LucideIcon,
            label: role.label,
            value: displayName,
            className: "flex items-start gap-2"
        };
    });

    return (
        <div className="w-full mt-4">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-[#060216] text-xl font-semibold leading-6 flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    Case Workers
                </h2>
                <div className="flex items-center gap-2">
                    {/* <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span className={`${(!allLinkedCases.length || workerSync.isPending) ? 'cursor-not-allowed' : ''}`}>
                                    <Button
                                        variant="outline"
                                        className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
                                        onClick={() => setShowSyncDialog(true)}
                                        disabled={workerSync.isPending || !allLinkedCases.length}
                                    >
                                        {workerSync.isPending ? (
                                            <>
                                                <span className="loading loading-spinner loading-xs mr-2"></span>
                                            </>
                                        ) : (
                                            <>
                                                <Repeat className="h-4 w-4 mr-2" />
                                            </>
                                        )}
                                    </Button>
                                </span>
                            </TooltipTrigger>
                            <TooltipContent>
                                {!allLinkedCases.length
                                    ? "No linked cases available to sync with. Please link cases first."
                                    : "Sync Case Workers"}
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider> */}
                    <Dialog open={open} onOpenChange={handleOpenChange}>
                        <DialogTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="hover:bg-green-50 hover:text-green-600"
                            >
                                <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Edit Assignments</DialogTitle>
                            </DialogHeader>
                            <div className="grid grid-cols-2 gap-4 py-4">
                                {workerRoles.map((role) => (
                                    <div key={role.key} className="flex flex-col gap-2">
                                        <label className="text-sm font-medium">{role.label}</label>
                                        <Select
                                            value={
                                                (pendingChanges[role.key]?.toString() ??
                                                    caseWorkers?.[role.key]?.toString()) ||
                                                undefined
                                            }
                                            onValueChange={(value) => handleWorkerUpdate(role.key, value === "none" ? null : value)}
                                        >
                                            <SelectTrigger className={cn(
                                                "w-full relative",
                                                validationErrors[role.key] ? "border-red-500" : ""
                                            )}>
                                                <SelectValue placeholder={`Select ${role.label}`} />
                                            </SelectTrigger>
                                            <SelectContent searchable>
                                                {availableUsers?.map((user) => (
                                                    <SelectItem
                                                        key={user.id}
                                                        value={user.id.toString()}
                                                    >
                                                        {user.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {validationErrors[role.key] && (
                                            <span className="text-sm text-red-500">
                                                {validationErrors[role.key]}
                                            </span>
                                        )}
                                    </div>
                                ))}
                            </div>
                            <div className="flex justify-end gap-3 pt-4">
                                <Button
                                    variant="secondary"
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => setShowSyncDialog(true)}
                                    disabled={workerSync.isPending}
                                >
                                    Sync Linked Cases
                                </Button>
                                <Button
                                    variant="default"
                                    className="bg-green-500 hover:bg-green-600"
                                    onClick={handleSaveChanges}
                                    disabled={Object.keys(validationErrors).length > 0}
                                >
                                    Save Changes
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <InfoFieldGroup
                fields={workerFields}
                columns={3}
                className="gap-x-8 gap-y-6"
            />

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />

            <SyncCaseDialog
                caseId={caseId}
                syncType="workers"
                isOpen={showSyncDialog}
                onClose={() => setShowSyncDialog(false)}
                onSync={handleSync}
                isSyncing={workerSync.isPending}
            />
        </div>
    );
} 