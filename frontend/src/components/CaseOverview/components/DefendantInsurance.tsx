import React, { FC, Fragment } from 'react';
import {
    useDefendantInsuranceLegalRepresentationQuery,
    useDefendantsListQuery
} from '@/services/case-management/defendantService';
import { Card, CardContent } from '@/components/ui/card';
import type {
    DefendantInsuranceAdjusterResponse,
    DefendantInsurance
} from '@/type/case-management/defendantTypes';
import { Separator } from '@/components/ui/separator';
import RichTextViewer from '@/components/ui/RichTextViewer';
import { InfoFieldGroup } from '@/components/ui/InfoField';
import { PhoneLink } from '@/components/ui/phone-link';
import { MailLink } from '@/components/ui/mail-link';
import { AddressLink } from '@/components/gMap/address-link';
import { AttorneyResponse } from '@/type/case-management/orgTypes';

interface DefendantInsuranceProps {
    caseId: string;
}

type InsuranceCompanyType = {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
}

type InsuranceWithDetails = {
    id: number;
    defendantName: string;
    defendantCompany: string;
    insurance_company: InsuranceCompanyType;
    claim_number: string | null;
    policy_number: string | null;
    policy_type: string | null;
    liability_status: string | null;
    coverage_status: string | null;
    claim_note: string | null;
    um_uim?: string | null;
    medpay?: string | null;
    adjusters?: DefendantInsuranceAdjusterResponse[];
    no_insurance?: boolean;
}

interface LawFirmData {
    id: number;
    office_name: string;
    phone?: string | null;
    cell?: string | null;
    fax?: string | null;
    email?: string | null;
    street1?: string | null;
    street2?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
}

// Simple component to display legal representation for a defendant
const DefendantLegalRepresentation: FC<{ caseId: string; defendantId: string }> = ({ caseId, defendantId }) => {
    const { data: legalRep } = useDefendantInsuranceLegalRepresentationQuery(
        caseId,
        defendantId,
        "",
        { enabled: !!defendantId }
    );
    if (!legalRep?.law_firm) return null;

    // Convert single law firm to array if needed
    const lawFirms = Array.isArray(legalRep.law_firm)
        ? legalRep.law_firm as LawFirmData[]
        : [legalRep.law_firm] as LawFirmData[];

    const attorneys = legalRep.attorney
        ? (Array.isArray(legalRep.attorney)
            ? legalRep.attorney as AttorneyResponse[]
            : [legalRep.attorney] as AttorneyResponse[])
        : [];

    if (!lawFirms.length && !attorneys.length) return null;

    return (
        <div className="mt-8 space-y-6">
            <h4 className="text-sm font-semibold text-[#060216]">Legal Representation</h4>
            {lawFirms.map((lawFirm, firmIndex) => (
                <Fragment key={lawFirm.id}>
                    {firmIndex > 0 && <Separator className="my-6 bg-gray-200" />}
                    <div className="space-y-6">
                        <InfoFieldGroup
                            fields={[
                                {
                                    label: "Law Firm",
                                    value: lawFirm.office_name || '—'
                                },
                                {
                                    label: "Phone",
                                    value: <PhoneLink phone={lawFirm.phone} />
                                },
                                {
                                    label: "Cell",
                                    value: <PhoneLink phone={lawFirm.cell} />
                                },
                                {
                                    label: "Fax",
                                    value: <PhoneLink phone={lawFirm.fax} />
                                },
                                {
                                    label: "Email",
                                    value: <MailLink email={lawFirm.email} caseId={caseId} />
                                },
                                {
                                    label: "Address",
                                    value: <AddressLink address={{
                                        street1: lawFirm.street1 || undefined,
                                        street2: lawFirm.street2 || undefined,
                                        city: lawFirm.city || undefined,
                                        state: lawFirm.state || undefined,
                                        zip_code: lawFirm.zip_code || undefined
                                    }} />
                                }
                            ]}
                        />

                        {attorneys && attorneys.length > 0 && (
                            <div className="mt-4">
                                <div className="space-y-6">
                                    {attorneys.map((attorney) => (
                                        <InfoFieldGroup
                                            key={attorney.id}
                                            fields={[
                                                {
                                                    label: "Attorney Name",
                                                    value: `${attorney.first_name} ${attorney.last_name}`.trim() || '—'
                                                },
                                                {
                                                    label: "Phone",
                                                    value: attorney.phone,
                                                    isPhone: true
                                                },
                                                {
                                                    label: "Cell",
                                                    value: attorney.cell,
                                                    isPhone: true
                                                },
                                                {
                                                    label: "Fax",
                                                    value: attorney.fax,
                                                    isPhone: true
                                                },
                                                {
                                                    label: "Email",
                                                    value: attorney.email,
                                                    isMail: true,
                                                    caseId
                                                },
                                                {
                                                    label: "Address",
                                                    value: <AddressLink address={{
                                                        street1: attorney.street1 || undefined,
                                                        street2: attorney.street2 || undefined,
                                                    }} />
                                                }
                                            ]}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </Fragment>
            ))}
        </div>
    );
};

const DefendantInsurance: FC<DefendantInsuranceProps> = ({ caseId }) => {
    const { data: defendants, isLoading: isDefendantsLoading } = useDefendantsListQuery(caseId);

    const isLoading = isDefendantsLoading;

    const getDefendantName = (defendantId: number) => {
        const defendant = defendants?.find(d => d.id === defendantId);
        if (!defendant) return 'Unknown Defendant';

        return `${defendant.first_name || ''} ${defendant.last_name || ''}`.trim() || 'Unknown Defendant';
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (!defendants || defendants.length === 0) {
        return (
            <Card className="w-full">
                <CardContent className="p-4">
                    <div className="text-gray-500 text-center">No defense insurance found</div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="w-full">
            <CardContent className="p-4">
                <div className="space-y-6">
                    {defendants.map((defendant, index) => {
                        const hasInsurance = defendant.insurances && defendant.insurances.length > 0;

                        return (
                            <Fragment key={defendant.id}>
                                {index > 0 && <Separator className="my-8 h-[2px] bg-gray-200" />}

                                {/* Defendant Header */}
                                <div className="mb-4">
                                    <h3 className="text-lg font-semibold text-[#060216]">
                                        {getDefendantName(defendant.id)}
                                        {defendant.company_entity && ` - ${defendant.company_entity}`}
                                    </h3>
                                </div>

                                {!hasInsurance ? (
                                    <div className="text-gray-500 text-center py-4">No defense insurance found</div>
                                ) : (
                                    <div className="space-y-4">
                                        {(defendant.insurances as InsuranceWithDetails[]).map((insurance, insuranceIndex) => {
                                            if (insurance.no_insurance) {
                                                return (
                                                    <div key={insurance.id} className="text-gray-500 text-center">
                                                        No defense insurance found
                                                    </div>
                                                );
                                            }

                                            const adjusterData = insurance.adjusters?.[0];
                                            const insuranceCompany = insurance.insurance_company as InsuranceCompanyType;
                                            const firstAdjuster = adjusterData?.bodily_injury ||
                                                adjusterData?.medpay_pip ||
                                                adjusterData?.medpay_pip_supervisor ||
                                                adjusterData?.bi_supervisor ||
                                                adjusterData?.property_damage ||
                                                adjusterData?.pd_supervisor;

                                            return (
                                                <div key={insurance.id} className="space-y-6">
                                                    {insuranceIndex > 0 && <Separator className="my-6 bg-gray-200" />}

                                                    <InfoFieldGroup
                                                        fields={[
                                                            {
                                                                label: "Insurance Company",
                                                                value: insuranceCompany?.name || '—'
                                                            },
                                                            {
                                                                label: "Company Phone",
                                                                value: insuranceCompany?.phone || '—',
                                                                isPhone: true
                                                            },
                                                            {
                                                                label: "Company Email",
                                                                value: insuranceCompany?.email || '—',
                                                                isMail: true,
                                                                caseId: caseId
                                                            },
                                                            {
                                                                label: "UM/UIM",
                                                                value: insurance.um_uim || '—'
                                                            },
                                                            {
                                                                label: "Claim ID",
                                                                value: insurance.claim_number ? `#${insurance.claim_number}` : '—'
                                                            },
                                                            {
                                                                label: "Medpay",
                                                                value: insurance.medpay || '—'
                                                            },
                                                            {
                                                                label: "Policy ID",
                                                                value: insurance.policy_number || '—'
                                                            },
                                                            {
                                                                label: "3P Policy Limit",
                                                                value: insurance.um_uim || '—'
                                                            }
                                                        ]}
                                                    />

                                                    {insurance.claim_note && (
                                                        <div className="p-2 bg-gray-50 rounded-lg border border-gray-100">
                                                            <p className="text-xs text-gray-500 mb-1">Claim Note</p>
                                                            <div className="text-sm font-semibold">
                                                                <RichTextViewer data={insurance.claim_note} isCopied={true} />
                                                            </div>
                                                        </div>
                                                    )}

                                                    {/* Adjuster Information */}
                                                    {adjusterData && (
                                                        <>
                                                            <div className="mt-4 mb-2">
                                                                <h4 className="text-sm font-semibold text-[#060216]">Adjuster Information</h4>
                                                            </div>
                                                            <InfoFieldGroup
                                                                columns={3}
                                                                caseId={caseId}
                                                                fields={[
                                                                    {
                                                                        label: "Adjuster",
                                                                        value: [firstAdjuster?.first_name, firstAdjuster?.last_name].filter(Boolean).join(' ') || '—'
                                                                    },
                                                                    {
                                                                        label: "Phone",
                                                                        value: firstAdjuster?.phone || '—',
                                                                        isPhone: true
                                                                    },
                                                                    {
                                                                        label: "Email",
                                                                        value: firstAdjuster?.email || '—',
                                                                        isMail: true,
                                                                        caseId: caseId
                                                                    }
                                                                ]}
                                                            />
                                                        </>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}

                                {/* Legal Representation Section */}
                                <DefendantLegalRepresentation
                                    caseId={caseId}
                                    defendantId={defendant.id.toString()}
                                />
                            </Fragment>
                        );
                    })}
                </div>
            </CardContent>
        </Card>
    );
};

export default DefendantInsurance;
