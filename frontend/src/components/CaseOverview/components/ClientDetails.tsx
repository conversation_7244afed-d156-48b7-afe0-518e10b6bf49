"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FileText, Shield, Repeat, Loader2, User, User<PERSON><PERSON><PERSON>, UserX, <PERSON>Check2, FileX2 } from "lucide-react";
import IndexInformation from "@/app/client-details/components/IndexInformation";
import ContactDetails from "@/app/client-details/components/ContactDetails";
import InsuranceDetails from "@/app/client-details/components/InsuranceDetails";
import AddEditProperDamage from "@/app/client-details/components/AddEditProperDamage";
import PropertyDamage from "@/app/client-details/components/PropertyDamage";
import {
    useClientBasicDetailsQuery,
    useClientContactDetailsQuery,
    useClientPropertyDamageQuery,
    useClientInsuranceListQuery,
    useUpdateClientEmploymentStatusMutation,
    // useSyncClientMutation,
    useUpdateMinorFeeSignedMutation,
    // useUpdateMinorAgreementStatusMutation,
} from "@/services/case-management/clientDetailService";
import { useLinkedCasesQuery } from "@/services/case-management/linkCaseService";
import { useEffect, useState, useRef } from "react";
import AddEditInsuranceAdjusterDialog from "@/app/client-details/components/AddEditInsuranceAdjusterDialog";
import EditInsuranceDetailsDialog from "@/app/client-details/components/InsuranceDetailsEditDialog";
import { toast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectValue, SelectTrigger, SelectItem } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { CaseBasicDetails } from "@/type/linkCaseType";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { CaseSection, useUploadBySectionMutation } from "@/services/case-management/newDocumentManagement";
import { useIncidentDetailsQuery, useUpdateIncidentDetailsMutation } from "@/services/incidentService";
import  SyncCaseDialog from "./SyncCaseDialog";
import { useClientSyncMutation } from "@/services/case-management/caseSyncService";

function ClientDetailsView({ caseId }: { caseId: string }) {
    const {
        data: basicDetails,
        refetch: refetchBasicDetails,
    } = useClientBasicDetailsQuery(caseId);
    const { refetch: refetchContactDetails } = useClientContactDetailsQuery(
        caseId
    );
    const { data: propertyDamageData } = useClientPropertyDamageQuery(
        caseId
    );
    const { refetch: refetchInsurance } = useClientInsuranceListQuery(
        caseId
    );
    const { data: linkedCases } = useLinkedCasesQuery(caseId);
    const { data: incidentDetails } = useIncidentDetailsQuery(caseId);
    const updateIncidentDetails = useUpdateIncidentDetailsMutation(caseId);
    const syncClient = useClientSyncMutation(caseId);
    const uploadBySection = useUploadBySectionMutation();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const updateMinorFeeSigned = useUpdateMinorFeeSignedMutation(caseId);

    useEffect(() => {
        refetchBasicDetails();
        refetchContactDetails();
        refetchInsurance();
    }, [caseId]);

    const [showAdjusterDialog, setShowAdjusterDialog] = useState(false);
    const [newInsuranceData, setNewInsuranceData] = useState<{
        insuranceId: string;
        companyId: string;
    } | null>(null);
    const [minorAgreementSigned, setMinorAgreementSigned] = useState<boolean>(basicDetails?.minor_fee_signed || false);
    const [showSyncDialog, setShowSyncDialog] = useState(false);

    useEffect(() => {
        if (basicDetails?.minor_fee_signed !== undefined) {
            setMinorAgreementSigned(basicDetails.minor_fee_signed);
        }
    }, [basicDetails?.minor_fee_signed]);

    const handleAdjusterDialogClose = () => {
        setShowAdjusterDialog(false);
        setNewInsuranceData(null);
        refetchInsurance();
    };
    const updateEmploymentStatus = useUpdateClientEmploymentStatusMutation(caseId);
    // const updateClientBasicDetails = useUpdateClientBasicDetailsMutation(caseId);
    // const updateMinorAgreementStatus = useUpdateMinorAgreementStatusMutation(caseId);

    // All linked cases (both direct and indirect)
    const allLinkedCases: CaseBasicDetails[] = [
        ...(linkedCases?.direct_cases || []),
        ...(linkedCases?.indirect_cases || [])
    ];

    // Helper function to calculate exact age
    const calculateAge = (birthDate: Date, dateOfIncident: Date) => {
        let age = dateOfIncident.getFullYear() - birthDate.getFullYear();
        const monthDiff = dateOfIncident.getMonth() - birthDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && dateOfIncident.getDate() < birthDate.getDate())) {
            age--;
        }

        return age;
    };

    // Calculate if client was minor at time of loss with more accurate calculation
    const wasMinorAtLoss = basicDetails?.date_of_birth && incidentDetails?.incident_date ?
        calculateAge(new Date(basicDetails.date_of_birth), new Date(incidentDetails.incident_date)) < 18
        : false;

    // Sync client data to linked cases
    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            if (!selectedCaseIds.length) {
                toast({
                    title: "Error",
                    description: "No cases selected for syncing",
                    variant: "destructive",
                });
                return;
            }

            if (!basicDetails?.id) {
                toast({
                    title: "Error",
                    description: "Client information not available for syncing",
                    variant: "destructive",
                });
                return;
            }

            await syncClient.mutateAsync({
                source_client_id: basicDetails.id,
                target_case_ids: selectedCaseIds,
                sync_options: {
                    sync_basic_details: true,
                    sync_contact_details: true,
                    sync_property_damage: true,
                    sync_insurances: true,    
                    sync_adjusters: true,
                    sync_legal_representation: true
                }
            });

            setShowSyncDialog(false);
            toast({
                title: "Success",
                description: "Case data synced successfully",
            });
        } catch (error) {
            console.error("Error syncing client:", error);
            toast({
                title: "Error",
                description: "Failed to sync case data",
                variant: "destructive",
            });
        }
    };

    const handleEmploymentChange = async (value: string) => {
        try {
            await Promise.all([
                updateEmploymentStatus.mutateAsync(value === "employed"),
                incidentDetails && updateIncidentDetails.mutateAsync({})
            ]);
            await refetchBasicDetails();
            toast({
                title: "Success",
                description: `Employment status updated to ${value === "employed" ? "Employed" : "Unemployed"}`,
            });
        } catch {
            toast({
                title: "Error",
                description: "Failed to update employment status",
                variant: "destructive",
            });
        }
    };

    const handleMinorAgreementChange = async (value: string) => {
        try {
            await Promise.all([
                updateMinorFeeSigned.mutateAsync(value === "signed"),
                incidentDetails && updateIncidentDetails.mutateAsync({})
            ]);
            setMinorAgreementSigned(value === "signed");
            toast({
                title: "Success",
                description: `Minor agreement status updated to ${value === "signed" ? "Signed" : "Not Signed"}`,
            });
        } catch (error) {
            console.error("Error updating minor agreement status:", error);
            toast({
                title: "Error",
                description: "Failed to update minor agreement status",
                variant: "destructive",
            });
        }
    };

    const shouldShowCompactView = true;//!(isDocumentSidebarVisible || !isTabsPanelCollapsed);

    const renderDropdownItems = () => (
        <>
            {/* <DropdownMenuItem
                disabled={syncClient.isPending || !allLinkedCases.length}
                onClick={handleSync}
                className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
            >
                <Repeat className="h-4 w-4 mr-2" />
                {syncClient.isPending ? 'Syncing...' : 'Sync'}
            </DropdownMenuItem> */}

            {/* <DropdownMenuItem
                className="flex items-center text-emerald-600 hover:bg-emerald-50"
                onClick={() => {
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                        fileInputRef.current.click();
                    }
                }}
                disabled={uploadBySection.isPending}
            >
                
                {uploadBySection.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="mr-2 h-4 w-4 stroke-[1.5]" />}
            </DropdownMenuItem> */}

            {/* <DropdownMenuItem className="flex flex-col items-start p-2">
                <span className="text-sm mb-1">Employment Status</span>
                <Select
                    value={basicDetails?.employed ? "employed" : "unemployed"}
                    onValueChange={handleEmploymentChange}
                >
                    <SelectTrigger className="w-full">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="employed">
                            <div className="flex items-center gap-2">
                                <span className="h-2 w-2 rounded-full bg-green-500"></span>
                                <span className="font-medium">Employed</span>
                            </div>
                        </SelectItem>
                        <SelectItem value="unemployed">
                            <div className="flex items-center gap-2">
                                <span className="h-2 w-2 rounded-full bg-gray-400"></span>
                                <span className="font-medium">Unemployed</span>
                            </div>
                        </SelectItem>
                    </SelectContent>
                </Select>
            </DropdownMenuItem> */}

            {wasMinorAtLoss && (
                // <DropdownMenuItem className="flex flex-col items-start border-none">
                <>
                    <Select
                        value={minorAgreementSigned ? "signed" : "not_signed"}
                        onValueChange={handleMinorAgreementChange}
                    >
                        <SelectTrigger className="w-full border-none">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="signed">
                                <div className="flex items-center gap-2 py-1">
                                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                                    <span className="font-medium text-green-700">Minority Agreement Signed</span>
                                </div>
                            </SelectItem>
                            <SelectItem value="not_signed">
                                <div className="flex items-center gap-2 py-1">
                                    <span className="h-2 w-2 rounded-full bg-red-500"></span>
                                    <span className="font-medium text-red-700">Minority Agreement Not Signed</span>
                                </div>
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </>
            )}
        </>
    );
    const employmentDropdownItems = () => (
        <>
            {/* <DropdownMenuItem
                disabled={syncClient.isPending || !allLinkedCases.length}
                onClick={handleSync}
                className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
            >
                <Repeat className="h-4 w-4 mr-2" />
                {syncClient.isPending ? 'Syncing...' : 'Sync'}
            </DropdownMenuItem> */}

            {/* <DropdownMenuItem
                className="flex items-center text-emerald-600 hover:bg-emerald-50"
                onClick={() => {
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                        fileInputRef.current.click();
                    }
                }}
                disabled={uploadBySection.isPending}
            >
                
                {uploadBySection.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="mr-2 h-4 w-4 stroke-[1.5]" />}
            </DropdownMenuItem> */}

            {/* <DropdownMenuItem className="flex flex-col items-start"> */}
            {/* <span className="text-sm mb-1">Employment Status</span> */}
            <Select
                value={basicDetails?.employed ? "employed" : "unemployed"}
                onValueChange={handleEmploymentChange}
            >
                <SelectTrigger className="w-full border-none">
                    <SelectValue />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="employed">
                        <div className="flex items-center gap-2">
                            <span className="h-2 w-2 rounded-full bg-green-500"></span>
                            <span className="font-medium">Employed</span>
                        </div>
                    </SelectItem>
                    <SelectItem value="unemployed">
                        <div className="flex items-center gap-2">
                            <span className="h-2 w-2 rounded-full bg-gray-400"></span>
                            <span className="font-medium">Unemployed</span>
                        </div>
                    </SelectItem>
                </SelectContent>
            </Select>
            {/* </DropdownMenuItem> */}

            {/* {wasMinorAtLoss && (
                <DropdownMenuItem className="flex flex-col items-start p-2">
                    <span className="text-sm mb-1">Minor Agreement Status</span>
                    <Select
                        value={minorAgreementSigned ? "signed" : "not_signed"}
                        onValueChange={handleMinorAgreementChange}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="signed">
                                <div className="flex items-center gap-2 py-1">
                                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                                    <span className="font-medium text-green-700">Minority Agreement Signed</span>
                                </div>
                            </SelectItem>
                            <SelectItem value="not_signed">
                                <div className="flex items-center gap-2 py-1">
                                    <span className="h-2 w-2 rounded-full bg-red-500"></span>
                                    <span className="font-medium text-red-700">Minority Agreement Not Signed</span>
                                </div>
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </DropdownMenuItem>
            )} */}
        </>
    );

    return (
        <div className="relative">
            <div className="-mx-8 bg-white py-4">
                <div className="flex mr-10 ml-10 justify-between items-center">
                    <h2 className="text-lg font-semibold flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Client Information
                    </h2>
                    <div className="flex gap-3">
                        {shouldShowCompactView ? (
                            <>
                                {/* <Button
                                    variant="outline"
                                    className="gap-0 bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed p-0"
                                    onClick={() => setShowSyncDialog(true)}
                                    disabled={syncClient.isPending || !allLinkedCases.length}
                                >
                                    {syncClient.isPending ? (
                                        <>
                                            <span className="loading loading-spinner loading-xs mr-2"></span>
                                        </>
                                    ) : (
                                        <>
                                            <Repeat className="h-4 w-4 mr-2" />
                                        </>
                                    )}
                                </Button> */}
                                <EditInsuranceDetailsDialog
                                    isEdit={false}
                                    caseId={caseId}
                                    onInsuranceCreated={(data) => {
                                        setNewInsuranceData(data);
                                        setShowAdjusterDialog(true);
                                    }}
                                />
                                {!propertyDamageData && <AddEditProperDamage isEditing={false} caseId={caseId} />}

                                <Button
                                    variant="link"
                                    className="text-green-600"
                                    onClick={() => {
                                        if (fileInputRef.current) {
                                            fileInputRef.current.value = '';
                                            fileInputRef.current.click();
                                        }
                                    }}
                                    disabled={uploadBySection.isPending}
                                >
                                    {uploadBySection.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="mr-2 h-4 w-4 stroke-[1.5]" />}
                                </Button>

                                <Select
                                    value={basicDetails?.employed ? "employed" : "unemployed"}
                                    onValueChange={handleEmploymentChange}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="employed">
                                            <div className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-green-500"></span>
                                                <span className="font-medium">Employed</span>
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="unemployed">
                                            <div className="flex items-center gap-2">
                                                <span className="h-2 w-2 rounded-full bg-gray-400"></span>
                                                <span className="font-medium">Unemployed</span>
                                            </div>
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </>
                        ) : (
                            <div className="flex">
                                {/* <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <span className={`${(!allLinkedCases.length || syncClient.isPending) ? 'cursor-not-allowed' : ''}`}>
                                                <Button
                                                    variant="outline"
                                                    className=" mr-4 bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
                                                    onClick={() => setShowSyncDialog(true)}
                                                    disabled={syncClient.isPending || !allLinkedCases.length}
                                                >
                                                    {syncClient.isPending ? (
                                                        <>
                                                            <span className="loading loading-spinner loading-xs mr-2"></span>
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Repeat className="h-4 w-4 mr-2" />
                                                        </>
                                                    )}
                                                </Button>
                                            </span>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            {!allLinkedCases.length
                                                ? "No linked cases available to sync with. Please link cases first."
                                                : "Sync Client Insurance and Adjuster"}
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider> */}
                                <Button
                                    variant="link"
                                    className="text-green-600 p-0"
                                    onClick={() => {
                                        if (fileInputRef.current) {
                                            fileInputRef.current.value = '';
                                            fileInputRef.current.click();
                                        }
                                    }}
                                    disabled={uploadBySection.isPending}
                                >
                                    {uploadBySection.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="mr-2 h-4 w-4 stroke-[1.5]" />}
                                </Button>
                                <EditInsuranceDetailsDialog
                                    isEdit={false}
                                    caseId={caseId}
                                    onInsuranceCreated={(data) => {
                                        setNewInsuranceData(data);
                                        setShowAdjusterDialog(true);
                                    }}
                                />

                                {!propertyDamageData && <AddEditProperDamage isEditing={false} caseId={caseId} />}
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="link">
                                            {basicDetails?.employed ? <UserCheck className="h-4 w-4 text-green-600" /> : <UserX className="h-4 w-4 text-red-600" />}
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-[220px]">
                                        {employmentDropdownItems()}
                                    </DropdownMenuContent>
                                </DropdownMenu>

                                {wasMinorAtLoss && (
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="link">
                                                {minorAgreementSigned ? <FileCheck2 className="h-4 w-4 text-green-600" /> : <FileX2 className="h-4 w-4 text-red-600" />}
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end" className="w-[220px]">
                                            {renderDropdownItems()}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <input
                type="file"
                ref={fileInputRef}
                onChange={(event) => {
                    console.log("handleFileUpload called");
                    const file = event.target.files?.[0];
                    if (!file) {
                        console.log("No file selected");
                        return;
                    }

                    console.log("File selected:", {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        caseId: caseId
                    });

                    try {
                        console.log("Starting upload mutation...");
                        const response = uploadBySection.mutateAsync({
                            case_id: caseId,
                            section: CaseSection.DEMAND_QUESTIONNAIRE,
                            file: file
                        });

                        console.log("Upload successful:", response);

                        // Clear the file input
                        if (fileInputRef.current) {
                            fileInputRef.current.value = '';
                        }
                    } catch (error) {
                        console.error("Upload error details:", {
                            error,
                            errorMessage: error instanceof Error ? error.message : "Unknown error",
                            errorStack: error instanceof Error ? error.stack : undefined
                        });

                        toast({
                            title: "Upload Failed",
                            description: error instanceof Error ? error.message : "Failed to upload file",
                            variant: "destructive"
                        });
                    }
                }}
                className="hidden"
                accept=".pdf,.doc,.docx"
                disabled={uploadBySection.isPending}
            />

            <div className="flex flex-col gap-6 bg-[#f5f5f5]/0 max-w-[1600px] mx-auto">
                <div className="flex flex-col gap-6">
                    <div className="w-full">
                        <IndexInformation clientId={caseId} />
                    </div>
                    {wasMinorAtLoss && (
                        <Select
                            value={minorAgreementSigned ? "signed" : "not_signed"}
                            onValueChange={handleMinorAgreementChange}
                        >
                            <SelectTrigger className="w-full">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="signed">
                                    <div className="flex items-center gap-2 py-1">
                                        <span className="h-2 w-2 rounded-full bg-green-500"></span>
                                        <span className="font-medium text-green-700">Minority Agreement Signed</span>
                                    </div>
                                </SelectItem>
                                <SelectItem value="not_signed">
                                    <div className="flex items-center gap-2 py-1">
                                        <span className="h-2 w-2 rounded-full bg-red-500"></span>
                                        <span className="font-medium text-red-700">Minority Agreement Not Signed</span>
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    )}
                    <Separator className="w-full" />
                    <div className="w-full">
                        <ContactDetails clientId={caseId} />
                    </div>
                    <Separator className="w-full" />
                    <div className="w-full">
                        <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
                            <Shield className="w-5 h-5 text-[#060216]" />
                            <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                                Insurance Details
                            </h2>
                        </div>
                        <InsuranceDetails caseId={caseId} />
                    </div>

                    {propertyDamageData && (
                        <>
                            <Separator className="w-full" />
                            <div className="w-full">
                                <PropertyDamage
                                    caseId={caseId}
                                    insurance_company={propertyDamageData.insurance_company}
                                    registered_owner={propertyDamageData.registered_owner}
                                    estimate={propertyDamageData.estimate}
                                    vehicle_make={propertyDamageData.vehicle_make}
                                    color={propertyDamageData.color}
                                    license_plate={propertyDamageData.license_plate}
                                    vehicle_vin={propertyDamageData.vehicle_vin}
                                    damage={propertyDamageData.damage}
                                    auto_body_shop={propertyDamageData.auto_body_shop}
                                    final={propertyDamageData.final}
                                    vehicle_model={propertyDamageData.vehicle_model}
                                    vehicle_year={propertyDamageData.vehicle_year}
                                    mileage={propertyDamageData.mileage}
                                    note={propertyDamageData.note || ""}
                                    frame_damage={propertyDamageData.frame_damage}
                                    total_loss={propertyDamageData.total_loss}
                                    damage_description={propertyDamageData.damage_description || ""}
                                />
                            </div>
                        </>
                    )}
                    {showAdjusterDialog && newInsuranceData && (
                        <AddEditInsuranceAdjusterDialog
                            caseId={caseId}
                            insuranceId={newInsuranceData.insuranceId}
                            insuranceCompanyId={newInsuranceData.companyId}
                            isShow={true}
                            onClose={handleAdjusterDialogClose}
                        />
                    )}
                </div>
            </div>

            <SyncCaseDialog
                caseId={caseId}
                syncType="client"
                isOpen={showSyncDialog}
                onClose={() => setShowSyncDialog(false)}
                onSync={handleSync}
                isSyncing={syncClient.isPending}
            />
        </div>
    );
}

export default function ClientDetails({ caseId }: { caseId: string }) {
    return <ClientDetailsView caseId={caseId} />;
}
 