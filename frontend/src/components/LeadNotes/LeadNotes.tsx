'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Plus, Search, User2, Pencil, MoreVertical } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { useInView } from 'react-intersection-observer';
import { useLeadNotesQuery, useCreateLeadNoteMutation, useUpdateLeadNoteMutation, useDeleteLeadNoteMutation } from '@/services/leads/leadTaskService';
import { useCaseNoteTags } from '@/services/organizationService';
import { CaseNoteTag } from '@/services/organizationService';
import { MultiSelect } from "@/components/ui/multi-select";
import CustomCKEditor from "@/components/ckeditor/CKEditor";
import { useOrganizationUsersQuery } from '@/services/orgAPIs';
import type { UserDetail as PartialUserDetail } from '@/type/case-management/commonTypes';
import { CustomDateInput } from "@/components/ui/custom-date-input";
import RichTextEditor from "@/components/ui/RichTextEditor";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { LeadNote } from '@/type/lead/leadTaskTypes';

interface LeadNotesProps {
    leadId: string;
}

const ICON_CLASSES = "h-4 w-4"


export function LeadNotes({ leadId }: LeadNotesProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [title, setTitle] = useState('');
    const [content, setContent] = useState("");
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedDate, setSelectedDate] = useState<string>('');
    const [selectedCreator, setSelectedCreator] = useState<string>();
    const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
    const [selectedTags, setSelectedTags] = useState<number[]>([]);
    const [editingNote, setEditingNote] = useState<{
        id: number;
        title: string;
        content: string;
        tagged_user_ids: number[];
        tag_ids: number[];
    } | null>(null);
    const { ref: loadMoreRef } = useInView();

    const { data: users = [] } = useOrganizationUsersQuery();
    const { data: notesData, isLoading } = useLeadNotesQuery(leadId);
    const createNoteMutation = useCreateLeadNoteMutation(leadId);
    const updateNoteMutation = useUpdateLeadNoteMutation(leadId);
    const deleteNoteMutation = useDeleteLeadNoteMutation(leadId);
    const { data: tagsData } = useCaseNoteTags();

    const handleDateChange = (value: string) => {
        setSelectedDate(value);
    };

    const handleSubmit = async () => {
        if (!title.trim()) return;

        try {
            await createNoteMutation.mutateAsync({
                lead: parseInt(leadId),
                title: title,
                content: content,
                tagged_user_ids: selectedUsers,
                tag_ids: selectedTags
            });

            setTitle('');
            setContent("");
            setSelectedUsers([]);
            setSelectedTags([]);
            setIsExpanded(false);
        } catch (error) {
            console.error('Failed to create note:', error);
        }
    };

    const handleDelete = async (noteId: number) => {
        try {
            await deleteNoteMutation.mutateAsync(noteId);
        } catch (error) {
            console.error('Failed to delete note:', error);
        }
    };

    const handleEdit = async () => {
        if (!editingNote) return;

        try {
            await updateNoteMutation.mutateAsync({
                noteId: editingNote.id,
                data: {
                    title: editingNote.title,
                    content: editingNote.content,
                    tagged_user_ids: editingNote.tagged_user_ids,
                    tag_ids: editingNote.tag_ids
                }
            });
            setEditingNote(null);
        } catch (error) {
            console.error('Failed to update note:', error);
        }
    };

    const handleEditClick = (note: LeadNote) => {
        // If already editing this note, cancel editing
        if (editingNote?.id === note.id) {
            setEditingNote(null);
            return;
        }

        // Start editing new note
        setEditingNote({
            id: note.id,
            title: note.title,
            content: note.content,
            tagged_user_ids: note.tagged_users.map((u: Partial<PartialUserDetail>) => u.id) as number[],
            tag_ids: note.tags.map((t: CaseNoteTag) => t.id)
        });
    };

    const filteredNotes = (notesData?.results || []).filter(note => {
        const matchesSearch = searchQuery
            ? note.content.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        const matchesDate = selectedDate
            ? format(new Date(note.created_at), 'dd-MM-yyyy') === selectedDate
            : true;
        const matchesCreator = selectedCreator
            ? note.created_by.id.toString() === selectedCreator
            : true;
        return matchesSearch && matchesDate && matchesCreator;
    });

    if (isLoading) {
        return <div className="flex items-center justify-center h-full">Loading notes...</div>;
    }

    return (
        <div className="h-full flex flex-col min-h-0">
            {/* Fixed Header Section */}
            <div className="flex-none p-4 border-b bg-white">
                {/* Search and Filter Controls */}
                <div className="flex flex-col items-stretch gap-2 mb-4">
                    <div className="flex flex-wrap gap-2">
                        <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
                            <CustomDateInput
                                value={selectedDate}
                                onChange={handleDateChange}
                                maxDate={new Date()}
                                className="w-full"
                                error={false}
                                placeholder="Filter by date"
                            />
                        </div>

                        <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
                            <Select value={selectedCreator} onValueChange={setSelectedCreator}>
                                <SelectTrigger className="w-full bg-white rounded-full">
                                    <div className="flex items-center gap-2">
                                        <User2 className={ICON_CLASSES} />
                                        {selectedCreator ? (
                                            users.find(u => u.id.toString() === selectedCreator)?.name
                                        ) : (
                                            <span className="text-muted-foreground">Created By</span>
                                        )}
                                    </div>
                                </SelectTrigger>
                                <SelectContent>
                                    <div className="flex flex-col">
                                        <div className="max-h-[200px] overflow-y-auto">
                                            {users.map(user => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name}
                                                </SelectItem>
                                            ))}
                                        </div>
                                        {selectedCreator && (
                                            <Button
                                                variant="ghost"
                                                className="mt-2"
                                                onClick={() => setSelectedCreator(undefined)}
                                            >
                                                Clear Selection
                                            </Button>
                                        )}
                                    </div>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px] relative">
                            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                <Search className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <Input
                                placeholder="Search notes..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-9 rounded-full w-full"
                            />
                        </div>
                    </div>
                </div>

                {/* Add Note Button/Form */}
                {!isExpanded ? (
                    <button
                        onClick={() => setIsExpanded(true)}
                        className="w-full flex items-center gap-2 p-3 border rounded-lg text-muted-foreground hover:bg-gray-50 transition-colors"
                    >
                        <Plus className={ICON_CLASSES} />
                        <span>Take a note...</span>
                    </button>
                ) : (
                    <div className="border rounded-lg p-4 space-y-4 bg-white">
                        <Input
                            placeholder="Title"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                        />
                        <CustomCKEditor
                            initialValue={content}
                            onChange={(value: string) => {
                                setContent(value);
                            }}
                            placeholder="Take a note..."
                            minHeight="100px"
                            className="min-h-[100px] resize-none bg-gray-50"
                        />
                        <div className="flex items-center gap-2">
                            <Select
                                onValueChange={(value) => {
                                    const userId = parseInt(value);
                                    setSelectedUsers(prev =>
                                        prev.includes(userId)
                                            ? prev.filter(id => id !== userId)
                                            : [...prev, userId]
                                    );
                                }}
                            >
                                <SelectTrigger className="w-[200px]">
                                    <div className="flex items-center gap-2">
                                        <User2 className={ICON_CLASSES} />
                                        <span>Tag users</span>
                                    </div>
                                </SelectTrigger>
                                <SelectContent searchable>
                                    {users.map(user => (
                                        <SelectItem
                                            key={user.id}
                                            value={user.id.toString()}
                                            className="flex items-center gap-2"
                                        >
                                            <div className="flex items-center gap-2">
                                                {selectedUsers.includes(user.id) && "✓"}
                                                {user.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {selectedUsers.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                    {selectedUsers.map(userId => {
                                        const user = users.find(u => u.id === userId);
                                        return user && (
                                            <Badge
                                                key={userId}
                                                variant="secondary"
                                                className="flex items-center gap-1"
                                            >
                                                {user.name}
                                                <button
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        setSelectedUsers(prev =>
                                                            prev.filter(id => id !== userId)
                                                        );
                                                    }}
                                                    className="ml-1 hover:text-red-500"
                                                >
                                                    ×
                                                </button>
                                            </Badge>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <MultiSelect
                                options={tagsData?.map((tag) => ({
                                    label: tag.name,
                                    value: tag.id.toString()
                                })) || []}
                                onValueChange={(values) => setSelectedTags(values.map(v => parseInt(v)))}
                                placeholder="Select tags"
                                defaultValue={[]}
                            />
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsExpanded(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleSubmit} disabled={!title.trim()}>
                                Done
                            </Button>
                        </div>
                    </div>
                )}
            </div>

            {/* Scrollable Content Section */}
            <div className="flex-1 overflow-y-auto min-h-0 bg-gray-50">
                <div className="p-4 space-y-4">
                    {filteredNotes.map((note) => (
                        <div
                            key={note.id}
                            className="group border rounded-lg p-4 hover:shadow-sm bg-white transition-shadow"
                        >
                            <div className="flex items-start justify-between gap-4">
                                <div className="flex-1">
                                    {editingNote?.id === note.id ? (
                                        <div className="space-y-4">
                                            <Input
                                                value={editingNote.title}
                                                onChange={(e) => setEditingNote(prev => prev ? ({
                                                    ...prev,
                                                    title: e.target.value
                                                }) : null)}
                                                placeholder="Title"
                                            />
                                            <RichTextEditor
                                                value={editingNote.content}
                                                onChange={(value) => setEditingNote(prev => prev ? ({
                                                    ...prev,
                                                    content: value
                                                }) : null)}
                                                placeholder="Edit note..."
                                            />
                                            <div className="flex items-center gap-2">
                                                <Select
                                                    onValueChange={(value) => {
                                                        const userId = parseInt(value);
                                                        setEditingNote(prev => prev ? ({
                                                            ...prev,
                                                            tagged_user_ids: prev.tagged_user_ids.includes(userId)
                                                                ? prev.tagged_user_ids.filter(id => id !== userId)
                                                                : [...prev.tagged_user_ids, userId]
                                                        }) : null);
                                                    }}
                                                >
                                                    <SelectTrigger className="w-[200px]">
                                                        <div className="flex items-center gap-2">
                                                            <User2 className={ICON_CLASSES} />
                                                            <span>Tag users</span>
                                                        </div>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {users.map(user => (
                                                            <SelectItem
                                                                key={user.id}
                                                                value={user.id.toString()}
                                                                className="flex items-center gap-2"
                                                            >
                                                                <div className="flex items-center gap-2">
                                                                    {editingNote.tagged_user_ids.includes(user.id) && "✓"}
                                                                    {user.name}
                                                                </div>
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {editingNote.tagged_user_ids.length > 0 && (
                                                    <div className="flex flex-wrap gap-1">
                                                        {editingNote.tagged_user_ids.map(userId => {
                                                            const user = users.find(u => u.id === userId);
                                                            return user && (
                                                                <Badge
                                                                    key={userId}
                                                                    variant="secondary"
                                                                    className="flex items-center gap-1"
                                                                >
                                                                    {user.name}
                                                                    <button
                                                                        onClick={(e) => {
                                                                            e.preventDefault();
                                                                            setEditingNote(prev => prev ? ({
                                                                                ...prev,
                                                                                tagged_user_ids: prev.tagged_user_ids.filter(id => id !== userId)
                                                                            }) : null);
                                                                        }}
                                                                        className="ml-1 hover:text-red-500"
                                                                    >
                                                                        ×
                                                                    </button>
                                                                </Badge>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <MultiSelect
                                                    options={tagsData?.map(tag => ({
                                                        label: tag.name,
                                                        value: tag.id.toString()
                                                    })) || []}
                                                    onValueChange={(values) =>
                                                        setEditingNote(prev => prev ? ({
                                                            ...prev,
                                                            tag_ids: values.map(v => parseInt(v))
                                                        }) : null)
                                                    }
                                                    defaultValue={editingNote.tag_ids.map(id => id.toString())}
                                                    placeholder="Select tags"
                                                />
                                            </div>
                                            <div className="flex justify-end gap-2">
                                                <Button variant="outline" onClick={() => setEditingNote(null)}>
                                                    Cancel
                                                </Button>
                                                <Button onClick={handleEdit}>
                                                    Save
                                                </Button>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            <h3 className="text-lg font-semibold mb-2">{note.title}</h3>
                                            <RichTextViewer data={note.content} />
                                            <div className="mt-2 text-sm text-gray-500">
                                                From {note.created_by.first_name}, Created on {format(new Date(note.created_at), 'dd MMM yyyy hh:mm a')}
                                            </div>
                                            {note.tagged_users.length > 0 && (
                                                <div className="mt-2 flex gap-1">
                                                    {note.tagged_users.map((user: Partial<PartialUserDetail>) => (
                                                        <Badge key={user.id} variant="secondary">
                                                            {user.first_name} {user.last_name}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                            {note.tags && note.tags.length > 0 && (
                                                <div className="mt-2 flex gap-1">
                                                    {note.tags.map((tag: CaseNoteTag) => (
                                                        <Badge key={tag.id} variant="secondary">
                                                            {tag.name}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                                <div className="flex gap-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="opacity-0 group-hover:opacity-100 h-8 w-8"
                                            >
                                                <MoreVertical className={ICON_CLASSES} />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem
                                                onClick={() => handleEditClick(note)}
                                                className="gap-2"
                                            >
                                                <Pencil className={ICON_CLASSES} />
                                                <span>Edit</span>
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                onClick={() => handleDelete(note.id)}
                                                className="gap-2 text-red-600"
                                            >
                                                <Trash2 className={ICON_CLASSES} />
                                                <span>Delete</span>
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                        </div>
                    ))}

                    {/* Load more trigger */}
                    <div ref={loadMoreRef} className="h-4">
                        {isLoading && (
                            <div className="text-center text-sm text-gray-500">
                                Loading more notes...
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
} 