import React, { ReactNode, Fragment, FC, useEffect, useState, useRef } from "react";
import { Descendant } from "slate";
import { CustomElement, CustomText } from "@/components/ui/RichTextEditor";
import { stringToRichText } from "@/utils/richTextUtils";
import { Check, Copy } from "lucide-react"
import { Button } from "./button"
import { useToast } from "@/hooks/use-toast"

interface RichTextViewerProps {
  data: string;
  className?: string;
  isCopied?: boolean;
}

interface IframeRendererProps {
  content: string;
  className?: string;
}

type RecipientInput = string | {
  name?: string;
  email?: string;
};

type FormattedRecipient = {
  name: string;
  email: string;
};

const IframeRenderer: FC<IframeRendererProps> = ({ content, className = "" }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

      if (iframeDocument) {
        // Add default styles and content
        iframeDocument.open();
        iframeDocument.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <style>
                body {
                  margin: 0;
                  font-family: system-ui, -apple-system, sans-serif;
                  color: #374151;
                }
                img { max-width: 100%; height: auto; }
                table { width: 100%; border-collapse: collapse; }
                td, th { border: 1px solid #e5e7eb; padding: 8px; }
                a { color: #2563eb; }
                p { margin-bottom: 1rem; }
              </style>
            </head>
            <body>${content}</body>
          </html>
        `);
        iframeDocument.close();

        // Adjust iframe height to content
        const resizeObserver = new ResizeObserver(() => {
          if (iframeDocument.body) {
            iframe.style.height = `${iframeDocument.body.scrollHeight}px`;
          }
        });

        resizeObserver.observe(iframeDocument.body);

        return () => {
          resizeObserver.disconnect();
        };
      }
    }
  }, [content]);

  return (
    <iframe
      ref={iframeRef}
      className={`w-full border-none ${className}`}
      title="content-viewer"
    />
  );
};

const RichTextViewer: FC<RichTextViewerProps> = ({ data, className = "", isCopied = false }) => {
  const [isClient, setIsClient] = useState(false);
  const [parsedData, setParsedData] = useState<Descendant[]>([]);
  const [hasCopied, setHasCopied] = useState<string | null>(null);
  const { toast } = useToast();

  // Check if content is email-like format
  const isEmailFormat = data ? data.includes("Email Attached:") || data.includes("Email Details:") || data.includes("Subject:") || data.includes("From:") : false;
  const isHtml = data ? data.includes("</") || data.includes("/>") : false;

  // Function to process HTML content
  const processHtmlContent = (html: string) => {
    // Add responsive container styles
    html = html.replace(/<div/g, '<div class="max-w-full mx-auto break-words"');

    // Add responsive styles for tables
    html = html.replace(/<table/g, '<table class="min-w-full border-collapse table-auto overflow-x-auto block md:table my-4"');
    html = html.replace(/<td/g, '<td class="border p-2 break-words text-sm md:text-base"');
    html = html.replace(/<th/g, '<th class="border p-2 bg-gray-50 text-sm md:text-base font-semibold"');

    // Add responsive styles for images
    html = html.replace(/<img/g, '<img class="max-w-full h-auto rounded-lg my-4 shadow-sm hover:shadow-md transition-shadow"');

    // Add styles for links
    html = html.replace(/<a/g, '<a class="text-blue-600 hover:text-blue-800 underline decoration-blue-400"');

    // Add spacing and responsive text for paragraphs
    html = html.replace(/<p/g, '<p class="mb-4 text-gray-700 text-sm md:text-base leading-relaxed"');

    // Add styles for lists
    html = html.replace(/<ul/g, '<ul class="list-disc pl-4 md:pl-5 mb-4 space-y-2"');
    html = html.replace(/<ol/g, '<ol class="list-decimal pl-4 md:pl-5 mb-4 space-y-2"');
    html = html.replace(/<li/g, '<li class="text-gray-700 text-sm md:text-base mb-1"');

    // Add responsive styles for headings
    html = html.replace(/<h1/g, '<h1 class="text-xl md:text-2xl font-bold mb-4 text-gray-900"');
    html = html.replace(/<h2/g, '<h2 class="text-lg md:text-xl font-bold mb-3 text-gray-900"');
    html = html.replace(/<h3/g, '<h3 class="text-base md:text-lg font-bold mb-2 text-gray-900"');

    // Add styles for blockquotes
    html = html.replace(/<blockquote/g, '<blockquote class="border-l-4 border-gray-300 pl-4 my-4 italic text-gray-600 text-sm md:text-base"');

    // Add styles for code blocks
    html = html.replace(/<pre/g, '<pre class="bg-gray-50 rounded-lg p-3 overflow-x-auto text-sm my-4"');
    html = html.replace(/<code/g, '<code class="font-mono text-sm bg-gray-50 rounded px-1"');

    return html;
  };

  useEffect(() => {
    setIsClient(true);
    if (!isHtml) {
      setParsedData(stringToRichText(data));
    }
  }, [data, isHtml]);

  // Return loading or placeholder state during SSR
  if (!isClient) {
    return <div className={className}>Loading...</div>;
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setHasCopied(text);
      toast({
        description: "Copied to clipboard",
      });
      setTimeout(() => setHasCopied(null), 2000);
    } catch (err) {
      console.log("error at copy time", err);
    }
  };

  const renderCopyableContent = (text: string) => {
    // Email regex pattern
    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    // Phone regex pattern (basic)
    const phonePattern = /(?:\+\d{1,3}[-. ]?)?\(?\d{3}\)?[-. ]?\d{3}[-. ]?\d{4}/g;

    let lastIndex = 0;
    const elements: ReactNode[] = [];

    // Check for emails
    let match: RegExpExecArray | null;
    while ((match = emailPattern.exec(text)) !== null) {
      // Add text before the match
      if (match.index > lastIndex) {
        elements.push(text.slice(lastIndex, match.index));
      }
      lastIndex = match.index + match[0].length;

      // Always render the email link, conditionally add copy button
      elements.push(
        <div key={`email-${match.index}`} className="inline-flex items-center gap-1">
          <a
            href={`mailto:${match[0]}`}
            className="text-blue-600 hover:underline"
            onClick={(e) => {
              // Prevent default only if copy functionality is enabled
              if (isCopied) {
                e.preventDefault();
                copyToClipboard(match![0]);
              }
            }}
          >
            {match[0]}
          </a>
          {isCopied && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 hover:bg-muted"
              onClick={() => copyToClipboard(match![0])}
            >
              {hasCopied === match[0] ? (
                <Check className="h-3 w-3" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      );
    }

    // Check for phone numbers
    while ((match = phonePattern.exec(text)) !== null) {
      // Add text before the match
      if (match.index > lastIndex) {
        elements.push(text.slice(lastIndex, match.index));
      }
      lastIndex = match.index + match[0].length;

      // Always render the phone link, conditionally add copy button
      elements.push(
        <div key={`phone-${match.index}`} className="inline-flex items-center gap-1">
          <a
            href={`tel:${match[0].replace(/\D/g, '')}`}
            className="text-blue-600 hover:underline"
            onClick={(e) => {
              // Prevent default only if copy functionality is enabled
              if (isCopied) {
                e.preventDefault();
                copyToClipboard(match![0]);
              }
            }}
          >
            {match[0]}
          </a>
          {isCopied && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 hover:bg-muted"
              onClick={() => copyToClipboard(match![0])}
            >
              {hasCopied === match[0] ? (
                <Check className="h-3 w-3" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      );
    }

    // Add remaining text
    if (lastIndex < text.length) {
      elements.push(text.slice(lastIndex));
    }

    return elements;
  };

  const renderLeaf = (leaf: CustomText) => {
    let content: ReactNode = leaf.text || "";

    if (leaf.code) {
      content = <code className="bg-gray-100 px-1 rounded">{content}</code>;
    }
    if (leaf.bold) {
      content = <strong>{renderCopyableContent(content as string)}</strong>;
    }
    if (leaf.italic) {
      content = <em>{renderCopyableContent(content as string)}</em>;
    }
    if (leaf.underline) {
      content = <u>{renderCopyableContent(content as string)}</u>;
    }

    // If no formatting, still check for copyable content
    if (!leaf.code && !leaf.bold && !leaf.italic && !leaf.underline) {
      content = renderCopyableContent(content as string);
    }

    return content;
  };

  const renderNode = (node: Descendant) => {
    // Add validation for node
    if (!node || typeof node !== 'object') {
      console.warn('Invalid node received:', node);
      return null;
    }

    if ('text' in node) {
      return renderLeaf(node as CustomText);
    }

    // Validate children exist and are in correct format
    if (!('children' in node) || !Array.isArray(node.children)) {
      console.warn('Node missing valid children:', node);
      return null;
    }

    const children = node.children.map((child: Descendant, i: number) => (
      <Fragment key={i}>{renderNode(child)}</Fragment>
    ));

    // Type check for node type
    if (!('type' in node)) {
      console.warn('Node missing type:', node);
      return <div className="mb-2">{children}</div>;
    }

    switch (node.type) {
      case 'paragraph':
        return <div className="mb-2 overflow-hidden overflow-wrap-anywhere">{children}</div>;
      case 'block-quote':
        return (
          <blockquote className="border-l-4 border-gray-300 pl-4 my-2 text-gray-600 overflow-wrap-anywhere">
            {children}
          </blockquote>
        );
      case 'bulleted-list':
        return <ul className="list-disc ml-6 my-2 overflow-wrap-anywhere">{children}</ul>;
      case 'list-item':
        return <li className="overflow-wrap-anywhere">{children}</li>;
      case 'numbered-list':
        return <ol className="list-decimal ml-6 my-2 overflow-wrap-anywhere">{children}</ol>;
      default:
        return <div className="mb-2 overflow-hidden overflow-wrap-anywhere">{children}</div>;
    }
  };

  const getPlainTextWithFormat = (data: string): string => {
    const result: string[] = [];

    const nodes = Array.isArray(data) ? data : [data];

    nodes.forEach(node => {
      if ((node as CustomElement).type === 'paragraph') {
        const paragraphText = Array.isArray((node as CustomElement).children) ?
          (node as CustomElement).children.map((leaf: CustomText) => {
            const format: string[] = [];
            if (leaf.bold) format.push('bold');
            if (leaf.italic) format.push('italic');
            if (leaf.underline) format.push('underline');
            if (leaf.code) format.push('code');

            return format.length > 0
              ? `${leaf.text} (${format.join(', ')})`
              : leaf.text;
          }).join(' ') : '';

        result.push(paragraphText);
      }
    });

    return result.join('\n');
  };

  if (className === 'plain-text') {
    return <div>{getPlainTextWithFormat(data)}</div>;
  }

  if (isEmailFormat) {
    // Extract email parts
    const tryParseJson = (str: string) => {
      try {
        // First try to parse the JSON string
        let parsed;
        try {
          parsed = JSON.parse(str);
        } catch {
          // If it looks like a JSON string but failed to parse, try cleaning it up
          const cleanedStr = str.replace(/'/g, '"')  // Replace single quotes with double quotes
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // Add quotes around keys
            .replace(/\]\s*,\s*\{/g, '], {')  // Fix array formatting
            .replace(/\}\s*,\s*\{/g, '}, {'); // Fix object formatting
          parsed = JSON.parse(cleanedStr);
        }

        // Function to format a single recipient
        const formatRecipient = (item: RecipientInput): FormattedRecipient => {
          if (typeof item === 'object' && item !== null) {
            const name = item.name?.trim() || '';
            const email = item.email?.trim() || '';
            if (name && email) {
              return { name, email };
            } else if (email) {
              return { name: email, email };
            } else if (name) {
              return { name, email: '' };
            }
            return { name: JSON.stringify(item), email: '' };
          }
          return { name: String(item), email: '' };
        };

        // Handle array of recipients
        if (Array.isArray(parsed)) {
          return parsed.map(formatRecipient);
        }

        // Handle comma-separated objects that might not be in proper array format
        if (typeof parsed === 'object' && !Array.isArray(parsed)) {
          const recipients = str.split(/(?<=}),\s*(?={)/);
          if (recipients.length > 1) {
            return recipients.map(recipient => {
              try {
                const cleanedRecipient = recipient.replace(/'/g, '"').replace(/([{,]\s*)(\w+):/g, '$1"$2":');
                return formatRecipient(JSON.parse(cleanedRecipient));
              } catch {
                return formatRecipient(recipient);
              }
            });
          }
        }

        // Handle single recipient
        return [formatRecipient(parsed)];
      } catch {
        // If all parsing fails, return the original string
        return [{ name: str, email: '' }];
      }
    };

    const emailParts = {
      subject: data.match(/Subject:\s*(.*?)(?=\n|$)/)?.[1]?.trim() || "",
      from: data.match(/From:\s*(.*?)(?=\n|$)/)?.[1]?.trim() || "",
      to: data.match(/To:\s*(.*?)(?=\n|$)/)?.[1]?.trim() || "",
      date: data.match(/Date:\s*(.*?)(?=\n|$)/)?.[1]?.trim() || "",
      body: data.split(/Body:\s*/)?.[1]?.trim() || data,
      attachments: data.match(/Attachments:\s*(.*?)(?=\n|$)/)?.[1]?.trim() || ""
    };

    // Try to parse JSON objects if they exist
    const fromData = tryParseJson(emailParts.from);
    const toData = tryParseJson(emailParts.to);

    // Function to render recipients
    const renderRecipients = (recipients: Array<{ name: string; email: string }>) => {
      return (
        <div className="flex flex-col gap-1">
          {recipients.map((recipient, index) => (
            <div key={index} className="flex items-start flex-col text-sm">
              <span className="text-gray-900">{recipient.name}</span>
              {recipient.email && (
                <a
                  href={`mailto:${recipient.email}`}
                  className="text-blue-600 hover:text-blue-800"
                >
                  {recipient.email}
                </a>
              )}
            </div>
          ))}
        </div>
      );
    };

    return (
      <div className={`space-y-4 overflow-hidden overflow-wrap-anywhere w-full ${className}`}>
        {/* Email Header */}
        {/* <div className="bg-gray-50 p-4 rounded-lg space-y-3">
          {emailParts.subject && (
            <div className="flex items-start gap-2">
              <span className="font-medium text-gray-500 min-w-[4.5rem]">Subject:</span>
              <span className="font-medium text-gray-900 overflow-wrap-anywhere">{emailParts.subject}</span>
            </div>
          )}
          {emailParts.from && (
            <div className="flex items-start gap-2">
              <span className="font-medium text-gray-500 min-w-[4.5rem]">From:</span>
              <div className="flex-1 overflow-wrap-anywhere">
                {renderRecipients(fromData)}
              </div>
            </div>
          )}
          {emailParts.to && (
            <div className="flex items-start gap-2">
              <span className="font-medium text-gray-500 min-w-[4.5rem]">To:</span>
              <div className="flex-1 overflow-wrap-anywhere">
                {renderRecipients(toData)}
              </div>
            </div>
          )}
          {emailParts.date && (
            <div className="flex items-start gap-2">
              <span className="font-medium min-w-20">Date:</span>
              <span>{formatDate(emailParts.date)}</span>
            </div>
          )}
          {emailParts.attachments && emailParts.attachments !== "0 files" && (
            <div className="flex items-start gap-2">
              <span className="font-medium min-w-20">Attachments:</span>
              <span className="overflow-wrap-anywhere">{emailParts.attachments}</span>
            </div>
          )}
        </div> */}

        {/* Email Body */}
        <div className="mt-4 bg-white p-4 rounded-lg overflow-hidden">
          {isHtml ? (
            <IframeRenderer content={processHtmlContent(emailParts.body)} />
          ) : (
            <div className="whitespace-pre-wrap text-gray-700 text-sm leading-relaxed overflow-wrap-anywhere">{emailParts.body}</div>
          )}
        </div>
      </div>
    );
  }

  if (isHtml) {
    return <IframeRenderer content={processHtmlContent(data)} />;
  }

  if (!parsedData) return null;

  return (
    <div className={`overflow-hidden overflow-wrap-anywhere w-full ${className} text-[0.95em]`}>
      {Array.isArray(parsedData) ?
        parsedData.map((node, i) => (
          <Fragment key={i}>{renderNode(node)}</Fragment>
        )) :
        renderNode(parsedData)
      }
    </div>
  );
};

export default RichTextViewer; 