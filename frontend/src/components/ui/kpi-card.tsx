import React, { Fragment, ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Settings, LucideIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CurrencyDisplay } from "./currency-display";

export interface KPIMetric {
  type: 'monetary' | 'percentage' | 'time' | 'number' | 'count' | 'rate' | 'date';
  value: number | string;
  unit?: string;
}

// Required base props
interface KPICardBaseProps {
  icon: LucideIcon;
  title: string;
  metrics: {
    primary: KPIMetric;
    total_items?: number;
  };
}

// Optional props
interface KPICardOptionalProps {
  description?: string;
  additionalInfo?: ReactNode;
  details?: boolean;
  onSettingsClick?: () => void;
  className?: string;
  showProgress?: boolean;
  variant?: 'default' | 'dashboard';
  rate?: KPIMetric;
  bgColor?: string;
  isCurrency?: boolean;
}

// Combine required and optional props
export type KPICardProps = KPICardBaseProps & KPICardOptionalProps;

const formatKPIValue = (metric: KPIMetric) => {
  switch (metric.type) {
    case 'monetary':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 0
      }).format(Number(metric.value) || 0);
    case 'percentage':
    case 'rate':
      return `${metric.value}%`;
    case 'time':
      if (metric.unit === 'days') {
        return `${metric.value} days`;
      }
      return metric.value;
    case 'date':
      return metric.value ? new Date(metric.value).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }) : "-";
    case 'count':
    case 'number':
      return metric.value.toLocaleString();
    default:
      return metric.value.toLocaleString();
  }
};

export function KPICard({
  // Required props
  icon: Icon,
  title,
  metrics,
  // Optional props with defaults
  description,
  additionalInfo,
  details = false,
  onSettingsClick,
  className,
  showProgress = false,
  variant = 'default',
  rate,
  bgColor,
  isCurrency = false
}: KPICardProps) {
  // Calculate progress only if showProgress is true and rate is provided
  const progress = showProgress && rate ? Number(rate.value) :
    showProgress && metrics.total_items && metrics.primary.value ? (Number(metrics.primary.value) / metrics.total_items) * 100 : 0;

  const cardClasses = cn(
    "relative group",
    variant === 'dashboard' ?
      `${bgColor || 'bg-gray-50'} hover:shadow-md transition-all duration-200 rounded-lg border border-gray-200` :
      `${bgColor || 'bg-gray-50'} hover:bg-gray-50/90 transition-all duration-200 rounded-lg border border-gray-200`,
    className
  );

  const CardWrapper = additionalInfo ? HoverCard : Fragment;
  const CardTrigger = additionalInfo ? HoverCardTrigger : Fragment;

  const mainContent = (
    <Card className={cardClasses}>
      <CardContent className="p-0 relative w-full">
        <div className="p-2">
          {/* Settings button - Optional */}
          {onSettingsClick && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={onSettingsClick}
            >
              <Settings className="h-4 w-4" />
            </Button>
          )}

          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-2 text-gray-900">
              {/* Icon - Required */}
              <Icon className="h-5 w-5 text-gray-600" />
              <div className="text-[1.2rem] font-semibold text-gray-900">
                {isCurrency ? <CurrencyDisplay amount={Number(metrics.primary.value)} /> : formatKPIValue(metrics.primary)}
              </div>
            </div>
            <div className="space-y-2">
              {/* Title and Description - Title required, Description optional */}
              <div className="flex justify-between items-center gap-2">
                <div className="text-[0.9rem] text-gray-600 uppercase">
                  {title}
                </div>

                {metrics.total_items && (
                  <div className="text-sm text-gray-500 opacity-50 shrink-0">
                    {metrics.total_items} {metrics.total_items === 1 ? description?.replace(/s$/, '') || '' : description}
                  </div>
                )}
              </div>

              {/* Progress bar - Optional */}
              {showProgress && (
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <div className="w-full bg-white border border-gray-100 rounded-full h-1">
                      <div
                        className="bg-gray-900 h-1 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min(Number(progress), 100)}%` }}
                      />
                    </div>
                    <div className="text-sm text-gray-400 opacity-50 shrink-0 min-w-[32px] text-right">
                      {Math.round(Number(progress))}%
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {details && (
          <div className="absolute bottom-2 right-2">
            <div className="text-xs text-gray-400 italic flex items-center gap-1">
              Details <span className="text-[10px]">↑</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <CardWrapper>
      {additionalInfo ? (
        <>
          <CardTrigger asChild>
            {mainContent}
          </CardTrigger>
          <HoverCardContent
            side="top"
            align="center"
            sideOffset={12}
            className="w-80 shadow-lg border-gray-200/50 bg-white/95 backdrop-blur-sm z-[60]"
            style={{
              position: 'relative',
              zIndex: 60
            }}
          >
            {additionalInfo}
          </HoverCardContent>
        </>
      ) : mainContent}
    </CardWrapper>
  );
} 