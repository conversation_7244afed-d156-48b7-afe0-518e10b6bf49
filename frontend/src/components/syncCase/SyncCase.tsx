import { createContext, useContext, useCallback, createElement, FC, ReactNode } from 'react';
import { UseMutationResult } from '@tanstack/react-query';
import {
  useDefendantSyncMutation,
  useNoteSyncMutation,
  useTaskSyncMutation,
  useUserCardSyncMutation,
  useClientSyncMutation,
  useIncidentDetailsSyncMutation,
  usePartySyncMutation,
  useTreatmentProviderSyncMutation,
  useLienSyncMutation,
  useWorkerSyncMutation,
  useCaseStatusSyncMutation,
  useExpertWitnessSyncMutation,
  useAttorneyLienSyncMutation,
  useMiscellaneousLienSyncMutation,
} from '@/services/case-management/caseSyncService';

// Base types for sync requests and responses
interface BaseSyncRequest {
  target_case_ids: string[];
}

interface BaseSyncResponse {
  message: string;
  synced_cases: string[];
  errors: string[] | null;
}

// API response type that matches the actual API response structure
interface ApiResponse {
  message?: string;
  synced_cases?: string[];
  errors?: string[] | null;
}

// Mutation types
type SyncMutationResult<T extends BaseSyncRequest> = UseMutationResult<
  BaseSyncResponse,
  Error,
  T,
  unknown
>;

// Specific sync request types
interface DefendantSyncRequest extends BaseSyncRequest {
  source_defendant_id: number;
  sync_options: {
    sync_insurances: boolean;
    sync_property_damage: boolean;
    sync_legal_representation: boolean;
    sync_adjusters: boolean;
  };
}

interface NoteSyncRequest extends BaseSyncRequest {
  source_note_id: number;
}

interface TaskSyncRequest extends BaseSyncRequest {
  source_task_id: number;
}

interface UserCardSyncRequest extends BaseSyncRequest {
  source_card_id: number;
}

interface ClientSyncRequest extends BaseSyncRequest {
  source_client_id: number;
  sync_options?: {
    sync_insurances: boolean;
    sync_property_damage: boolean;
    sync_legal_representation: boolean;
    sync_adjusters: boolean;
  };
}

interface IncidentDetailsSyncRequest extends BaseSyncRequest {
  source_incident_detail_id: number;
}

interface PartySyncRequest extends BaseSyncRequest {
  party_id: number;
}

interface ExpertWitnessSyncRequest extends BaseSyncRequest {
  expert_witness_id: number;
}

interface TreatmentProviderSyncRequest extends BaseSyncRequest {
  treatment_provider_id: number;
}

  interface AttorneyLienSyncRequest extends BaseSyncRequest {
    attorney_lien_id: number;
  }

  interface MiscLienSyncRequest extends BaseSyncRequest {
    miscellaneous_lien_id: number;
  }

interface WorkerSyncRequest extends BaseSyncRequest {
  source_worker_id: number;
}

interface StatusSyncRequest extends BaseSyncRequest {
  source_case_status_id?: number;
}

export interface Defendant {
  id: string;
  name: string;
  type: string;
  contact_info: string;
  status: string;
}

export interface Note {
  id: string;
  content: string;
  created_at: string;
  updated_at: string;
  author: string;
  type: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  due_date: string;
  status: string;
  assigned_to: string;
  priority: string;
}

export interface UserCard {
  id: string;
  title: string;
  content: string;
  type: string;
  created_at: string;
}

export interface ClientDetail {
  id: string;
  name: string;
  contact_info: string;
  address: string;
  email: string;
  phone: string;
}

export interface IncidentDetail {
  id: string;
  date: string;
  location: string;
  description: string;
  type: string;
  severity: string;
}

export interface CaseParty {
  id: string;
  name: string;
  role: string;
  type: string;
  contact_info: string;
}

export interface TreatmentProvider {
  id: string;
  name: string;
  type: string;
  contact_info: string;
  address: string;
  speciality: string;
}

export interface Lien {
  id: string;
  amount: number;
  type: string;
  status: string;
  provider: string;
  date: string;
}

export interface CaseWorker {
  id: string;
  name: string;
  role: string;
  contact_info: string;
  assigned_date: string;
}

export interface CaseStatus {
  id: string;
  status: string;
  last_updated: string;
  next_action: string;
  priority: string;
}

interface SyncOperations {
  syncDefendants: () => Promise<BaseSyncResponse>;
  syncNotes: () => Promise<BaseSyncResponse>;
  syncTasks: () => Promise<BaseSyncResponse>;
  syncUserCards: () => Promise<BaseSyncResponse>;
  syncClientDetails: () => Promise<BaseSyncResponse>;
  syncIncidentDetails: () => Promise<BaseSyncResponse>;
  syncCaseParties: () => Promise<BaseSyncResponse>;
  syncTreatmentProviders: () => Promise<BaseSyncResponse>;
  syncCaseWorkers: () => Promise<BaseSyncResponse>;
  syncCaseStatus: () => Promise<BaseSyncResponse>;
  syncExpertWitnesses: () => Promise<BaseSyncResponse>;
  syncAttorneyLiens: () => Promise<BaseSyncResponse>;
  syncMiscLiens: () => Promise<BaseSyncResponse>;
}

const SyncCaseContext = createContext<SyncOperations | null>(null);

interface SyncCaseProviderProps {
  caseId: string;
  sourceId: number;
  targetCaseIds: string[];
  children: ReactNode;
}

// type SyncMutationHook<TRequest extends BaseSyncRequest> = UseMutationResult<
//   BaseSyncResponse,
//   Error,
//   TRequest,
//   unknown
// >;

export const SyncCaseProvider: FC<SyncCaseProviderProps> = ({ caseId, sourceId, targetCaseIds, children }) => {
  const defendantSync = useDefendantSyncMutation(caseId);
  const noteSync = useNoteSyncMutation(caseId);
  const taskSync = useTaskSyncMutation(caseId);
  const userCardSync = useUserCardSyncMutation(caseId);
  const clientSync = useClientSyncMutation(caseId);
  const incidentSync = useIncidentDetailsSyncMutation(caseId);
  const partySync = usePartySyncMutation(caseId);
  const providerSync = useTreatmentProviderSyncMutation(caseId);
  const attorneyLienSync = useAttorneyLienSyncMutation(caseId);
  const miscLienSync = useMiscellaneousLienSyncMutation(caseId);
  const workerSync = useWorkerSyncMutation(caseId);
  const statusSync = useCaseStatusSyncMutation(caseId);
  const expertWitnessSync = useExpertWitnessSyncMutation(caseId);

  const createSyncResponse = (response: unknown): BaseSyncResponse => {
    const resp = response as { message?: string; synced_cases?: string[]; errors?: string[] | null };
    return {
      message: String(resp?.message || ''),
      synced_cases: Array.isArray(resp?.synced_cases) ? resp.synced_cases : [], 
      errors: Array.isArray(resp?.errors) ? resp.errors : null,
    } as BaseSyncResponse;
  };

  const syncDefendants = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: DefendantSyncRequest = {
        source_defendant_id: sourceId,
        target_case_ids: targetCaseIds,
        sync_options: {
          sync_insurances: true,
          sync_property_damage: true,
          sync_legal_representation: true,
          sync_adjusters: true,
        },
      };
      const response = await defendantSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing defendants:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, defendantSync]);

  const syncNotes = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: NoteSyncRequest = {
        source_note_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await noteSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing notes:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, noteSync]);

  const syncTasks = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: TaskSyncRequest = {
        source_task_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await taskSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing tasks:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, taskSync]);

  const syncUserCards = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: UserCardSyncRequest = {
        source_card_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await userCardSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing user cards:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, userCardSync]);

  const syncClientDetails = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: ClientSyncRequest = {
        source_client_id: sourceId,
        target_case_ids: targetCaseIds,
        sync_options: {
          sync_insurances: true,
          sync_property_damage: true,
          sync_legal_representation: true,
          sync_adjusters: true,
        },
      };
      const response = await clientSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing client details:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, clientSync]);

  const syncIncidentDetails = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: IncidentDetailsSyncRequest = {
        source_incident_detail_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await incidentSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing incident details:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, incidentSync]);

  const syncCaseParties = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: PartySyncRequest = {
        party_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await partySync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing case parties:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, partySync]);

  const syncExpertWitnesses = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: ExpertWitnessSyncRequest = {
        expert_witness_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await expertWitnessSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing expert witnesses:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, expertWitnessSync]);
  
  const syncTreatmentProviders = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: TreatmentProviderSyncRequest = {
        treatment_provider_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await providerSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing treatment providers:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, providerSync]);

  const syncAttorneyLiens = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: AttorneyLienSyncRequest = {
        attorney_lien_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await attorneyLienSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing liens:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, attorneyLienSync]);

  const syncMiscLiens = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: MiscLienSyncRequest = {
        miscellaneous_lien_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await miscLienSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing miscellaneous liens:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, miscLienSync]);

  const syncCaseWorkers = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: WorkerSyncRequest = {
        source_worker_id: sourceId,
        target_case_ids: targetCaseIds,
      };
      const response = await workerSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing case workers:', error);
      throw error;
    }
  }, [sourceId, targetCaseIds, workerSync]);

  const syncCaseStatus = useCallback(async (): Promise<BaseSyncResponse> => {
    try {
      const request: StatusSyncRequest = {
        target_case_ids: targetCaseIds,
      };
      const response = await statusSync.mutateAsync(request);
      return createSyncResponse(response);
    } catch (error) {
      console.error('Error syncing case status:', error);
      throw error;
    }
  }, [targetCaseIds, statusSync]);

  const value = {
    syncDefendants,
    syncNotes,
    syncTasks,
    syncUserCards,
    syncClientDetails,
    syncIncidentDetails,
    syncCaseParties,
    syncTreatmentProviders,
    syncCaseWorkers,
    syncCaseStatus,
    syncExpertWitnesses,
    syncAttorneyLiens,
    syncMiscLiens
  };

  return (
    <SyncCaseContext.Provider value={value}>
      {children}
    </SyncCaseContext.Provider>
  );
};

export const useSyncCase = () => {
  const context = useContext(SyncCaseContext);
  if (!context) {
    throw new Error('useSyncCase must be used within a SyncCaseProvider');
  }
  return context;
}; 