'use client';

import React, { FC, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, Repeat } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import SyncCaseDialog from '../CaseOverview/components/SyncCaseDialog';
import { useSyncCase, SyncCaseProvider } from './SyncCase';

interface SyncCaseProps {
  caseId: string;
  sourceId?: number;
  syncType?: 'defendants' | 'notes' | 'tasks' | 'userCards' | 'client' | 'incident' | 'parties' | 'expert-witnesses' | 'providers' | 'workers' | 'status' | 'attorney-liens' | 'misc-liens';
  onSyncComplete?: () => void;
  onSyncError?: (error: Error) => void;
  className?: string;
}

const SyncCaseButton:FC<SyncCaseProps> = ({
  caseId,
  sourceId,
  syncType = 'defendants',
  onSyncComplete,
  onSyncError,
  className,
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();
  const {
    syncDefendants,
    syncNotes,
    syncTasks,
    syncUserCards,
    syncClientDetails,
    syncIncidentDetails,
    syncCaseParties,
    syncTreatmentProviders,
    syncAttorneyLiens,
    syncMiscLiens,
    syncCaseWorkers,
    syncCaseStatus
  } = useSyncCase();

  // Map of sync types to their corresponding sync functions
  const syncFunctionMap = {
    defendants: syncDefendants,
    notes: syncNotes,
    tasks: syncTasks,
    userCards: syncUserCards,
    client: syncClientDetails,
    incident: syncIncidentDetails,
    parties: syncCaseParties,
    providers: syncTreatmentProviders,
    'attorney-liens': syncAttorneyLiens,
    'misc-liens': syncMiscLiens,
    workers: syncCaseWorkers,
    status: syncCaseStatus
  };

  const handleSync = async (selectedCaseIds: string[]) => {
    setIsSyncing(true);
    try {
      const syncFunction = syncFunctionMap[syncType as keyof typeof syncFunctionMap];
      if (!syncFunction) {
        throw new Error(`Invalid sync type: ${syncType}`);
      }

      // Sync data for each selected case
      await Promise.all(selectedCaseIds.map(async () => {
        await syncFunction();
      }));

      toast({
        title: 'Success',
        description: `${syncType.charAt(0).toUpperCase() + syncType.slice(1)} data synchronized successfully`,
      });
      onSyncComplete?.();
      setIsDialogOpen(false);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `Failed to sync ${syncType} data`;
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      onSyncError?.(error instanceof Error ? error : new Error(errorMessage));
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className={className}>
      <Button
        onClick={() => setIsDialogOpen(true)}
        variant="link"
        size="sm"
        className="flex items-center gap-2"
        disabled={isSyncing}
      >
        {isSyncing ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Repeat className="h-4 w-4" />
        )}
        {/* <span>Sync {syncType.charAt(0).toUpperCase() + syncType.slice(1)}</span> */}
      </Button>

      <SyncCaseDialog
        caseId={caseId}
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onSync={handleSync}
        isSyncing={isSyncing}
        syncType={syncType}
      />
    </div>
  );
};

export const SyncCase:FC<SyncCaseProps> = (props) => {
  // Default to -1 if sourceId is not provided
  const sourceId = props.sourceId ?? -1;
  
  return (
    <SyncCaseProvider caseId={props.caseId} sourceId={sourceId} targetCaseIds={[props.caseId]}>
      <SyncCaseButton {...props} />
    </SyncCaseProvider>
  );
};

export default SyncCase; 