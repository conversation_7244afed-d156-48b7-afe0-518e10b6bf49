'use client';

import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { useEffect, useState, useCallback, useRef } from 'react';
import styles from './CKEditor.module.css';
import { Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
// import { saveAs } from 'file-saver';
import type { EditorConfig } from '@ckeditor/ckeditor5-core';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Type for our custom config that extends the base config
interface CustomConfig extends EditorConfig {
  toolbar: {
    items: string[];
    shouldNotGroupWhenFull: boolean;
  };
  table?: {
    contentToolbar: string[];
  };
  link?: {
    defaultProtocol: string;
    decorators: {
      openInNewTab: {
        mode: 'manual';
        label: string;
        defaultValue: boolean;
        attributes: {
          target: string;
          rel: string;
        };
      };
    };
  };
  removePlugins?: string[];
  ui?: {
    viewportOffset: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
  };
}

interface CKEditorProps {
  /**
   * Initial content for the editor
   */
  initialValue?: string;
  /**
   * Callback function triggered when editor content changes
   */
  onChange?: (data: string) => void;
  /**
   * Additional CSS class names
   */
  className?: string;
  /**
   * Placeholder text when editor is empty
   */
  placeholder?: string;
  /**
   * Whether the editor is disabled
   */
  disabled?: boolean;
  /**
   * Minimum height of the editor
   */
  minHeight?: string;
  /**
   * Whether to allow file downloads
   */
  allowDownload?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Editor = any;

/**
 * A reusable CKEditor component with TypeScript support
 * @component
 */
const CustomCKEditor: React.FC<CKEditorProps> = ({
  initialValue = '',
  onChange,
  className = '',
  placeholder = 'Start typing...',
  disabled = false,
  minHeight = '200px',
  allowDownload = false,
}) => {
  const [editorData, setEditorData] = useState<string>(initialValue);
  const [isReady, setIsReady] = useState<boolean>(false);
  const editorRef = useRef<HTMLDivElement>(null);

  // Update editor data when initialValue changes and editor is ready
  useEffect(() => {
    if (!isReady) return;
    setEditorData(initialValue);
  }, [initialValue, isReady]);

  const handleEditorReady = useCallback(() => {
    setIsReady(true);
  }, []);

  // const handleExportDoc = useCallback(() => {
  //   try {
  //     // Create a temporary container with proper styling
  //     const container = document.createElement('div');
  //     container.innerHTML = editorData;

  //     // Apply basic styling for Word
  //     const styledContent = `
  //       <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
  //       <head>
  //       <meta charset="utf-8">
  //       <style>
  //         body {
  //           font-family: Arial, sans-serif;
  //           font-size: 12pt;
  //           line-height: 1.6;
  //         }
  //         table {
  //           border-collapse: collapse;
  //           width: 100%;
  //           margin: 1em 0;
  //         }
  //         th, td {
  //           border: 1px solid #000;
  //           padding: 8pt;
  //           text-align: left;
  //         }
  //         th {
  //           background-color: #f0f0f0;
  //           font-weight: bold;
  //         }
  //         a {
  //           color: blue;
  //           text-decoration: underline;
  //         }
  //         h1 { font-size: 16pt; }
  //         h2 { font-size: 14pt; }
  //         h3 { font-size: 12pt; }
  //       </style>
  //       </head>
  //       <body>
  //         ${container.innerHTML}
  //       </body>
  //       </html>
  //     `;

  //     const blob = new Blob([styledContent], {
  //       type: 'application/msword;charset=utf-8'
  //     });
  //     saveAs(blob, 'document.doc');
  //   } catch (error) {
  //     console.error('Error exporting DOC:', error);
  //   }
  // }, [editorData]);

  // const handlePrint = useCallback(() => {
  //   try {
  //     if (!editorRef.current) return;

  //     const printWindow = window.open('', '_blank');
  //     if (!printWindow) return;

  //     printWindow.document.write(`
  //       <html>
  //         <head>
  //           <title>Print Document</title>
  //           <style>
  //             @page {
  //               size: A4;
  //               margin: 2cm;
  //             }
  //             body {
  //               font-family: Arial, sans-serif;
  //               line-height: 1.6;
  //               color: #1a1a1a;
  //               max-width: 21cm;
  //               margin: 0 auto;
  //               padding: 2cm;
  //               background: white;
  //             }
  //             table {
  //               width: 100%;
  //               border-collapse: collapse;
  //               margin: 1rem 0;
  //               border: 2px solid #e2e8f0;
  //             }
  //             th, td {
  //               border: 1px solid #e2e8f0;
  //               padding: 0.75rem;
  //               text-align: left;
  //               vertical-align: top;
  //             }
  //             th {
  //               background: #f8fafc;
  //               font-weight: 600;
  //               border-bottom: 2px solid #e2e8f0;
  //             }
  //             a {
  //               color: #2563eb;
  //               text-decoration: underline;
  //             }
  //             h1 {
  //               font-size: 24px;
  //               margin: 1.5rem 0 1rem;
  //             }
  //             h2 {
  //               font-size: 20px;
  //               margin: 1.25rem 0 0.875rem;
  //             }
  //             h3 {
  //               font-size: 16px;
  //               margin: 1rem 0 0.75rem;
  //             }
  //             p {
  //               margin: 0.75rem 0;
  //             }
  //             ul, ol {
  //               margin: 0.75rem 0;
  //               padding-left: 2rem;
  //             }
  //             img {
  //               max-width: 100%;
  //               height: auto;
  //             }
  //             @media print {
  //               body {
  //                 padding: 0;
  //               }
  //               a {
  //                 text-decoration: underline;
  //               }
  //               table {
  //                 page-break-inside: avoid;
  //               }
  //               tr {
  //                 page-break-inside: avoid;
  //               }
  //               thead {
  //                 display: table-header-group;
  //               }
  //               img {
  //                 page-break-inside: avoid;
  //               }
  //             }
  //           </style>
  //         </head>
  //         <body>
  //           ${editorData}
  //         </body>
  //       </html>
  //     `);

  //     printWindow.document.close();
  //     printWindow.focus();
  //     printWindow.print();
  //     printWindow.close();
  //   } catch (error) {
  //     console.error('Error printing:', error);
  //   }
  // }, [editorData]);

  const handleExportPDF = async () => {
    try {
      // Get the CKEditor content area
      const editorContent = document.querySelector('.ck-editor__editable');
      if (!editorContent) return;

      // Store original styles and scroll position
      const originalStyles = {
        height: (editorContent as HTMLElement).style.height,
        maxHeight: (editorContent as HTMLElement).style.maxHeight,
        overflow: (editorContent as HTMLElement).style.overflow,
        position: (editorContent as HTMLElement).style.position
      };
      const originalScroll = window.scrollY;

      // Create a temporary container for measuring full content
      const tempContainer = document.createElement('div');
      tempContainer.innerHTML = editorData;
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.visibility = 'hidden';
      document.body.appendChild(tempContainer);

      // Temporarily modify editor styles to show full content
      (editorContent as HTMLElement).style.height = 'auto';
      (editorContent as HTMLElement).style.maxHeight = 'none';
      (editorContent as HTMLElement).style.overflow = 'visible';
      (editorContent as HTMLElement).style.position = 'relative';

      // Get the full dimensions
      const fullHeight = Math.max(
        editorContent.scrollHeight,
        tempContainer.scrollHeight
      );

      // Clean up temporary container
      document.body.removeChild(tempContainer);

      // Set up html2canvas options
      const canvas = await html2canvas(editorContent as HTMLElement, {
        scale: 2, // Higher quality
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff',
        height: fullHeight,
        windowHeight: fullHeight,
        scrollY: -window.scrollY,
        onclone: (clonedDoc) => {
          const clonedContent = clonedDoc.querySelector('.ck-editor__editable');
          if (clonedContent) {
            (clonedContent as HTMLElement).style.height = `${fullHeight}px`;
            (clonedContent as HTMLElement).style.maxHeight = 'none';
            (clonedContent as HTMLElement).style.overflow = 'visible';
            
            // Ensure tables and other elements are fully visible
            const tables = clonedContent.getElementsByTagName('table');
            Array.from(tables).forEach(table => {
              table.style.display = 'table';
              table.style.pageBreakInside = 'avoid';
              table.style.maxHeight = 'none';
              table.style.overflow = 'visible';
            });

            // Handle other block elements
            const blockElements = clonedContent.querySelectorAll('div, p, ul, ol, h1, h2, h3, h4, h5, h6');
            blockElements.forEach(element => {
              (element as HTMLElement).style.maxHeight = 'none';
              (element as HTMLElement).style.overflow = 'visible';
            });
          }
        }
      });

      // Restore original styles
      Object.assign((editorContent as HTMLElement).style, originalStyles);
      window.scrollTo(0, originalScroll);

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 10; // 10mm margin

      const imgWidth = pageWidth - (2 * margin);
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Calculate number of pages needed
      const totalPages = Math.ceil(imgHeight / (pageHeight - (2 * margin)));

      // Add content page by page
      for (let i = 0; i < totalPages; i++) {
        if (i > 0) {
          pdf.addPage();
        }

        // Calculate position and height for this page
        const position = i === 0 ? margin : 0;
        const sourceY = i * (pageHeight - (2 * margin));
        const heightLeft = imgHeight - sourceY;
        const pageImgHeight = Math.min(heightLeft, pageHeight - (2 * margin));

        // Add image for this page
        pdf.addImage(
          imgData,
          'PNG',
          margin,
          position,
          imgWidth,
          pageImgHeight,
          '',
          'NONE',
          0
        );
      }

      pdf.save('document.pdf');
    } catch (error) {
      console.error('Error exporting PDF:', error);
    }
  };

  const handleEditorChange = useCallback((_event: unknown, editor: Editor) => {
    const data = editor.getData();
    setEditorData(data);
    onChange?.(data);
  }, [onChange]);

  // Memoize the editor configuration to prevent unnecessary re-renders
  const editorConfig = useCallback((): CustomConfig => ({
    placeholder,
    toolbar: {
      items: [
        'heading',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        '|',
        'link',
        'bulletedList',
        'numberedList',
        '|',
        'outdent',
        'indent',
        '|',
        'alignment',
        'blockQuote',
        'insertTable',
        'undo',
        'redo',
        '|',
        'fontColor',
        'fontBackgroundColor'
      ],
      shouldNotGroupWhenFull: true
    },
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableProperties',
        'tableCellProperties'
      ]
    },
    link: {
      defaultProtocol: 'https://',
      decorators: {
        openInNewTab: {
          mode: 'manual',
          label: 'Open in a new tab',
          defaultValue: true,
          attributes: {
            target: '_blank',
            rel: 'noopener noreferrer'
          }
        }
      }
    },
    removePlugins: ['MediaEmbed', 'EasyImage'],
    ui: {
      viewportOffset: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      }
    }
  }), [placeholder]);

  return (
    <div
      className={`${styles['ckeditor-wrapper']} ${className}`}
      style={{
        '--ck-min-height': minHeight
      } as React.CSSProperties}
      ref={editorRef}
    >
      {allowDownload && (
        <div className="flex items-end justify-end p-1 bg-gray-50 border-b">
          <div className="flex gap-2">
            {/* <Button
              variant="outline"
              size="sm"
              onClick={handleExportDoc}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              DOC
            </Button> */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportPDF}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              PDF
            </Button>
            {/* <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              Print
            </Button> */}
          </div>
        </div>
      )}
      <CKEditor
        editor={ClassicEditor as Editor}
        config={editorConfig()}
        data={editorData}
        disabled={disabled}
        onReady={handleEditorReady}
        onChange={handleEditorChange}
      />
    </div>
  );
};

export default CustomCKEditor; 