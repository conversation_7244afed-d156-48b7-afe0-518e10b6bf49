.ckeditor-wrapper {
  /* Base styles */
  width: 100%;
  border-radius: 0.5rem;
  background-color: white;
  overflow: hidden;
  position: relative;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  --ck-border-radius: 0.5rem !important;
  --ck-color-base-border: #e2e8f0 !important;
  --ck-color-toolbar-border: #e2e8f0 !important;
  --ck-color-toolbar-background: #f8fafc !important;
  --ck-color-text: #1a1a1a !important;
  --ck-color-button-default-hover-background: #f1f5f9 !important;
  --ck-color-button-on-background: #e2e8f0 !important;
  --ck-color-button-on-hover-background: #cbd5e1 !important;
  --ck-color-button-action-background: #3b82f6 !important;
  --ck-spacing-unit: 0.5rem !important;
  --ck-font-size-base: 1rem !important;
  --ck-line-height-base: 1.5 !important;
}

/* Editor styles that must override CKEditor defaults */
.ckeditor-wrapper :global(.ck-editor__editable) {
  min-height: var(--ck-min-height, 200px) !important;
  max-height: 500px !important;
  background-color: white !important;
  color: #1a1a1a !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: #cbd5e1 #f1f5f9 !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  padding: 1rem !important;
  border: none !important;
  box-shadow: none !important;
}

/* Scrollbar styling */
.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar {
  width: 6px !important;
}

.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar-track {
  background: #f1f5f9 !important;
  border-radius: 3px !important;
}

.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar-thumb {
  background-color: #cbd5e1 !important;
  border-radius: 3px !important;
}

.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8 !important;
}

/* Focus state */
.ckeditor-wrapper :global(.ck-editor__editable.ck-focused) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Toolbar styling */
.ckeditor-wrapper :global(.ck.ck-toolbar) {
  border: none !important;
  border-bottom: 1px solid #e2e8f0 !important;
  background: #f8fafc !important;
  padding: 0.5rem !important;
  border-radius: 0 !important;
}

.ckeditor-wrapper :global(.ck.ck-toolbar__items) {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.25rem !important;
}

/* Button styling */
.ckeditor-wrapper :global(.ck.ck-button) {
  padding: 0.375rem !important;
  border-radius: 0.25rem !important;
  border: 1px solid transparent !important;
  background: transparent !important;
  color: #4b5563 !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  min-width: auto !important;
  min-height: auto !important;
}

.ckeditor-wrapper :global(.ck.ck-button:hover) {
  background: #f1f5f9 !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck.ck-button.ck-on) {
  background: #e2e8f0 !important;
  color: #1a1a1a !important;
  border-color: #cbd5e1 !important;
}

/* Dropdown styling */
.ckeditor-wrapper :global(.ck.ck-dropdown) {
  margin: 0 !important;
}

.ckeditor-wrapper :global(.ck.ck-dropdown__panel) {
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.ckeditor-wrapper :global(.ck.ck-list) {
  padding: 0.25rem !important;
}

.ckeditor-wrapper :global(.ck.ck-list__item) {
  border-radius: 0.25rem !important;
}

.ckeditor-wrapper :global(.ck.ck-list__item:hover) {
  background: #f1f5f9 !important;
}

/* Content styling */
.ckeditor-wrapper :global(.ck-content h1) {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  margin: 1.5rem 0 1rem !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content h2) {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 1.25rem 0 0.875rem !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content h3) {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 1rem 0 0.75rem !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content p) {
  margin: 0.75rem 0 !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content blockquote) {
  border-left: 3px solid #e2e8f0 !important;
  padding: 0.5rem 1rem !important;
  margin: 1rem 0 !important;
  color: #64748b !important;
  font-style: italic !important;
}

.ckeditor-wrapper :global(.ck-content pre) {
  background: #f8fafc !important;
  padding: 1rem !important;
  border-radius: 0.375rem !important;
  overflow-x: auto !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content code) {
  background: #f1f5f9 !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.875em !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content ul),
.ckeditor-wrapper :global(.ck-content ol) {
  padding-left: 1.5rem !important;
  margin: 0.75rem 0 !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content li) {
  margin: 0.25rem 0 !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck-content table) {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 1rem 0 !important;
  border: 2px solid #e2e8f0 !important;
}

.ckeditor-wrapper :global(.ck-content th),
.ckeditor-wrapper :global(.ck-content td) {
  border: 1px solid #e2e8f0 !important;
  padding: 0.75rem !important;
  color: #1a1a1a !important;
  vertical-align: top !important;
}

.ckeditor-wrapper :global(.ck-content th) {
  background: #f8fafc !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #e2e8f0 !important;
}

.ckeditor-wrapper :global(.ck-content tr:hover) {
  background-color: #f8fafc !important;
}

.ckeditor-wrapper :global(.ck-content a) {
  color: #2563eb !important;
  text-decoration: underline !important;
  transition: color 0.2s ease !important;
}

.ckeditor-wrapper :global(.ck-content a:hover) {
  color: #1d4ed8 !important;
  text-decoration: underline !important;
}

.ckeditor-wrapper :global(.ck-content a:visited) {
  color: #7c3aed !important;
}

/* Fixed header */
.ckeditor-wrapper :global(.ck.ck-editor__top) {
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  background: white !important;
}

/* Prevent layout shifts during resize */
.ckeditor-wrapper :global(.ck.ck-editor__main) {
  position: relative !important;
  z-index: 1 !important;
}

/* Responsive design */
@media (max-width: 640px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.25rem !important;
  }

  .ckeditor-wrapper :global(.ck-content) {
    font-size: 0.875rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    display: none !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button .ck-icon) {
    margin: 0 !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .ckeditor-wrapper :global(.ck.ck-button) {
    min-height: 36px !important;
    min-width: 36px !important;
  }
}

/* Enhanced toolbar styling */
.ckeditor-wrapper :global(.ck.ck-toolbar) {
  border: none !important;
  border-bottom: 1px solid #e2e8f0 !important;
  background: #f8fafc !important;
  padding: 0rem !important;
  width: 100% !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.5rem !important;
  justify-content: flex-start !important;
  min-height: 50px !important;
  position: relative;
  z-index: 2;
}

@media screen and (width >= 1025px) {
  .CKEditor-module__P61Zeq__ckeditor-wrapper .ck.ck-toolbar {
    padding: 0rem !important;
  }
}
.ckeditor-wrapper :global(.ck.ck-toolbar__items) {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.5rem !important;
  justify-content: flex-start !important;
  width: 100% !important;
}

/* Toolbar button styling */
.ckeditor-wrapper :global(.ck.ck-button) {
  padding: 0.5rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  transition: all 0.2s ease-in-out !important;
  margin: 0 !important;
}

.ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
  max-width: 100% !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Toolbar separator styling */
.ckeditor-wrapper :global(.ck.ck-toolbar__separator) {
  margin: 0 !important;
  width: 1px !important;
  background: #e2e8f0 !important;
}

/* Content area styling */
.ckeditor-wrapper :global(.ck-content) {
  font-size: 1rem;
  line-height: 1.5;
  padding: 1rem !important;
}

/* Responsive design - Mobile first approach */
@media screen and (max-width: 640px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.5rem !important;
    gap: 0.25rem !important;
    min-height: auto !important;
  }

  .ckeditor-wrapper :global(.ck.ck-toolbar__items) {
    gap: 0.25rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.375rem !important;
    font-size: 0.75rem !important;
  }

  /* Hide labels on very small screens */
  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    display: none !important;
  }

  /* Show only icons */
  .ckeditor-wrapper :global(.ck.ck-button .ck-icon) {
    margin: 0 !important;
  }

  .ckeditor-wrapper :global(.ck-content) {
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* Tablet screens */
@media screen and (min-width: 641px) and (max-width: 1024px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.625rem !important;
    gap: 0.375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-toolbar__items) {
    gap: 0.375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.4375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    max-width: 100px !important;
  }
}

/* Desktop screens */
@media screen and (min-width: 1025px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.75rem !important;
    gap: 0.5rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    /* padding: 0.5rem 0.75rem !important; */
    padding: 0 !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    max-width: 150px !important;
  }
}

/* Active and hover states */
.ckeditor-wrapper :global(.ck.ck-button:hover) {
  background: #e2e8f0 !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck.ck-button.ck-on) {
  background: #e2e8f0 !important;
  color: #1a1a1a !important;
}

/* Dropdown styling */
.ckeditor-wrapper :global(.ck.ck-dropdown) {
  margin: 0 !important;
}

.ckeditor-wrapper :global(.ck.ck-dropdown .ck-button.ck-dropdown__button) {
  padding-right: 1.5rem !important;
}

.ckeditor-wrapper :global(.ck.ck-dropdown .ck-dropdown__panel) {
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Style for placeholder text */
.ckeditor-wrapper :global(.ck-placeholder) {
  color: #9ca3af !important;
}

/* Ensure toolbar buttons are properly styled */
.ckeditor-wrapper :global(.ck-button) {
  color: #4b5563 !important;
}

.ckeditor-wrapper :global(.ck-button:hover) {
  background: #f3f4f6 !important;
}

.ckeditor-wrapper :global(.ck-button.ck-on) {
  background: #e5e7eb !important;
  color: #1a1a1a !important;
}

/* Responsive adjustments */
.ckeditor-wrapper :global(.ck-content) {
  font-size: 16px;
  line-height: 1.5;
}

/* Mobile-first responsive design */
@media screen and (max-width: 640px) {
  .ckeditor-wrapper :global(.ck-toolbar__items) {
    gap: 0.125rem;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.25rem !important;
  }

  .ckeditor-wrapper :global(.ck-content) {
    font-size: 14px;
  }

  .ckeditor-wrapper :global(.ck-editor__editable_inline) {
    padding: 0.5rem;
  }
}

/* Tablet and larger screens */
@media screen and (min-width: 641px) and (max-width: 1024px) {
  .ckeditor-wrapper :global(.ck-toolbar__items) {
    gap: 0.175rem;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.375rem !important;
  }
}

/* Handle images and tables responsively */
.ckeditor-wrapper :global(.ck-content img) {
  max-width: 100%;
  height: auto;
}

.ckeditor-wrapper :global(.ck-content table) {
  width: 100% !important;
  overflow-x: auto;
  display: block;
}

/* Ensure toolbar buttons are properly spaced and sized */
.ckeditor-wrapper :global(.ck.ck-toolbar > *) {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Improve touch targets on mobile */
@media (hover: none) and (pointer: coarse) {
  .ckeditor-wrapper :global(.ck.ck-button) {
    min-height: 36px;
    min-width: 36px;
  }
} 