'use client';

import { useEffect, useRef } from 'react';
import styles from './CKEditor.module.css';

interface CKViewerProps {
  /**
   * HTML content to display in the viewer
   */
  content: string;
  /**
   * Additional CSS class names
   */
  className?: string;
  /**
   * Minimum height of the viewer
   */
  minHeight?: string;
}

/**
 * An iframe-based viewer component for displaying HTML content with style isolation
 * @component
 */
const CKViewer: React.FC<CKViewerProps> = ({
  content = '',
  className = '',
  minHeight = '70px',
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    // Write content to iframe
    const doc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!doc) return;

    // Set up the iframe document with proper styling
    doc.open();
    doc.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {
              margin: 0;
              padding: 0;
              font-family: system-ui, -apple-system, sans-serif;
            }
            /* Add any default styles needed for content here */
          </style>
        </head>
        <body>${content}</body>
      </html>
    `);
    doc.close();

    // Adjust iframe height to content
    const resizeObserver = new ResizeObserver(() => {
      if (doc.body) {
        iframe.style.height = `${doc.body.scrollHeight}px`;
      }
    });

    if (doc.body) {
      resizeObserver.observe(doc.body);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [content]);

  return (
    <div 
      className={`${styles['ckeditor-wrapper']} ${className}`}
      style={{ 
        minHeight,
        width: '100%',
        backgroundColor: 'transparent',
        border: 'none'
      }}
    >
      <iframe
        ref={iframeRef}
        style={{
          border: 'none',
          width: '100%',
          minHeight,
          overflow: 'hidden'
        }}
        title="Content Viewer"
        sandbox="allow-same-origin"
      />
    </div>
  );
};

export default CKViewer; 