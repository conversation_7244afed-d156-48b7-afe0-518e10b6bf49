import { FC, useCallback, useEffect, useState } from "react";
import {
  MessageSquare,
  CheckSquare,
  ExternalLink,
  Bell,
  Loader2,
  Mail,
  FileText,
  AlertCircle,
  Clock,
  Upload,
  Shield,
  AlertTriangle,
  CheckCircle,
  User,
  Pencil,
  Tag,
  MessageSquareDot,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useInView } from "react-intersection-observer";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { setNotifications } from "@/store/slices/notificationSlice";
import { setTabsPanelCollapsed } from "@/store/slices/authSlice";
import {
  useNotificationsListQuery,
  useMarkNotificationReadMutation,
  useMarkNotificationUnreadMutation,
  useMarkAllNotificationsReadMutation,
  Notification,
  NotificationFilters,
} from "@/services/notificationService";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface NotificationItemProps { notification: Notification; onRead: (id: string) => void; onUnread?: (id: string) => void; }

const NotificationItem: FC<NotificationItemProps> = ({ notification, onRead, onUnread }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState(false);

  const getIcon = () => {
    switch (notification.notification_type.toLowerCase()) {
      case "incoming_message":
        return <Mail className="h-5 w-5 text-blue-500" />;
      case "case_update":
        return <FileText className="h-5 w-5 text-indigo-500" />;
      case "task_assigned":
      case "lead_task_created":
        return <CheckSquare className="h-5 w-5 text-green-500" />;
      case "task_updated":
      case "task_created":
      case "lead_task_status_changed":
        return <Pencil className="h-5 w-5 text-orange-500" />;
      case "task_status_changed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "task_tagged":
      case "note_tagged":
      case "lead_task_tagged":
        return <Tag className="h-5 w-5 text-purple-500" />;
      case "note_created":
      case "lead_note_created":
        return <FileText className="h-5 w-5 text-teal-500" />;
      case "case_status_changed":
      case "lead_status_changed":
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case "org_update":
      case "system_update":
      case "lead_update":
        return <Bell className="h-5 w-5 text-gray-500" />;
      case "deadline_reminder":
        return <Clock className="h-5 w-5 text-red-500" />;
      case "document_uploaded":
        return <Upload className="h-5 w-5 text-blue-500" />;
      case "permission_changed":
        return <Shield className="h-5 w-5 text-purple-500" />;
      case "conflict_detected":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case "conflict_resolved":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "lead_assigned":
        return <User className="h-5 w-5 text-blue-500" />;
      case "lead_communication_added":
        return <MessageSquare className="h-5 w-5 text-teal-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const handleClick = async (caseId: string) => {
    if (!notification.is_read) {
      await onRead(notification.id);
    }

    if (caseId) {
      dispatch(setTabsPanelCollapsed(false));
      router.push(`/dashboard/case-view/${caseId}?tab=phone`);
    } else {
      toast({
        title: 'Error',
        description: 'No caseId associated with this notification',
        variant: 'destructive',
      });
    }
  };

  // const handleExternalLinkClick = (caseId: string) => {
  //   if (!notification.is_read) {
  //     onRead(notification.id);
  //   }

  //   if (caseId) {
  //     dispatch(setTabsPanelCollapsed(false));
  //     router.push(`/dashboard/case-view/${caseId}?tab=phone`);
  //   } else {
  //     toast({
  //       title: 'Error',
  //       description: 'No caseId associated with this notification',
  //       variant: 'destructive',
  //     });
  //   }
  // };

  return (
    <div
      className={`flex items-start gap-3 p-3 hover:bg-gray-50 cursor-pointer rounded-lg ${!notification.is_read ? "bg-blue-50" : ""
        }`}
      onClick={() => handleClick(notification.case_id)}
    >
      <div className="flex-shrink-0">{getIcon()}</div>
      <div className="flex-grow">
        <div className="flex flex-col">
          {notification.title.includes(':') ? (
            <>
              <h4 className="text-sm font-medium text-gray-900">
                {notification.title.split(':')[0]}:
              </h4>
              <h6 className="text-sm font-medium text-gray-700 mt-0.5">
                {notification.title.split(':')[1]}
              </h6>
            </>
          ) : (
            <h4 className="text-sm font-medium text-gray-900">{notification.title}</h4>
          )}
        </div>
        <div className="relative">
          <p className={`text-sm text-gray-500 ${isExpanded ? '' : 'line-clamp-2'}`}>
            {notification.message}
          </p>
          {notification.message.length > 100 && (
            <button
              className="text-xs text-blue-600 hover:text-blue-800 mt-1 font-medium"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
            >
              {isExpanded ? 'Show Less' : 'Show More'}
            </button>
          )}
        </div>
        <span className="text-xs text-gray-400 mt-1">
          {new Date(notification.created_at).toLocaleString()}
        </span>
      </div>
      <div className="flex items-center gap-2">
        {notification.is_read && onUnread && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="cursor-pointer hover:bg-gray-100 p-1 rounded-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    onUnread(notification.id);
                  }}
                >
                  <MessageSquareDot className="h-4 w-4 text-gray-400" />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Mark as unread</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        {/* <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className="cursor-pointer hover:bg-gray-100 p-1 rounded-full"
                onClick={(e) => {
                  e.stopPropagation();
                  handleExternalLinkClick(notification.case_id);
                }}
              >
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Open case in new tab</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider> */}
      </div>
    </div>
  );
};

// Component for a single notification dropdown
interface NotificationDropdownProps { icon: React.ReactNode; badgeCount: number; title: string; notifications: Notification[]; isLoading: boolean; error: Error | null; hasNextPage: boolean | undefined; isFetchingNextPage: boolean; refElement: (node?: Element | null) => void; onMarkAsRead: (id: string) => void; onMarkAsUnread?: (id: string) => void; onMarkAllAsRead: () => void; }

const NotificationDropdown: FC<NotificationDropdownProps> = ({
  icon,
  badgeCount,
  title,
  notifications,
  isLoading,
  error,
  hasNextPage,
  isFetchingNextPage,
  refElement,
  onMarkAsRead,
  onMarkAsUnread,
  onMarkAllAsRead,
}: NotificationDropdownProps) => {
  const router = useRouter();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="w-10 h-10 p-0 rounded-full hover:bg-white/[0.12] relative"
        >
          {icon}
          {badgeCount > 0 && (
            <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-[10px] text-white flex items-center justify-center">
              {badgeCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[95vw] sm:w-[340px] md:w-[380px] bg-white rounded-[10px] border border-[rgba(1,1,1,0.08)] shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] p-2"
        align="end"
      >
        <div className="px-4 py-3 border-b border-gray-100">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {title}
              </h3>
              <Button
                variant="link"
                size="sm"
                className="text-xs text-blue-600 p-0 h-auto"
                onClick={() => router.push('/notifications')}
              >
                View All
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                className="text-xs sm:text-sm text-[#060216]-600 hover:text-[#060216]-700 px-2 sm:px-4"
                onClick={onMarkAllAsRead}
                disabled={badgeCount === 0}
              >
                Mark all as read
              </Button>
            </div>
          </div>
        </div>
        <div className="max-h-[calc(100vh-200px)] md:max-h-[400px] overflow-y-auto">
          {isLoading ? (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
            </div>
          ) : error ? (
            <div className="text-center py-4 text-red-500">
              Error loading notifications
            </div>
          ) : notifications.length > 0 ? (
            <>
              {notifications.map((notification) => (
                <DropdownMenuItem
                  key={notification.id}
                  className="w-full focus:bg-transparent"
                >
                  <NotificationItem
                    notification={notification}
                    onRead={onMarkAsRead}
                    onUnread={onMarkAsUnread}
                  />
                </DropdownMenuItem>
              ))}
              {hasNextPage && (
                <div
                  ref={refElement}
                  className="flex justify-center items-center py-4"
                >
                  {isFetchingNextPage ? (
                    <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
                  ) : (
                    <span className="text-sm text-gray-500">
                      Scroll for more
                    </span>
                  )}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 px-4">
              <Bell className="h-10 w-10 mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">No new notifications</p>
            </div>
          )}
        </div>
        <div className="px-4 py-3 border-t border-gray-100">
          <Button
            variant="default"
            className="w-full flex items-center justify-center gap-2"
            onClick={() => router.push('/notifications')}
          >
            <Bell className="h-4 w-4" />
            View All Notifications
          </Button>
          <div className="text-xs text-center text-gray-500 mt-2">
            Access advanced filters and search
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const NotificationsDropdown: FC = () => {
  const { ref, inView } = useInView();
  const { ref: messageRef, inView: messageInView } = useInView();
  const dispatch = useDispatch();

  // Get notification state from Redux
  // const { newNotification } = useSelector((state: RootState) => state.notification);
  // State to track which type of notifications we're viewing
  const [generalFilters] = useState<NotificationFilters>({
    is_archived: false,
  });

  const [messageFilters] = useState<NotificationFilters>({
    is_archived: false,
    notification_type: "incoming_message" //case_update
  });

  // Fetch all notifications
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useNotificationsListQuery(generalFilters);

  // Fetch message notifications
  const {
    data: messageData,
    fetchNextPage: fetchNextMessagePage,
    hasNextPage: hasNextMessagePage,
    isFetchingNextPage: isFetchingNextMessagePage,
    isLoading: isLoadingMessages,
    error: messageError,
  } = useNotificationsListQuery(messageFilters);

  // Mutations for marking notifications as read/unread
  const markAsRead = useMarkNotificationReadMutation();
  const markAsUnread = useMarkNotificationUnreadMutation();
  const markAllAsRead = useMarkAllNotificationsReadMutation();

  // Handle infinite scroll for general notifications
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Handle infinite scroll for message notifications
  useEffect(() => {
    if (messageInView && hasNextMessagePage && !isFetchingNextMessagePage) {
      fetchNextMessagePage();
    }
  }, [messageInView, hasNextMessagePage, isFetchingNextMessagePage, fetchNextMessagePage]);

  // Update Redux store with notifications data
  useEffect(() => {
    if (data?.pages && data.pages.length > 0) {
      const allNotifications = data.pages.flatMap(page => page.results);
      dispatch(setNotifications(allNotifications));
    }
  }, [data, dispatch]);

  // Handle marking a single notification as read/unread
  const handleMarkAsRead = useCallback((id: string) => {
    markAsRead.mutate(id);
  }, [markAsRead]);

  const handleMarkAsUnread = useCallback((id: string) => {
    markAsUnread.mutate(id);
  }, [markAsUnread]);

  // Handle marking all notifications as read
  const handleMarkAllAsRead = useCallback(() => {
    markAllAsRead.mutate();
  }, [markAllAsRead]);

  // Safely flatten all pages of notifications and filter out any invalid entries
  const allNotifications = data?.pages
    ?.flatMap((page) => page.results)
    .filter((notification): notification is Notification =>
      notification !== null &&
      notification !== undefined &&
      typeof notification === 'object' &&
      'id' in notification
    ) || [];

  const messageNotifications = messageData?.pages
    ?.flatMap((page) => page.results)
    .filter((notification): notification is Notification =>
      notification !== null &&
      notification !== undefined &&
      typeof notification === 'object' &&
      'id' in notification
    ) || [];

  // Use the unread_count from the API response instead of counting manually
  // The API now returns an unread_count field in the paginated response
  // This is more efficient than filtering the notifications array on the client

  // For general notifications
  const unreadGeneralCount = data?.pages[0]?.unread_count ?? 0;

  const unreadMessageCount = messageNotifications.filter(n => !n.is_read).length;

  return (
    <div className="flex items-center gap-2">
      {/* Message Notifications */}
      <NotificationDropdown
        icon={<MessageSquare className="text-white" size={20} />}
        badgeCount={unreadMessageCount}
        title="Messages"
        notifications={messageNotifications}
        isLoading={isLoadingMessages}
        error={messageError}
        hasNextPage={hasNextMessagePage}
        isFetchingNextPage={isFetchingNextMessagePage}
        refElement={messageRef}
        onMarkAsRead={handleMarkAsRead}
        onMarkAsUnread={handleMarkAsUnread}
        onMarkAllAsRead={handleMarkAllAsRead}
      />

      {/* General Notifications */}
      <NotificationDropdown
        icon={<Bell className="text-white" size={20} />}
        badgeCount={unreadGeneralCount}
        title="Notifications"
        notifications={allNotifications}
        isLoading={isLoading}
        error={error}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        refElement={ref}
        onMarkAsRead={handleMarkAsRead}
        onMarkAsUnread={handleMarkAsUnread}
        onMarkAllAsRead={handleMarkAllAsRead}
      />
    </div>
  );
};
