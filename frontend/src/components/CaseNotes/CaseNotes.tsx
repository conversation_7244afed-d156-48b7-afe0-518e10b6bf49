'use client';

import { useState, useEffect, useMemo } from 'react';
import { useNotesListQuery, useCreateNoteMutation, useDeleteNoteMutation, useUpdateNoteMutation, useSyncNoteMutation } from '@/services/case-management/noteTaskService';
import caseManagementService from '@/services/caseManagementService';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Plus, Search, User2, Pencil, Share2, Loader2, MoreVertical, ChevronUp, ChevronDown, FilterIcon, FilterX, Repeat } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { UserDetails } from '@/type/caseManagement';
import { useInView } from 'react-intersection-observer';
import { CaseNote, ListResponse, UserDetail } from '@/type/case-management/noteTaskTypes';
import { MultiSelect } from "@/components/ui/multi-select";
import { useCaseNoteTags } from '@/services/organizationService';
import { CustomDateInput } from "@/components/ui/custom-date-input";
import CustomCKEditor from "@/components/ckeditor/CKEditor";
import { useToast } from "@/hooks/use-toast";
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import { NotesTasksSectionTypes } from '@/type/case-management/noteTaskTypes';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { cn } from "@/lib/utils";
import CKViewer from '../ckeditor/CKViewer';
import SyncCaseDialog from '@/components/CaseOverview/components/SyncCaseDialog';

interface CaseNotesProps {
    caseId: string;
    noteFor?: NotesTasksSectionTypes;
    defendantId?: string;
}

/**
 * CaseNotes component displays and manages case notes with custom scrollbars for a modern UI.
 * @component
 * @param {CaseNotesProps} props - The props for CaseNotes.
 * @returns {JSX.Element}
 */
export function CaseNotes({ caseId, noteFor = 'Case', defendantId }: CaseNotesProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const [hasAppliedFilters, setHasAppliedFilters] = useState(false);
    const [tempFilters, setTempFilters] = useState({
        date: '',
        creator: '',
        taggedUsers: [] as number[],
        tagIds: [] as number[],
        search: ''
    });
    
    const [showSyncDialog, setShowSyncDialog] = useState(false);
    const [title, setTitle] = useState('');
    const [content, setContent] = useState<string>('');
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedDate, setSelectedDate] = useState<string>('');
    const [selectedCreator, setSelectedCreator] = useState<string>();
    const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
    const [selectedTags, setSelectedTags] = useState<number[]>([]);
    const [availableUsers, setAvailableUsers] = useState<UserDetails[]>([]);
    const [editingNote, setEditingNote] = useState<{
        id: number;
        title: string;
        content: string;
        tagged_user_ids: number[];
        tag_ids: number[];
    } | null>(null);
    const [deletingNoteId, setDeletingNoteId] = useState<string>('');
    const [linkedCases, setLinkedCases] = useState<string[]>([]);
    const [filterTags, setFilterTags] = useState<number[]>([]);
    const [filterTaggedUsers, setFilterTaggedUsers] = useState<number[]>([]);
    const [filterTagIds, setFilterTagIds] = useState<number[]>([]);
    const { toast } = useToast();
    const [selectedNoteForSync, setSelectedNoteForSync] = useState<number | null>(null);
    const [selectedCaseIds, setSelectedCaseIds] = useState<string[]>([]);

    const {
        data: notesData,
        isLoading,
        hasNextPage,
        fetchNextPage,
        isFetchingNextPage
    } = useNotesListQuery(caseId, noteFor, defendantId);

    const { data: linkedCasesData } = useLinkedCasesQuery(caseId);

    const { ref: loadMoreRef, inView } = useInView({
        threshold: 0,
        rootMargin: '100px',
        skip: isLoading || !hasNextPage || isFetchingNextPage
    });

    const createNoteMutation = useCreateNoteMutation(caseId, noteFor);
    const updateNoteMutation = useUpdateNoteMutation(
        caseId,
        editingNote?.id?.toString() || '',
        noteFor
    );
    const deleteNoteMutation = useDeleteNoteMutation(caseId, deletingNoteId);
    const syncNoteMutation = useSyncNoteMutation(caseId);
    const { data: noteTags } = useCaseNoteTags();

    const allLinkedCases = [
        ...(linkedCasesData?.direct_cases || []),
        ...(linkedCasesData?.indirect_cases || [])
    ];

    // Helper function to format user name
    const formatUserName = (user?: UserDetails) => {
        if (!user) return '';
        return user.name?.trim() || user.email;
    };

    // Fetch available users
    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const users = await caseManagementService.getAvailableUsers(caseId);
                setAvailableUsers(users);
            } catch (error) {
                console.error('Failed to fetch users:', error);
            }
        };
        fetchUsers();
    }, [caseId]);

    // Extract linked case IDs
    useEffect(() => {
        if (linkedCasesData?.direct_cases && linkedCasesData.direct_cases.length > 0) {
            const linkedCaseIds = linkedCasesData.direct_cases.map(linkedCase => linkedCase.id);
            setLinkedCases(linkedCaseIds);
        }
    }, [linkedCasesData]);

    const handleSubmit = async () => {
        if (!title.trim()) return;

        try {
            const createdNote = await createNoteMutation.mutateAsync({
                case: caseId,
                title: title,
                content: content,
                tagged_user_ids: selectedUsers,
                tag_ids: selectedTags,
                note_for: noteFor,
                ...(noteFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
            });

            if (showSyncDialog && selectedCaseIds.length > 0) {
                await syncNoteMutation.mutateAsync({
                    source_note_id: createdNote.id,
                    target_case_ids: selectedCaseIds
                });
            }

            setTitle('');
            setContent('');
            setSelectedUsers([]);
            setSelectedTags([]);
            setIsExpanded(false);
            setShowSyncDialog(false);
            setSelectedCaseIds([]);

            toast({
                title: "Success",
                description: "Note created successfully",
            });
        } catch (error) {
            console.error('Failed to create note:', error);
            toast({
                title: "Error",
                description: "Failed to create note",
                variant: "destructive",
            });
        }
    };

    const handleDelete = async (noteId: number) => {
        try {
            setDeletingNoteId(noteId.toString());
            await deleteNoteMutation.mutateAsync();
        } catch (error) {
            console.error('Failed to delete note:', error);
        } finally {
            setDeletingNoteId('');
        }
    };

    const handleEdit = async () => {
        if (!editingNote || updateNoteMutation.status === 'pending') return;

        try {
            const updatedNote = await updateNoteMutation.mutateAsync({
                case: caseId,
                title: editingNote.title,
                content: editingNote.content,
                tagged_user_ids: editingNote.tagged_user_ids,
                tag_ids: editingNote.tag_ids,
                note_for: noteFor,
                ...(noteFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
            });

            if (showSyncDialog && selectedCaseIds.length > 0) {
                await syncNoteMutation.mutateAsync({
                    source_note_id: updatedNote.id,
                    target_case_ids: selectedCaseIds
                });
            }

            setEditingNote(null);
            setShowSyncDialog(false);
            setSelectedCaseIds([]);

            toast({
                title: "Success",
                description: "Note updated successfully",
            });
        } catch (error) {
            console.error('Failed to update note:', error);
            toast({
                title: "Error",
                description: "Failed to update note",
                variant: "destructive",
            });
        }
    };

    const handleEditClick = (note: CaseNote) => {
        // If there's a pending update mutation, don't allow editing
        if (updateNoteMutation.status === 'pending') return;

        // If the note is not editable, don't allow editing
        if (!note.is_editable) return;

        // If already editing this note, cancel editing
        if (editingNote?.id === note.id) {
            setEditingNote(null);
            return;
        }

        // Start editing new note
        setEditingNote({
            id: note.id,
            title: note.title,
            content: note.content,
            tagged_user_ids: note.tagged_users.map((u: UserDetail) => u.id),
            tag_ids: note.tags?.map(t => t.id) || []
        });
    };

    // Load more when scrolled to bottom
    useEffect(() => {
        if (!inView || !hasNextPage || isFetchingNextPage || isLoading) return;
        fetchNextPage();
    }, [inView, hasNextPage, isFetchingNextPage, isLoading, fetchNextPage]);

    const handleDateChange = (value: string) => {
        setSelectedDate(value);
    };

    const allNotes = notesData?.pages.flatMap((page: ListResponse<CaseNote>) => page.results) || [];
    const filteredNotes = useMemo(() => {
        return allNotes.filter(note => {
            const matchesSearch = searchQuery
                ? note.content.toLowerCase().includes(searchQuery.toLowerCase())
                : true;
            const matchesDate = selectedDate
                ? format(new Date(note.created_at), 'dd-MM-yyyy') === selectedDate
                : true;
            const matchesCreator = selectedCreator
                ? note.created_by.id.toString() === selectedCreator
                : true;
            const matchesTags = filterTags.length > 0
                ? filterTags.every(tagId => note.tags?.some(t => t.id === tagId))
                : true;
            const matchesTaggedUsers = filterTaggedUsers.length > 0
                ? filterTaggedUsers.every(userId => note.tagged_users.some(u => u.id === userId))
                : true;
            const matchesTagIds = filterTagIds.length > 0
                ? filterTagIds.every(tagId => note.tags?.some(t => t.id === tagId))
                : true;
            return matchesSearch && matchesDate && matchesCreator && matchesTags && matchesTaggedUsers && matchesTagIds;
        });
    }, [allNotes, searchQuery, selectedDate, selectedCreator, filterTags, filterTaggedUsers, filterTagIds]);

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            if (!selectedCaseIds.length) {
                toast({
                    title: "Error",
                    description: "No cases selected for syncing",
                    variant: "destructive",
                });
                return;
            }

            if (selectedNoteForSync) {
                await syncNoteMutation.mutateAsync({
                    source_note_id: selectedNoteForSync,
                    target_case_ids: selectedCaseIds
                });

                setShowSyncDialog(false);
                setSelectedNoteForSync(null);
                toast({
                    title: "Success",
                    description: "Note synchronized successfully",
                });
            } else if (editingNote) {
                // Handle sync during edit
                const updatedNote = await updateNoteMutation.mutateAsync({
                    case: caseId,
                    title: editingNote.title,
                    content: editingNote.content,
                    tagged_user_ids: editingNote.tagged_user_ids,
                    tag_ids: editingNote.tag_ids,
                    note_for: noteFor,
                    ...(noteFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
                });

                await syncNoteMutation.mutateAsync({
                    source_note_id: updatedNote.id,
                    target_case_ids: selectedCaseIds
                });

                setEditingNote(null);
                setShowSyncDialog(false);
                toast({
                    title: "Success",
                    description: "Note updated and synchronized successfully",
                });
            } else {
                // Handle sync during create
                const createdNote = await createNoteMutation.mutateAsync({
                    case: caseId,
                    title: title,
                    content: content,
                    tagged_user_ids: selectedUsers,
                    tag_ids: selectedTags,
                    note_for: noteFor,
                    ...(noteFor === 'Negotiation' && defendantId ? { defendant_id: defendantId } : {})
                });

                await syncNoteMutation.mutateAsync({
                    source_note_id: createdNote.id,
                    target_case_ids: selectedCaseIds
                });

                setTitle('');
                setContent('');
                setSelectedUsers([]);
                setSelectedTags([]);
                setIsExpanded(false);
                setShowSyncDialog(false);
                toast({
                    title: "Success",
                    description: "Note created and synchronized successfully",
                });
            }
        } catch (error) {
            console.error("Error syncing note:", error);
            toast({
                title: "Error",
                description: "Failed to sync note",
                variant: "destructive",
            });
        }
    };

    // Add this new function to handle filter application
    const handleApplyFilters = () => {
        setSelectedDate(tempFilters.date);
        setSelectedCreator(tempFilters.creator);
        setFilterTaggedUsers(tempFilters.taggedUsers);
        setFilterTagIds(tempFilters.tagIds);
        setSearchQuery(tempFilters.search);
        setShowFilters(false);
        setHasAppliedFilters(
            !!tempFilters.date ||
            !!tempFilters.creator ||
            tempFilters.taggedUsers.length > 0 ||
            tempFilters.tagIds.length > 0 ||
            !!tempFilters.search
        );
    };

    // Add this function to clear all filters
    const handleClearFilters = () => {
        // Clear temporary filters
        setTempFilters({
            date: '',
            creator: '',
            taggedUsers: [],
            tagIds: [],
            search: ''
        });
        // Clear applied filters
        setSelectedDate('');
        setSelectedCreator(undefined);
        setFilterTaggedUsers([]);
        setFilterTagIds([]);
        setSearchQuery('');
        setHasAppliedFilters(false);
        // Close the filter section
        setShowFilters(false);
    };

    if (isLoading) {
        return <div className="flex items-center justify-center h-full">Loading notes...</div>;
    }

    return (
        <div className="h-full flex flex-col min-h-0">
            {/* Fixed Header Section */}
            <div className={cn(
                "p-4 border-b bg-white gap-2",
                showFilters || isExpanded ? "flex-none" : "flex"
            )}>
                {/* Filter Toggle Button */}
                <div className={cn(
                    "flex",
                    showFilters || isExpanded ? "mb-2" : ""
                )}>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                            if (hasAppliedFilters) {
                                handleClearFilters();
                            } else {
                                setShowFilters(!showFilters);
                            }
                        }}
                        className="h-12 w-12 flex items-center gap-4 hover:bg-gray-50 transition-colors"
                    >
                        {hasAppliedFilters ? (
                            <FilterX className='h-4 w-4 text-blue-500' />
                        ) : (
                            <FilterIcon className='h-4 w-4' />
                        )}
                    </Button>
                </div>

                {/* Search and Filter Controls */}
                {showFilters && (
                    <div className={cn(
                        "transition-all duration-300 ease-in-out overflow-hidden",
                        showFilters ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                    )}>
                        <div className="flex flex-col items-stretch gap-2 mb-4">
                            <div className="flex flex-wrap gap-2">
                                <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
                                    <CustomDateInput
                                        value={tempFilters.date}
                                        onChange={(value) => setTempFilters(prev => ({ ...prev, date: value }))}
                                        maxDate={new Date()}
                                        className="w-full"
                                        error={false}
                                        placeholder="Filter by date"
                                    />
                                </div>
                                <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
                                    <Select
                                        value={tempFilters.creator}
                                        onValueChange={(value) => setTempFilters(prev => ({ ...prev, creator: value }))}
                                    >
                                        <SelectTrigger className="w-full bg-white rounded-full h-10 flex items-center px-3 text-sm border border-gray-300 shadow-sm">
                                            <div className="flex items-center gap-2">
                                                <User2 className="h-4 w-4" />
                                                {tempFilters.creator ? (
                                                    formatUserName(availableUsers.find(u => u.id.toString() === tempFilters.creator))
                                                ) : (
                                                    <span className="text-muted-foreground">Created By</span>
                                                )}
                                            </div>
                                        </SelectTrigger>
                                        <SelectContent searchable>
                                            <div className="flex flex-col">
                                                <div className="max-h-[200px] overflow-y-auto">
                                                    {availableUsers.map(user => (
                                                        <SelectItem
                                                            key={user.id}
                                                            value={user.id.toString()}
                                                            className="flex items-center gap-2"
                                                        >
                                                            {formatUserName(user)}
                                                        </SelectItem>
                                                    ))}
                                                </div>
                                                {tempFilters.creator && (
                                                    <Button
                                                        variant="ghost"
                                                        className="mt-2"
                                                        onClick={() => setTempFilters(prev => ({ ...prev, creator: '' }))}
                                                    >
                                                        Clear Selection
                                                    </Button>
                                                )}
                                            </div>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="w-full">
                                    <div className="flex items-center gap-2">
                                        <MultiSelect
                                            options={availableUsers.map(user => ({
                                                label: formatUserName(user),
                                                value: user.id.toString()
                                            }))}
                                            value={tempFilters.taggedUsers.map(id => id.toString())}
                                            onValueChange={values => setTempFilters(prev => ({
                                                ...prev,
                                                taggedUsers: values.map(v => parseInt(v))
                                            }))}
                                            placeholder={tempFilters.taggedUsers.length > 0 ? `${tempFilters.taggedUsers.length} users selected` : "Tagged Users"}
                                            className="w-full rounded-full"
                                        />
                                    </div>
                                </div>
                                <div className="w-full">
                                    <div className="w-full">
                                        <MultiSelect
                                            options={noteTags?.map(tag => ({
                                                label: tag.name,
                                                value: tag.id.toString()
                                            })) || []}
                                            value={tempFilters.tagIds.map(id => id.toString())}
                                            onValueChange={values => setTempFilters(prev => ({
                                                ...prev,
                                                tagIds: values.map(v => parseInt(v))
                                            }))}
                                            placeholder={tempFilters.tagIds.length > 0 ? `${tempFilters.tagIds.length} tags selected` : "Tags"}
                                            className="w-full flex justify-between rounded-full"
                                        />
                                    </div>
                                </div>
                                <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px] relative">
                                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                        <Search className="h-4 w-4 text-muted-foreground" />
                                    </div>
                                    <Input
                                        placeholder="Search notes..."
                                        value={tempFilters.search}
                                        onChange={(e) => setTempFilters(prev => ({ ...prev, search: e.target.value }))}
                                        className="pl-9 rounded-full w-full"
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end gap-2 mt-4">
                                <Button
                                    variant="outline"
                                    onClick={handleClearFilters}
                                    className="flex items-center gap-2"
                                >
                                    <FilterX className="h-4 w-4" />
                                    Clear Filters
                                </Button>
                                <Button
                                    onClick={handleApplyFilters}
                                    className="flex items-center gap-2"
                                >
                                    Apply Filters
                                </Button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Add Note Button/Form */}
                {!isExpanded ? (
                    <button
                        onClick={() => setIsExpanded(true)}
                        className="w-full flex items-center gap-2 p-3 border rounded-lg text-muted-foreground hover:bg-gray-50 transition-colors"
                    >
                        <Plus className="h-4 w-4" />
                        <span>Take a note...</span>
                    </button>
                ) : (
                    <div className="border rounded-lg p-4 space-y-4 bg-white max-h-[50vh] overflow-y-auto">
                        <Input
                            placeholder="Title"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                        />
                        <CustomCKEditor
                            initialValue={content}
                            onChange={(value: string) => {
                                setContent(value);
                            }}
                            placeholder="Take a note..."
                            minHeight="100px"
                            className="min-h-[100px] resize-none bg-gray-50"
                        />
                        <div className="flex items-center gap-2">
                            <MultiSelect
                                options={availableUsers.map(user => ({
                                    label: formatUserName(user),
                                    value: user.id.toString()
                                }))}
                                value={selectedUsers.map(id => id.toString())}
                                onValueChange={(values) => {
                                    setSelectedUsers(values.map(v => parseInt(v)));
                                }}
                                placeholder="Tag users"
                                className="w-[200px]"
                            />
                        </div>
                        <div className="flex items-center gap-2">
                            <MultiSelect
                                options={noteTags?.map(tag => ({
                                    label: tag.name,
                                    value: tag.id.toString()
                                })) || []}
                                onValueChange={(values) => setSelectedTags(values.map(v => parseInt(v)))}
                                placeholder="Select tags"
                                defaultValue={[]}
                            />
                            {selectedTags.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                    {selectedTags.map(tagId => {
                                        const tag = noteTags?.find(t => t.id === tagId);
                                        return tag && (
                                            <Badge
                                                key={tagId}
                                                variant="outline"
                                                className="flex items-center gap-1"
                                            >
                                                {tag.name}
                                                <button
                                                    onClick={e => {
                                                        e.preventDefault();
                                                        setSelectedTags(prev => prev.filter(id => id !== tagId));
                                                    }}
                                                    className="ml-1 hover:text-red-500"
                                                >
                                                    ×
                                                </button>
                                            </Badge>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsExpanded(false)}>
                                Cancel
                            </Button>
                            {linkedCasesData && (linkedCasesData.direct_cases.length > 0 || linkedCasesData.indirect_cases.length > 0) && (
                                <Button
                                    variant="outline"
                                    onClick={() => setShowSyncDialog(true)}
                                    className="rounded-full"
                                >
                                    Sync Linked Cases
                                </Button>
                            )}
                            <Button
                                onClick={handleSubmit}
                                disabled={!title.trim() || createNoteMutation.isPending}
                            >
                                {createNoteMutation.isPending ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Creating...
                                    </>
                                ) : (
                                    "Done"
                                )}
                            </Button>
                        </div>
                    </div>
                )}
            </div>

            {/* Scrollable Content Section */}
            <div className="flex-1 overflow-y-auto min-h-0 bg-gray-50 custom-scrollbar">
                <div className="p-4 space-y-4">
                    {filteredNotes.map((note) => (
                        <div
                            key={note.id}
                            className={cn(
                                "p-2 group border rounded-lg hover:shadow-sm bg-white transition-shadow relative",
                                !note.is_editable ? "max-w-[600px]" : ""
                            )}
                        >
                            <div className="flex items-start">
                                <div
                                    style={{ overflow: 'hidden' }}
                                    className={cn("flex-1 p-2")}
                                >
                                    {editingNote?.id === note.id ? (
                                        <div className="space-y-4">
                                            <Input
                                                value={editingNote.title}
                                                onChange={(e) => setEditingNote(prev => prev ? {
                                                    ...prev,
                                                    title: e.target.value
                                                } : null)}
                                                placeholder="Title"
                                                disabled={updateNoteMutation.status === 'pending'}
                                            />
                                            <CustomCKEditor
                                                initialValue={editingNote.content}
                                                onChange={(value: string) => {
                                                    if (value !== editingNote.content) {
                                                        setEditingNote(prev => prev ? {
                                                            ...prev,
                                                            content: value
                                                        } : null);
                                                    }
                                                }}
                                                placeholder="Edit note..."
                                                className="min-h-[100px] resize-none bg-gray-50"
                                                disabled={updateNoteMutation.status === 'pending'}
                                            />
                                            <div className="flex items-center gap-2">
                                                <MultiSelect
                                                    options={availableUsers.map(user => ({
                                                        label: formatUserName(user),
                                                        value: user.id.toString()
                                                    }))}
                                                    value={editingNote.tagged_user_ids.map(id => id.toString())}
                                                    onValueChange={(values) => {
                                                        if (updateNoteMutation.status === 'pending') return;
                                                        const newTagIds = values.map(v => parseInt(v));
                                                        setEditingNote(prev => prev ? {
                                                            ...prev,
                                                            tagged_user_ids: newTagIds
                                                        } : null);
                                                    }}
                                                    placeholder="Tag users"
                                                    className="w-[200px]"
                                                />
                                                {editingNote.tagged_user_ids.length > 0 && (
                                                    <div className="flex flex-wrap gap-1">
                                                        {editingNote.tagged_user_ids.map(userId => {
                                                            const user = availableUsers.find(u => u.id === userId);
                                                            return user && (
                                                                <Badge
                                                                    key={userId}
                                                                    variant="secondary"
                                                                    className="flex items-center gap-1"
                                                                >
                                                                    {formatUserName(user)}
                                                                    <button
                                                                        onClick={(e) => {
                                                                            e.preventDefault();
                                                                            if (updateNoteMutation.status === 'pending') return;
                                                                            setEditingNote(prev => prev ? {
                                                                                ...prev,
                                                                                tagged_user_ids: prev.tagged_user_ids.filter(id => id !== userId)
                                                                            } : null);
                                                                        }}
                                                                        className="ml-1 hover:text-red-500"
                                                                    >
                                                                        ×
                                                                    </button>
                                                                </Badge>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <MultiSelect
                                                    options={noteTags?.map(tag => ({
                                                        label: tag.name,
                                                        value: tag.id.toString()
                                                    })) || []}
                                                    onValueChange={(values) => {
                                                        if (updateNoteMutation.status === 'pending') return;
                                                        const newTagIds = values.map(v => parseInt(v));
                                                        setEditingNote(prev => prev ? {
                                                            ...prev,
                                                            tag_ids: newTagIds
                                                        } : null);
                                                    }}
                                                    value={editingNote?.tag_ids?.map(id => id.toString()) || []}
                                                    placeholder="Select tags"
                                                    disabled={updateNoteMutation.status === 'pending'}
                                                />
                                                {editingNote?.tag_ids.length > 0 && (
                                                    <div className="flex flex-wrap gap-1">
                                                        {editingNote.tag_ids.map(tagId => {
                                                            const tag = noteTags?.find(t => t.id === tagId);
                                                            return tag && (
                                                                <Badge
                                                                    key={tagId}
                                                                    variant="outline"
                                                                    className="flex items-center gap-1"
                                                                >
                                                                    {tag.name}
                                                                    <button
                                                                        onClick={e => {
                                                                            e.preventDefault();
                                                                            if (updateNoteMutation.status === 'pending') return;
                                                                            setEditingNote(prev => prev ? {
                                                                                ...prev,
                                                                                tag_ids: prev.tag_ids.filter(id => id !== tagId)
                                                                            } : null);
                                                                        }}
                                                                        className="ml-1 hover:text-red-500"
                                                                    >
                                                                        ×
                                                                    </button>
                                                                </Badge>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex justify-end gap-2">
                                                <Button variant="outline" onClick={() => setEditingNote(null)}>
                                                    Cancel
                                                </Button>
                                                {linkedCasesData && (linkedCasesData.direct_cases.length > 0 || linkedCasesData.indirect_cases.length > 0) && (
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => setShowSyncDialog(true)}
                                                        className="rounded-full"
                                                    >
                                                        Sync Linked Cases
                                                    </Button>
                                                )}
                                                <Button
                                                    onClick={handleEdit}
                                                    disabled={updateNoteMutation.status === 'pending'}
                                                >
                                                    {updateNoteMutation.status === 'pending' ? 'Saving...' : 'Save'}
                                                </Button>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            <h3 className="text-lg font-semibold mb-2">{note.title}</h3>
                                            <div
                                                className="overflow-x-auto overflow-y-auto custom-scrollbar"
                                                style={{
                                                    width: '100%',
                                                    maxWidth: '100%',
                                                    maxHeight: '200px',
                                                    wordBreak: 'break-word',
                                                    overflowWrap: 'break-word',
                                                    background: 'inherit',
                                                }}
                                            >
                                                <div style={{ minWidth: '100%' }}>
                                                    <CKViewer content={note.content} />
                                                </div>
                                            </div>
                                            <div className="mt-2 text-sm">
                                                From {note.created_by.name}, Created on {formatDateForDisplay(note.created_at)}
                                            </div>
                                            {note.tagged_users.length > 0 && (
                                                <div className="mt-2 flex gap-1">
                                                    {note.tagged_users.map((user: UserDetail) => (
                                                        <Badge key={user.id} variant="secondary">
                                                            {user.name}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                            {note.tags && note.tags.length > 0 && (
                                                <div className="mt-2 flex gap-1">
                                                    {note.tags.map((tag) => (
                                                        <Badge key={tag.id} variant="outline">
                                                            {tag.name}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                                <div className="flex gap-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="invisible group-hover:visible absolute right-4 top-4 h-8 w-8 z-10"
                                            >
                                                <MoreVertical className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {/* {allLinkedCases.length > 0 && (
                                                <DropdownMenuItem
                                                    className="gap-2"
                                                    onClick={() => {
                                                        setSelectedNoteForSync(note.id);
                                                        setShowSyncDialog(true);
                                                    }}
                                                    disabled={syncNoteMutation.isPending}
                                                >
                                                    {syncNoteMutation.isPending ? (
                                                        <>
                                                            <span className="loading loading-spinner loading-xs mr-2"></span>
                                                            Syncing...
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Repeat className="h-4 w-4" />
                                                            <span>Sync Note</span>
                                                        </>
                                                    )}
                                                </DropdownMenuItem>
                                            )} */}
                                            {note.is_editable && (
                                                <DropdownMenuItem
                                                    onClick={() => handleEditClick(note)}
                                                    className="gap-2"
                                                >
                                                    <Pencil className="h-4 w-4" />
                                                    <span>Edit</span>
                                                </DropdownMenuItem>
                                            )}
                                            <DropdownMenuItem
                                                onClick={() => handleDelete(note.id)}
                                                className="gap-2 text-red-600"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                                <span>Delete</span>
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                        </div>
                    ))}

                    {/* Load more trigger */}
                    <div ref={loadMoreRef} className="h-4">
                        {isFetchingNextPage && (
                            <div className="text-center text-sm text-gray-500">
                                Loading more notes...
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Add SyncCaseDialog at the end of the component */}
            <SyncCaseDialog
                caseId={caseId}
                syncType="notes"
                isOpen={showSyncDialog}
                onClose={() => {
                    setShowSyncDialog(false);
                    setSelectedNoteForSync(null);
                }}
                onSync={handleSync}
                isSyncing={syncNoteMutation.isPending}
            />
        </div>
    );
} 