"use client"

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input";
import {
    Trash2,
    Plus,
    Search,
    User2,
    CheckCircle2,
    Circle,
    Clock,
    AlertCircle,
    Pencil,
    CalendarIcon,
} from "lucide-react"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
} from "@/components/ui/select"
import { format, parse } from "date-fns"
import { Badge } from "@/components/ui/badge"
import { useInView } from "react-intersection-observer"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import {
    Task,
    TaskStatus,
    TaskStatusConfig,
    UserDetail,
} from "@/type/lead/leadTaskTypes"
import { useLeadTasksQuery, useCreateLeadTaskMutation, useDeleteLeadTaskMutation, useUpdateLeadTaskMutation } from "@/services/leads/leadTaskService";
import { useOrganizationUsersQuery } from "@/services/orgAPIs";
import RichTextEditor from "@/components/ui/RichTextEditor"
import RichTextViewer from "@/components/ui/RichTextViewer"
import CustomCKEditor from "@/components/ckeditor/CKEditor";

interface LeadTasksProps {
    leadId: string;
}

const StatusIcons: Record<TaskStatus, React.ElementType> = {
    [TaskStatus.PENDING]: Circle,
    [TaskStatus.IN_PROGRESS]: Clock,
    [TaskStatus.COMPLETED]: CheckCircle2,
    [TaskStatus.REOPENED]: AlertCircle,
};

export function LeadTasks({ leadId }: LeadTasksProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedDate, setSelectedDate] = useState<string>("");
    const [selectedAssignee, setSelectedAssignee] = useState<string>();
    const [selectedDueDate, setSelectedDueDate] = useState<string>("");
    const [selectedStatus, setSelectedStatus] = useState<TaskStatus>(TaskStatus.PENDING);
    const [availableUsers, setAvailableUsers] = useState<UserDetail[]>([]);
    const { data: users } = useOrganizationUsersQuery();
    const { ref: loadMoreRef } = useInView();
    const [editingTask, setEditingTask] = useState<{
        id: number;
        title: string;
        description: string;
        assigned_to?: string;
        tagged_user_ids: number[];
        due_date?: string;
    } | null>(null);
    const [selectedTaggedUsers, setSelectedTaggedUsers] = useState<number[]>([]);
    const [filterAssignee, setFilterAssignee] = useState<string>();

    const {
        data: tasksData,
        isLoading,
    } = useLeadTasksQuery();
    const createTaskMutation = useCreateLeadTaskMutation(leadId);
    const deleteTaskMutation = useDeleteLeadTaskMutation(leadId);
    const updateTaskMutation = useUpdateLeadTaskMutation(leadId);

    useEffect(() => {
        setAvailableUsers(users || []);
    }, [users]);

    // Fetch available users
    // useEffect(() => {
    //     const fetchUsers = async () => {
    //         try {
    //             // const users = await caseManagementService.getAvailableUsers(leadId);
    //             setAvailableUsers(users);
    //         } catch (error) {
    //             console.error("Failed to fetch users:", error);
    //         }
    //     };
    //     fetchUsers();
    // }, [leadId]);

    const handleSubmit = async () => {
        if (!title.trim()) return;

        try {
            await createTaskMutation.mutateAsync({
                lead: leadId,
                title: title,
                description: description || "",
                status: selectedStatus,
                assigned_to_id: selectedAssignee ? parseInt(selectedAssignee) : undefined,
                tagged_user_ids: selectedTaggedUsers,
                due_date: selectedDueDate,
            });
            setTitle("");
            setDescription("");
            setSelectedDueDate("");
            setSelectedAssignee(undefined);
            setSelectedTaggedUsers([]);
            setSelectedStatus(TaskStatus.PENDING);
            setIsExpanded(false);
        } catch (error) {
            console.error("Failed to create task:", error);
        }
    };

    const handleDelete = async (taskId: number) => {
        try {
            await deleteTaskMutation.mutateAsync(taskId.toString());
        } catch (error) {
            console.error("Failed to delete task:", error);
        }
    };

    const handleStatusChange = async (taskId: number, currentStatus: TaskStatus) => {
        const nextStatus = TaskStatusConfig[currentStatus].nextStatus[0];
        if (!nextStatus) return;

        try {
            await updateTaskMutation.mutateAsync({
                taskId: taskId.toString(),
                data: {
                    lead: leadId,
                    status: nextStatus,
                    title: tasksData?.results.find((t: Task) => t.id === taskId)?.title || "",
                }
            });
        } catch (error) {
            console.error("Failed to update task status:", error);
        }
    };

    const handleEdit = async () => {
        if (!editingTask) return;

        try {
            await updateTaskMutation.mutateAsync({
                taskId: editingTask.id.toString(),
                data: {
                    lead: leadId,
                    title: editingTask.title,
                    description: editingTask.description,
                    assigned_to_id: editingTask.assigned_to ? parseInt(editingTask.assigned_to) : undefined,
                    tagged_user_ids: editingTask.tagged_user_ids,
                    due_date: editingTask.due_date,
                },
            });
            setEditingTask(null);
        } catch (error) {
            console.error("Failed to update task:", error);
        }
    };

    const handleDateChange = (value: string) => {
        setSelectedDate(value);
    };

    const handleDueDateChange = (value: string) => {
        if (value) {
            const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
            const formattedDate = format(parsedDate, 'yyyy-MM-dd');
            setSelectedDueDate(formattedDate);
        } else {
            setSelectedDueDate('');
        }
    };

    const handleEditingDueDateChange = (value: string) => {
        if (value) {
            const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
            const formattedDate = format(parsedDate, 'yyyy-MM-dd');
            setEditingTask(prev => prev ? { ...prev, due_date: formattedDate } : null);
        } else {
            setEditingTask(prev => prev ? { ...prev, due_date: undefined } : null);
        }
    };

    const allTasks = tasksData?.results || [];
    const filteredTasks = allTasks.filter((task: Task) => {
        const matchesSearch = searchQuery
            ? task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            task.description?.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        const matchesDate = selectedDate
            ? task.due_date && format(new Date(task.due_date), 'dd-MM-yyyy') === selectedDate
            : true;
        const matchesAssignee = filterAssignee
            ? task.assigned_to?.id.toString() === filterAssignee
            : true;
        return matchesSearch && matchesDate && matchesAssignee;
    });

    // Helper function to format user name
    const formatUserName = (user?: UserDetail) => {
        if (!user) return "Unassigned";
        return user.first_name && user.last_name
            ? `${user.first_name} ${user.last_name}`
            : user.email;
    };

    const StatusIcon = (status: TaskStatus) => {
        const Icon = StatusIcons[status];
        return <Icon className="h-4 w-4" />;
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-full">
                Loading tasks...
            </div>
        );
    }

    return (
        <div className="flex h-full flex-col">
            <div className="border-b bg-white p-4 space-y-4">
                <div className="flex flex-col items-stretch gap-2">
                    <div className="flex flex-wrap gap-2">
                        <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
                            <CustomDateInput
                                value={selectedDate}
                                onChange={handleDateChange}
                                maxDate={new Date()}
                                className="w-full"
                                error={false}
                                placeholder="Filter by date"
                            />
                        </div>

                        <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px]">
                            <Select value={filterAssignee} onValueChange={setFilterAssignee}>
                                <SelectTrigger className="w-full bg-white rounded-full">
                                    <div className="flex items-center gap-2">
                                        <User2 className="h-4 w-4" />
                                        {filterAssignee ? (
                                            formatUserName(
                                                availableUsers.find(
                                                    (u) => u.id.toString() === filterAssignee
                                                )
                                            )
                                        ) : (
                                            <span className="text-muted-foreground">
                                                Assignee
                                            </span>
                                        )}
                                    </div>
                                </SelectTrigger>
                                <SelectContent>
                                    <div className="flex flex-col">
                                        <div className="max-h-[200px] overflow-y-auto">
                                            {availableUsers.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {formatUserName(user)}
                                                </SelectItem>
                                            ))}
                                        </div>
                                        {filterAssignee && (
                                            <Button
                                                variant="ghost"
                                                className="mt-2"
                                                onClick={() => setFilterAssignee(undefined)}
                                            >
                                                Clear Selection
                                            </Button>
                                        )}
                                    </div>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grow basis-full md:basis-[calc(33.33%-0.5rem)] min-w-[200px] relative">
                            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                <Search className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <Input
                                placeholder="Search tasks..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-9 rounded-full w-full"
                            />
                        </div>
                    </div>
                </div>

                <div className="space-y-2">
                    {!isExpanded ? (
                        <div
                            className="flex items-center gap-2 p-2 border rounded-lg cursor-pointer hover:bg-gray-50"
                            onClick={() => setIsExpanded(true)}
                        >
                            <Plus className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">Add tasks</span>
                        </div>
                    ) : (
                        <div className="space-y-4 p-4 border rounded-lg">
                            <div className="max-h-[60vh] overflow-y-auto pr-2 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <Input
                                    placeholder="Title"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    className="rounded-lg"
                                />
                                <div className="border rounded-lg overflow-hidden">
                                    <CustomCKEditor
                                        initialValue={description}
                                        onChange={(value: string) => setDescription(value)}
                                        placeholder="Add description"
                                        className="min-h-[100px]"
                                    />
                                </div>
                                <div className="flex flex-wrap items-start gap-2">
                                    <Select
                                        value={selectedAssignee}
                                        onValueChange={setSelectedAssignee}
                                    >
                                        <SelectTrigger className="w-[200px] bg-white rounded-lg">
                                            <div className="flex items-center gap-2">
                                                <User2 className="h-4 w-4" />
                                                {selectedAssignee ? (
                                                    formatUserName(
                                                        availableUsers.find(
                                                            (u) => u.id.toString() === selectedAssignee
                                                        )
                                                    )
                                                ) : (
                                                    <span className="text-muted-foreground">Assign to</span>
                                                )}
                                            </div>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <div className="max-h-[200px] overflow-y-auto">
                                                {availableUsers.map((user) => (
                                                    <SelectItem
                                                        key={user.id}
                                                        value={user.id.toString()}
                                                    >
                                                        <div className="flex items-center gap-2">
                                                            {selectedAssignee === user.id.toString() && "✓"}
                                                            {formatUserName(user)}
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </div>
                                        </SelectContent>
                                    </Select>

                                    <Select
                                        onValueChange={(value) => {
                                            const userId = parseInt(value);
                                            setSelectedTaggedUsers((prev) =>
                                                prev.includes(userId)
                                                    ? prev.filter((id) => id !== userId)
                                                    : [...prev, userId]
                                            );
                                        }}
                                    >
                                        <SelectTrigger className="w-[200px] bg-white rounded-lg">
                                            <div className="flex items-center gap-2">
                                                <User2 className="h-4 w-4" />
                                                <span>Tag users</span>
                                            </div>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {availableUsers.map((user) => (
                                                <SelectItem
                                                    key={user.id}
                                                    value={user.id.toString()}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        {selectedTaggedUsers.includes(user.id) && "✓"}
                                                        {formatUserName(user)}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>

                                    <CustomDateInput
                                        value={selectedDueDate ? format(new Date(selectedDueDate), 'dd-MM-yyyy') : ''}
                                        onChange={handleDueDateChange}
                                        maxDate={new Date()}
                                        className="w-[200px]"
                                        error={false}
                                        placeholder="Due date"
                                    />
                                </div>

                                {selectedTaggedUsers.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                        {selectedTaggedUsers.map((userId) => {
                                            const user = availableUsers.find((u) => u.id === userId);
                                            return (
                                                user && (
                                                    <Badge
                                                        key={userId}
                                                        variant="secondary"
                                                        className="flex items-center gap-1 rounded-full"
                                                    >
                                                        {formatUserName(user)}
                                                        <button
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                setSelectedTaggedUsers((prev) =>
                                                                    prev.filter((id) => id !== userId)
                                                                );
                                                            }}
                                                            className="ml-1 hover:text-red-500"
                                                        >
                                                            ×
                                                        </button>
                                                    </Badge>
                                                )
                                            );
                                        })}
                                    </div>
                                )}

                                <div className="flex justify-end gap-2 pt-2 border-t">
                                    <Button
                                        variant="outline"
                                        onClick={() => setIsExpanded(false)}
                                        className="rounded-full"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        onClick={handleSubmit}
                                        disabled={!title.trim()}
                                        className="rounded-full"
                                    >
                                        Done
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <ScrollArea className="flex-1">
                <div className="p-4 space-y-4 pb-20">
                    {filteredTasks.map((task) => (
                        <div
                            key={task.id}
                            className="group border rounded-lg p-4 hover:shadow-sm bg-white"
                        >
                            <div className="flex items-start gap-4">
                                <button
                                    onClick={() => handleStatusChange(task.id, task.status)}
                                    className="mt-1"
                                >
                                    {StatusIcon(task.status)}
                                </button>
                                <div className="flex-1">
                                    {editingTask?.id === task.id ? (
                                        <div className="space-y-4">
                                            <Input
                                                value={editingTask.title}
                                                onChange={(e) =>
                                                    setEditingTask((prev) =>
                                                        prev
                                                            ? {
                                                                ...prev,
                                                                title: e.target.value,
                                                            }
                                                            : null
                                                    )
                                                }
                                                className="font-medium rounded-lg"
                                                placeholder="Task title"
                                            />
                                            <div className="border rounded-lg overflow-hidden">
                                                <RichTextEditor
                                                    value={editingTask.description}
                                                    onChange={(value) =>
                                                        setEditingTask((prev) =>
                                                            prev
                                                                ? {
                                                                    ...prev,
                                                                    description: value,
                                                                }
                                                                : null
                                                        )
                                                    }
                                                    placeholder="Task description"
                                                    className="min-h-[100px]"
                                                />
                                            </div>
                                            <div className="flex flex-wrap items-start gap-2">
                                                <Select
                                                    value={editingTask.assigned_to}
                                                    onValueChange={(value) =>
                                                        setEditingTask((prev) =>
                                                            prev
                                                                ? {
                                                                    ...prev,
                                                                    assigned_to: value,
                                                                }
                                                                : null
                                                        )
                                                    }
                                                >
                                                    <SelectTrigger className="w-[200px] bg-white rounded-lg">
                                                        <div className="flex items-center gap-2">
                                                            <User2 className="h-4 w-4" />
                                                            {editingTask.assigned_to ? (
                                                                formatUserName(
                                                                    availableUsers.find(
                                                                        (u) => u.id.toString() === editingTask.assigned_to
                                                                    )
                                                                )
                                                            ) : (
                                                                <span className="text-muted-foreground">
                                                                    Assign to
                                                                </span>
                                                            )}
                                                        </div>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <div className="max-h-[200px] overflow-y-auto">
                                                            {availableUsers.map((user) => (
                                                                <SelectItem
                                                                    key={user.id}
                                                                    value={user.id.toString()}
                                                                >
                                                                    <div className="flex items-center gap-2">
                                                                        {editingTask.assigned_to === user.id.toString() &&
                                                                            "✓"}
                                                                        {formatUserName(user)}
                                                                    </div>
                                                                </SelectItem>
                                                            ))}
                                                        </div>
                                                    </SelectContent>
                                                </Select>

                                                <Select
                                                    onValueChange={(value) => {
                                                        const userId = parseInt(value);
                                                        setEditingTask((prev) =>
                                                            prev
                                                                ? {
                                                                    ...prev,
                                                                    tagged_user_ids:
                                                                        prev.tagged_user_ids.includes(userId)
                                                                            ? prev.tagged_user_ids.filter(
                                                                                (id) => id !== userId
                                                                            )
                                                                            : [...prev.tagged_user_ids, userId],
                                                                }
                                                                : null
                                                        );
                                                    }}
                                                >
                                                    <SelectTrigger className="w-[200px] bg-white rounded-lg">
                                                        <div className="flex items-center gap-2">
                                                            <User2 className="h-4 w-4" />
                                                            <span>Tag users</span>
                                                        </div>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {availableUsers.map((user) => (
                                                            <SelectItem
                                                                key={user.id}
                                                                value={user.id.toString()}
                                                            >
                                                                <div className="flex items-center gap-2">
                                                                    {editingTask.tagged_user_ids.includes(
                                                                        user.id
                                                                    ) && "✓"}
                                                                    {formatUserName(user)}
                                                                </div>
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>

                                                <CustomDateInput
                                                    value={editingTask.due_date ? format(new Date(editingTask.due_date), 'dd-MM-yyyy') : ''}
                                                    onChange={handleEditingDueDateChange}
                                                    maxDate={new Date()}
                                                    className="w-[200px]"
                                                    error={false}
                                                    placeholder="Due date"
                                                />
                                            </div>

                                            {editingTask.tagged_user_ids.length > 0 && (
                                                <div className="flex flex-wrap gap-1">
                                                    {editingTask.tagged_user_ids.map((userId) => {
                                                        const user = availableUsers.find(
                                                            (u) => u.id === userId
                                                        );
                                                        return (
                                                            user && (
                                                                <Badge
                                                                    key={userId}
                                                                    variant="secondary"
                                                                    className="flex items-center gap-1 rounded-full"
                                                                >
                                                                    {formatUserName(user)}
                                                                    <button
                                                                        onClick={(e) => {
                                                                            e.preventDefault();
                                                                            setEditingTask((prev) =>
                                                                                prev
                                                                                    ? {
                                                                                        ...prev,
                                                                                        tagged_user_ids:
                                                                                            prev.tagged_user_ids.filter(
                                                                                                (id) => id !== userId
                                                                                            ),
                                                                                    }
                                                                                    : null
                                                                            );
                                                                        }}
                                                                        className="ml-1 hover:text-red-500"
                                                                    >
                                                                        ×
                                                                    </button>
                                                                </Badge>
                                                            )
                                                        );
                                                    })}
                                                </div>
                                            )}

                                            <div className="flex justify-end gap-2">
                                                <Button
                                                    variant="outline"
                                                    onClick={() => setEditingTask(null)}
                                                    className="rounded-full"
                                                >
                                                    Cancel
                                                </Button>
                                                <Button onClick={handleEdit} className="rounded-full">
                                                    Save
                                                </Button>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            <div className="flex items-center gap-2">
                                                <div className="font-medium">{task.title}</div>
                                                <Badge variant="secondary">
                                                    {TaskStatusConfig[task.status].label}
                                                </Badge>
                                            </div>
                                            {task.description && (
                                                <div className="mt-1 text-sm text-gray-600">
                                                    <RichTextViewer data={task.description} />
                                                </div>
                                            )}
                                            <div className="mt-2 flex items-start gap-4 text-sm text-gray-500 flex-col">
                                                {task.assigned_to && (
                                                    <div className="flex items-center gap-1">
                                                        <User2 className="h-4 w-4" />
                                                        {formatUserName(task.assigned_to)}
                                                    </div>
                                                )}
                                                {task.due_date && (
                                                    <div className="flex items-center gap-1">
                                                        <CalendarIcon className="h-4 w-4" />
                                                        {format(new Date(task.due_date), "dd MMM yyyy")}
                                                    </div>
                                                )}
                                            </div>
                                            {task.tagged_users.length > 0 && (
                                                <div className="mt-2 flex gap-1">
                                                    {task.tagged_users.map((user) => (
                                                        <Badge key={user.id} variant="secondary">
                                                            {formatUserName(user)}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() =>
                                            setEditingTask({
                                                id: task.id,
                                                title: task.title,
                                                description: task.description || "",
                                                assigned_to: task.assigned_to?.id.toString(),
                                                tagged_user_ids: task.tagged_users.map((u) => u.id),
                                                due_date: task.due_date ? format(new Date(task.due_date), 'yyyy-MM-dd') : undefined,
                                            })
                                        }
                                        className="opacity-0 group-hover:opacity-100"
                                    >
                                        <Pencil className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleDelete(task.id)}
                                        className="opacity-0 group-hover:opacity-100"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    {/* Load more trigger */}
                    <div ref={loadMoreRef} className="h-4">
                        {/* {isFetchingNextPage && (
                            <div className="text-center text-sm text-gray-500">
                                Loading more tasks...
                            </div>
                        )} */}
                    </div>
                </div>
            </ScrollArea>
        </div>
    );
}