'use client';

import { useState, useCallback, useEffect } from 'react';
import { useOrganizationUsers, useRemoveOrganizationUser, useUserTags, useUpdateUserTags, useTerminateOrganizationUser, useUpdateOrganizationUser, OrganizationUser } from '@/services/organizationService';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { UserX, Edit2, Check, X, UserCheck, UserMinus } from "lucide-react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MultiSelect } from "@/components/ui/multi-select";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import debounce from 'lodash/debounce';

interface EditingUser {
    id: number;
    first_name: string;
    last_name: string;
    role: string;
    is_active: boolean;
}

export function UsersList() {
    const [search, setSearch] = useState('');
    const [userToRemove, setUserToRemove] = useState<number | null>(null);
    const [transferToUserId, setTransferToUserId] = useState<number | null>(null);
    const [editingUser, setEditingUser] = useState<EditingUser | null>(null);
    const { data: users = [], isLoading } = useOrganizationUsers();
    const { data: tags = [] } = useUserTags();
    const removeUser = useRemoveOrganizationUser();
    const terminateUser = useTerminateOrganizationUser();
    const updateUserTags = useUpdateUserTags();
    const updateOrganizationUser = useUpdateOrganizationUser();

    // Create a debounced version of the update function
    const debouncedUpdateTags = useCallback(
        debounce(async (userId: number, selectedTags: string[]) => {
            try {
                await updateUserTags.mutateAsync({
                    userId,
                    data: { tag_ids: selectedTags.map(id => parseInt(id)) }
                });
            } catch (error) {
                console.error('Failed to update user tags:', error);
            }
        }, 2000), // Wait 2 seconds after last change before making API call
        [updateUserTags]
    );

    // Clean up the debounced function on component unmount
    useEffect(() => {
        return () => {
            debouncedUpdateTags.cancel();
        };
    }, [debouncedUpdateTags]);

    // Replace the existing handleUpdateTags with this version
    const handleUpdateTags = (userId: number, selectedTags: string[]) => {
        debouncedUpdateTags(userId, selectedTags);
    };

    // Handle starting edit mode for a user
    const handleEditUser = (user: OrganizationUser) => {
        setEditingUser({
            id: user.id,
            first_name: user.first_name || '',
            last_name: user.last_name || '',
            role: user.role,
            is_active: user.is_active
        });
    };

    // Handle saving user changes
    const handleSaveUser = async () => {
        if (!editingUser) return;

        try {
            await updateOrganizationUser.mutateAsync({
                userId: editingUser.id,
                data: {
                    first_name: editingUser.first_name,
                    last_name: editingUser.last_name,
                    role: editingUser.role as OrganizationUser['role'],
                    is_active: editingUser.is_active
                }
            });
            setEditingUser(null);
        } catch (error) {
            console.error('Failed to update user:', error);
        }
    };

    // Handle canceling edit mode
    const handleCancelEdit = () => {
        setEditingUser(null);
    };

    // Handle input changes during editing
    const handleEditChange = (field: keyof EditingUser, value: string | boolean) => {
        if (editingUser) {
            setEditingUser({
                ...editingUser,
                [field]: value
            });
        }
    };

    // Handle toggling user active status
    const handleToggleActiveStatus = async (user: OrganizationUser) => {
        try {
            await updateOrganizationUser.mutateAsync({
                userId: user.id,
                data: {
                    is_active: !user.is_active
                }
            });
        } catch (error) {
            console.error('Failed to toggle user status:', error);
        }
    };

    const handleRemoveUser = async () => {
        if (userToRemove === null) return;

        try {
            if (transferToUserId) {
                await terminateUser.mutateAsync({
                    user_id: userToRemove,
                    transfer_to_user_id: transferToUserId
                });
            } else {
                await removeUser.mutateAsync(userToRemove);
            }
            setUserToRemove(null);
            setTransferToUserId(null);
        } catch (error) {
            console.error('Failed to remove user:', error);
        }
    };

    const filteredUsers = users.filter(user => {
        const searchLower = search.toLowerCase();
        const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim();
        return fullName.toLowerCase().includes(searchLower) ||
            user.email.toLowerCase().includes(searchLower);
    });

    // Transform tags for MultiSelect options
    const tagOptions = tags.map(tag => ({
        label: tag.name,
        value: tag.id?.toString() || '',
    }));

    // Role options for the select dropdown
    const roleOptions = [
        { value: 'admin', label: 'Admin' },
        { value: 'general', label: 'General' },
        { value: 'attorney', label: 'Attorney' },
        { value: 'paralegal', label: 'Paralegal' },
        { value: 'legal_assistant', label: 'Legal Assistant' },
        { value: 'managing_partner', label: 'Managing Partner' },
        { value: 'case_manager', label: 'Case Manager' },
        { value: 'intake_user', label: 'Intake User' },
        { value: 'marketing_user', label: 'Marketing User' },
    ];

    if (isLoading) {
        return <div>Loading users...</div>;
    }

    if (users.length === 0) {
        return (
            <div className="text-center py-6 text-gray-500">
                No users found
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center space-x-2">
                <Input
                    placeholder="Search users..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            <div className="bg-white rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>System Role</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Organization Role</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredUsers.map((user) => {
                            const isEditing = editingUser?.id === user.id;
                            return (
                                <TableRow key={user.id}>
                                    <TableCell className="font-medium">
                                        {isEditing ? (
                                            <div className="flex space-x-2">
                                                <Input
                                                    value={editingUser.first_name}
                                                    onChange={(e) => handleEditChange('first_name', e.target.value)}
                                                    placeholder="First name"
                                                    className="w-24"
                                                />
                                                <Input
                                                    value={editingUser.last_name}
                                                    onChange={(e) => handleEditChange('last_name', e.target.value)}
                                                    placeholder="Last name"
                                                    className="w-24"
                                                />
                                            </div>
                                        ) : (
                                            user.first_name || user.last_name
                                                ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                                                : '—'
                                        )}
                                    </TableCell>
                                    <TableCell>{user.email}</TableCell>
                                    <TableCell>
                                        {isEditing ? (
                                            <Select
                                                value={editingUser.role}
                                                onValueChange={(value) => handleEditChange('role', value)}
                                            >
                                                <SelectTrigger className="w-40">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {roleOptions.map(option => (
                                                        <SelectItem key={option.value} value={option.value}>
                                                            {option.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${user.role === 'admin'
                                                    ? 'bg-blue-100 text-[#060216]-700'
                                                    : 'bg-gray-100 text-gray-700'
                                                }`}>
                                                {roleOptions.find(r => r.value === user.role)?.label || user.role}
                                            </span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        {isEditing ? (
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    checked={editingUser.is_active}
                                                    onCheckedChange={(checked) => handleEditChange('is_active', checked)}
                                                />
                                                <span className="text-sm">
                                                    {editingUser.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </div>
                                        ) : (
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${user.is_active
                                                    ? 'bg-green-100 text-green-700'
                                                    : 'bg-red-100 text-red-700'
                                                }`}>
                                                {user.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <MultiSelect
                                            options={tagOptions}
                                            defaultValue={user.tags.map(tag => tag.id?.toString() || '')}
                                            onValueChange={(values) => handleUpdateTags(user.id, values)}
                                            placeholder="Select roles"
                                            className="min-w-[200px]"
                                        />
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <div className="flex items-center justify-end space-x-2">
                                            {isEditing ? (
                                                <>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={handleSaveUser}
                                                        className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                                    >
                                                        <Check className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={handleCancelEdit}
                                                        className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                                                    >
                                                        <X className="w-4 h-4" />
                                                    </Button>
                                                </>
                                            ) : (
                                                <>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleEditUser(user)}
                                                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                                    >
                                                        <Edit2 className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleToggleActiveStatus(user)}
                                                        className={user.is_active
                                                            ? "text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                                                            : "text-green-600 hover:text-green-700 hover:bg-green-50"
                                                        }
                                                        title={user.is_active ? "Deactivate user" : "Activate user"}
                                                    >
                                                        {user.is_active ? <UserMinus className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
                                                    </Button>
                                                    {user.role !== 'admin' && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => setUserToRemove(user.id)}
                                                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                                        >
                                                            <UserX className="w-4 h-4" />
                                                        </Button>
                                                    )}
                                                </>
                                            )}
                                        </div>
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                    </TableBody>
                </Table>
            </div>

            <AlertDialog open={userToRemove !== null} onOpenChange={() => setUserToRemove(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Remove User</AlertDialogTitle>
                        <AlertDialogDescription className="space-y-4">
                            <p>Are you sure you want to remove this user from the organization?
                                This action cannot be undone.</p>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">
                                    Transfer user&apos;s data to (optional):
                                </label>
                                <Select
                                    value={transferToUserId?.toString() || ''}
                                    onValueChange={(value) => setTransferToUserId(Number(value))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a user" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {users
                                            .filter(u => u.id !== userToRemove)
                                            .map(user => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.first_name || user.last_name
                                                        ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                                                        : user.email}
                                                </SelectItem>
                                            ))
                                        }
                                    </SelectContent>
                                </Select>
                            </div>
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={() => {
                            setUserToRemove(null);
                            setTransferToUserId(null);
                        }}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleRemoveUser}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            Remove
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}