import React, { FC } from "react";
import { FileText } from "lucide-react";
import AddEditProperDamage from "./AddEditProperDamage";
import { InsuranceCompany } from "@/type/case-management/orgTypes";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import <PERSON><PERSON><PERSON>ie<PERSON> from "@/components/ckeditor/CKViewer";

interface PropertyDamageProps {
  insurance_company: InsuranceCompany | null;
  registered_owner: string | null;
  estimate: string | null;
  vehicle_make: string | null;
  color: string | null;
  license_plate: string | null;
  vehicle_vin: string | null;
  damage: string | null;
  auto_body_shop: string | null;
  final: string | null;
  vehicle_model: string | null;
  vehicle_year: number | null;
  mileage: number | null;
  note?: string | null;
  frame_damage: boolean;
  total_loss: boolean;
  damage_description: string | null;
  caseId: string;
}

enum DamageType {
  MAJOR = "Major",
  MINOR = "Minor",
  MODERATE = "Moderate",
}

const PropertyDamage: FC<PropertyDamageProps> = ({
  insurance_company,
  registered_owner,
  estimate,
  vehicle_make,
  color,
  license_plate,
  vehicle_vin,
  damage,
  auto_body_shop,
  final,
  vehicle_model,
  vehicle_year,
  mileage,
  note,
  frame_damage,
  total_loss,
  damage_description,
  caseId,
}) => {
  const defaultValues = {
    insurance: insurance_company?.id?.toString() ?? "",
    damage: (damage ?? "Minor") as DamageType,
    frameDamage: frame_damage ?? false,
    totalLoss: total_loss ?? false,
    registeredOwner: registered_owner ?? "",
    autoBodyShop: auto_body_shop ?? "",
    estimate: estimate ?? "",
    final: final ?? "",
    make: vehicle_make ?? "",
    model: vehicle_model ?? "",
    color: color ?? "",
    year: vehicle_year ? vehicle_year.toString() : "",
    plateNumber: license_plate ?? "",
    vinNumber: vehicle_vin ?? "",
    mileage: mileage ? mileage.toString() : "",
    note: note ?? "",
    damageDescription: damage_description ?? "",
  } as const;

  const mainFields = [
    {
      label: "insurance",
      value: insurance_company?.name ?? "—"
    },
    {
      label: "registered owner",
      value: registered_owner ?? "—"
    },
    {
      label: "estimate",
      value: estimate ? `$${estimate}` : "—"
    },
    {
      label: "make",
      value: vehicle_make ?? "—"
    },
    {
      label: "color",
      value: color ?? "—"
    },
    {
      label: "plate #",
      value: license_plate ?? "—"
    },
    {
      label: "vin #",
      value: vehicle_vin ?? "—"
    },
    {
      label: "damage",
      value: damage ?? "—"
    },
    {
      label: "auto body shop",
      value: auto_body_shop ?? "—"
    },
    {
      label: "final",
      value: final ? `$${final}` : "—"
    },
    {
      label: "model",
      value: vehicle_model ?? "—"
    },
    {
      label: "year",
      value: vehicle_year ? vehicle_year.toString() : "—"
    },
    {
      label: "mileage",
      value: mileage ? mileage.toString() : "—"
    },
    // ...(note ? [{
    //   label: "note",
    //   value: <CKViewer content={note} />
    // }] : []),
    // ...(damage_description ? [{
    //   label: "damage description",
    //   value: <CKViewer content={damage_description} />
    // }] : [])
  ];

  return (
    <div className="w-full">
      <div className="flex items-center  justify-between gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
        <div className="flex items-center gap-2">
          <FileText className="w-5 h-5 text-[#060216]" />
          <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
            Property Damage
          </h2>
        </div>
        <AddEditProperDamage isEditing={true} defaultValues={defaultValues} caseId={caseId} />
      </div>

      <div className="p-4">
      <InfoFieldGroup
        fields={mainFields}
        // columns={4}
        // gap="gap-4"
      />
      {/* Notes Section */}
        <div className="bg-[#F7F7F7] p-2 rounded-lg border border-[#00000014] mt-4">
          <div className="flex flex-col px-2 py-2">
            <span className="text-[#06021699] text-xs leading-[21px] font-medium font-Manrope mb-[2px]">
              Notes
            </span>
            <div className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
              {note ? <CKViewer content={note} /> : "—"}
            </div>
          </div>
        </div>
      {/* Damage Description Section */}
        <div className="bg-[#F7F7F7] p-2 rounded-lg border border-[#00000014] mt-4">
          <div className="flex flex-col px-2 py-2">
            <span className="text-[#06021699] text-xs leading-[21px] font-medium font-Manrope mb-[2px]">
              Damage Description
            </span>
            <div className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
              {damage_description ? <CKViewer content={damage_description} /> : "—"}
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default PropertyDamage;
