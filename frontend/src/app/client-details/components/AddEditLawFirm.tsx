import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON>alog<PERSON>eader,
    <PERSON>alog<PERSON><PERSON><PERSON>,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useCreateLawFirmMutation, useUpdateLawFirmMutation } from '@/services/orgAPIs';
import { LawFirmCreateRequest, LawFirmResponse, LawFirmUpdateRequest } from '@/type/case-management/orgTypes';
import { USStatesLabels } from '@/constants/commont';
import { useState, useEffect } from "react";
import { PhoneNumberInput } from "@/components/ui/phone-number-input"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import CustomCKEditor from "@/components/ckeditor/CKEditor"


const websiteRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9][-a-zA-Z0-9@:%._+~#=]{0,256}\.[a-z]{2,}([-a-zA-Z0-9@:%_+.~#?&//=]*)?$/i;

const formSchema = z.object({
    officeName: z.string().min(1, "Office name is required"),
    payee: z.string().nullable().optional(),
    street1: z.string().nullable().optional(),
    street2: z.string().nullable().optional(),
    city: z.string().nullable().optional(),
    state: z.string().nullable().optional(),
    zip: z.string().nullable()
        .refine((val) => !val || /^\d{5}(-\d{4})?$/.test(val), {
            message: "ZIP code must be in format: 12345 or 12345-6789"
        })
        .optional(),
    phone: z.string().nullable()
        .refine((val) => !val || /^\d{3}-\d{3}-\d{4}$/.test(val), {
            message: "Phone must be in format: ************"
        })
        .optional(),
    phoneExt: z.string().nullable()
        .refine((val) => !val || /^\d{0,6}$/.test(val), {
            message: "Extension must be up to 6 digits"
        })
        .optional(),
    cell: z.string().nullable()
        .refine((val) => !val || /^\d{3}-\d{3}-\d{4}$/.test(val), {
            message: "Cell phone must be in format: ************"
        })
        .optional(),
    email: z.string().nullable()
        .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
            message: "Email must be valid (e.g., <EMAIL>)"
        })
        .optional(),
    fax: z.string().nullable()
        .refine((val) => !val || /^\d{3}-\d{3}-\d{4}$/.test(val), {
            message: "Fax must be in format: ************"
        })
        .optional(),
    taxId: z.string().nullable()
        .refine((val) => !val || /^\d{2}-\d{7}$|^\d{3}-\d{2}-\d{4}$/.test(val), {
            message: "Tax ID must be in format: 12-3456789 or SSN: ***********"
        })
        .optional(),
    website: z.string()
        .refine((val) => !val || websiteRegex.test(val), {
            message: "Please enter a valid website URL (e.g., example.com or https://example.com)"
        })
        .optional()
        .or(z.literal("")),
    note: z.string().nullable()
        .optional(),
})

interface AddEditLawFirm {
    isEdit?: boolean;
    children: React.ReactNode;
    selectedLawFirm?: LawFirmResponse;
    onSuccess?: (newLawFirmId?: number) => void;
    onOpenChange?: (open: boolean) => void;
}

export function AddEditLawFirm({
    isEdit = false,
    children,
    selectedLawFirm,
    onSuccess,
    onOpenChange
}: AddEditLawFirm) {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty && form.formState.isSubmitSuccessful === false) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(newOpen);
            }
        }
    };

    const handleClose = () => {
        form.reset();
        setOpen(false);
        onOpenChange?.(false);
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            officeName: "",
            payee: "",
            street1: "",
            street2: "",
            city: "",
            state: "",
            zip: "",
            phone: "",
            phoneExt: "",
            cell: "",
            email: "",
            fax: "",
            taxId: "",
            website: "",
            note: "",
        },
    });

    // Update form when selectedLawFirm changes
    useEffect(() => {
        if (selectedLawFirm && isEdit) {
            form.reset({
                officeName: selectedLawFirm.office_name || "",
                payee: selectedLawFirm.payee || "",
                street1: selectedLawFirm.street1 || "",
                street2: selectedLawFirm.street2 || "",
                city: selectedLawFirm.city || "",
                state: selectedLawFirm.state || "",
                zip: selectedLawFirm.zip_code || "",
                phone: selectedLawFirm.phone || "",
                phoneExt: selectedLawFirm.phone_ext || "",
                cell: selectedLawFirm.cell || "",
                email: selectedLawFirm.email || "",
                fax: selectedLawFirm.fax || "",
                taxId: selectedLawFirm.tax_id || "",
                website: selectedLawFirm.website || "",
                note: selectedLawFirm.note || "",
            });
        }
    }, [selectedLawFirm, isEdit, form]);

    const createLawFirm = useCreateLawFirmMutation();
    const updateLawFirm = useUpdateLawFirmMutation(selectedLawFirm?.id?.toString() || '0');

    async function onSubmit(values: z.infer<typeof formSchema>) {
        try {
            setIsSubmitting(true);
            const lawFirmData = {
                office_name: values.officeName,
                payee: values.payee,
                street1: values.street1,
                street2: values.street2,
                city: values.city,
                state: values.state,
                zip_code: values.zip,
                phone: values.phone,
                phone_ext: values.phoneExt,
                cell: values.cell,
                email: values.email,
                fax: values.fax,
                tax_id: values.taxId,
                website: values.website,
                note: values.note,
            };

            let newLawFirmId: number | undefined;
            if (isEdit && selectedLawFirm) {
                const response = await updateLawFirm.mutateAsync(lawFirmData as LawFirmUpdateRequest);
                newLawFirmId = response.id;
            } else {
                const response = await createLawFirm.mutateAsync(lawFirmData as LawFirmCreateRequest);
                newLawFirmId = response.id;
            }

            form.reset(form.getValues());
            setOpen(false);
            onSuccess?.(newLawFirmId);
        } catch (error) {
            console.error(`Failed to ${isEdit ? 'update' : 'create'} law firm:`, error);
        } finally {
            setIsSubmitting(false);
        }
    }

    const states = Object.entries(USStatesLabels).map(([code, label]) => ({
        value: code,
        label: label
    }));

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {children}
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? 'Edit' : 'Add'} Law Firm
                        </DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="max-h-[80vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <FormField
                                    control={form.control}
                                    name="officeName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Office Name *</FormLabel>
                                            <FormControl>
                                                <Input {...field} value={field.value ?? ''} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="payee"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Payee</FormLabel>
                                            <FormControl>
                                                <Input {...field} value={field.value ?? ''} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="street1"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Street 1</FormLabel>
                                                <FormControl>
                                                    <Input {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="street2"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Street 2</FormLabel>
                                                <FormControl>
                                                    <Input {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="city"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>City</FormLabel>
                                                <FormControl>
                                                    <Input {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="state"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>State</FormLabel>
                                                <Select onValueChange={field.onChange} defaultValue={field.value ?? undefined}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select state" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {states.map(({ value, label }) => (
                                                            <SelectItem key={value} value={value}>
                                                                {label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="zip"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>ZIP</FormLabel>
                                                <FormControl>
                                                    <Input {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="flex space-x-2">
                                        <FormField
                                            control={form.control}
                                            name="phone"
                                            render={({ field }) => (
                                                <FormItem className="flex-1">
                                                    <FormLabel>Phone</FormLabel>
                                                    <FormControl>
                                                        <PhoneNumberInput {...field} value={field.value ?? ''} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="phoneExt"
                                            render={({ field }) => (
                                                <FormItem className="w-20">
                                                    <FormLabel>Ext</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="x" value={field.value ?? ''} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <FormField
                                        control={form.control}
                                        name="cell"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Cell</FormLabel>
                                                <FormControl>
                                                    <PhoneNumberInput {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="fax"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Fax</FormLabel>
                                                <FormControl>
                                                    <PhoneNumberInput {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Email</FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="email" value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="website"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Website</FormLabel>
                                                <FormControl>
                                                    <Input {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="taxId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Tax ID / SSN</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="12-3456789 or ***********" value={field.value ?? ''} />
                                            </FormControl>
                                            <p className="text-xs text-muted-foreground">
                                                Format: 12-3456789 (Tax ID) or *********** (SSN)
                                            </p>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="note"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Note</FormLabel>
                                            <FormControl>
                                                <CustomCKEditor
                                                    initialValue={field.value ?? ''}
                                                    onChange={(value: string) => {
                                                        field.onChange(value);
                                                    }}
                                                    placeholder="Take a note..."
                                                    minHeight="100px"
                                                    className="min-h-[100px] resize-none bg-gray-50"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="flex justify-end space-x-4">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="button"
                                    onClick={(e) => {
                                        form.handleSubmit((values) => onSubmit(values))(e);
                                    }}
                                    disabled={isSubmitting}
                                >
                                    {isEdit ? "Update" : "Create"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    )
}

export default AddEditLawFirm