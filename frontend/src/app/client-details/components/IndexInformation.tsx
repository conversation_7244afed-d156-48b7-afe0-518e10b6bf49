import React, { <PERSON> } from "react";
import { FileText } from "lucide-react";
import { LoadingSpinner } from "@/components/DocumentSidebar/components/LoadingSpinner";
import { useIncidentDetailsQuery } from "@/services/incidentService";
import { useClientBasicDetailsQuery } from "@/services/case-management/clientDetailService";
import EditIndexInformationDialog from "./EditIndexInformationDialog";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { formatDateForDisplay } from "@/utils/dateUtils";
// import { CurrencyDisplay } from "@/components/ui/currency-display";
// import { AddressLink, formatFullAddress } from "@/components/gMap/address-link";
import { CopyNumber } from "@/components/ui/copy-number";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import CKViewer from "@/components/ckeditor/CKViewer";

const IndexInformation: FC<{ clientId: string }> = ({ clientId }) => {
  const { data: clientBasicDetails, isLoading, error } = useClientBasicDetailsQuery(clientId);
  const { data: incidentDetails } = useIncidentDetailsQuery(clientId || "");

  // Only show loading state on initial load, not on updates
  if (isLoading && !clientBasicDetails) return <LoadingSpinner />;

  const safeData =
    error || !clientBasicDetails
      ? {
        first_name: "—",
        middle_name: "—",
        last_name: "—",
        nickname: "—",
        gender: "—",
        date_of_birth: "—",
        ssn: "—",
        driver_license_type: "—",
        driver_license: "—",
        source_type: "—",
        source_detail: "—",
        referred_by: "—",
        note: "—",
        deceased: false,
        language: "—",
        race: "—",
      }
      : clientBasicDetails;

  const {
    first_name,
    middle_name,
    last_name,
    nickname,
    gender,
    date_of_birth,
    ssn,
    driver_license_type,
    driver_license,
    source_type,
    source_detail,
    referred_by,
    language,
    race,
    note,
  } = safeData;

  const name = `${first_name} ${middle_name ? middle_name + " " : ""}${last_name}`;

  const getLicenseTypeDisplay = (type: string | undefined) => {
    if (!type) return "—";
    switch (type?.toLowerCase()) {
      case "dl":
        return "DL";
      case "id":
        return "ID";
      case "permit":
        return "Permit";
      default:
        return type || "—";
    }
  };

  return (
    <div className="bg-white rounded-lg p-4 shadow-sm">
      {/* Header */}
      <div className="flex items-center  justify-between gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
        <div className="flex items-center gap-2">
          <FileText className="w-5 h-5 text-[#060216]" />
          <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
            Index Details
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <EditIndexInformationDialog
            clientId={clientId}
            incidentDetails={incidentDetails}
          />
          <ManageEmailTemplate
            caseId={clientId}
            contextType={"client" as TemplateContextType}
          />
        </div>
      </div>

      <div className="flex flex-col gap-4">
        {/* Main Info Grid */}
        <InfoFieldGroup fields={[
          { label: "Name", value: name || "—" },
          { label: "Nickname", value: nickname || "—" },
          { label: "SSN", value: ssn ? <CopyNumber value={ssn} /> : "—" },
          {
            label: "DOB", value: date_of_birth && date_of_birth !== "—"
              ? formatDateForDisplay(date_of_birth)
              : "—"
          },
          { label: "License Type", value: getLicenseTypeDisplay(driver_license_type) || "—" },
          { label: "License Number", value: driver_license || "—" },
          { label: "Gender", value: gender || "—" },
          { label: "Source Type", value: source_type || "—" },
          { label: "Source Detail", value: source_detail || "—" },
          { label: "Referred By", value: referred_by || "—" },
          { label: "Language", value: language || "—" },
          { label: "Race", value: race || "—" }
        ]} />

        {/* Notes Section */}
        <div className="bg-[#F7F7F7] p-2 rounded-lg border border-[#00000014]">
          <div className="flex flex-col px-2 py-2">
            <span className="text-[#06021699] text-xs leading-[21px] font-medium font-Manrope mb-[2px]">
              Notes
            </span>
            <div className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
              {note ? <CKViewer content={note} /> : "—"}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndexInformation;
