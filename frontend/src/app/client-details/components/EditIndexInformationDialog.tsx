import React, { useState, useEffect, FC, useCallback } from "react";
import { Edit, Loader2, Search } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { format, parse, startOfDay, parseISO } from "date-fns";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useUpdateClientBasicDetailsMutation } from "@/services/case-management/clientDetailService";
import { Gender } from "@/type/case-management/commonTypes";

import { CaseIncidentDetails } from "@/type/incidentTypes";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import {
  useSourceDetails,
  useSourceTags,
} from "@/services/organizationService";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

const formSchema = z.object({
  // Required fields
  doi: z.date({
    required_error: "Date of loss is required",
  }),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  sourceType: z.string().min(1, "Source type is required"),

  // Optional fields
  dob: z.date().optional().nullable(),
  gender: z.enum(["Male", "Female", "Non-binary"]).optional().nullable(),
  middleName: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  nickname: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  ssn: z.string()
    .refine((val) => !val || /^\d{3}-\d{2}-\d{4}$/.test(val), {
      message: "If provided, SSN must be in format: ***********"
    })
    .optional()
    .nullable()
    .transform((val) => val || ""),
  licenseType: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  licenseNumber: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  sourceDetail: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  referredBy: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  note: z.string().optional().nullable(),
  deceased: z
    .boolean()
    .optional()
    .nullable()
    .transform((val) => val || false),
  language: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
  race: z
    .string()
    .optional()
    .nullable()
    .transform((val) => val || ""),
});

type FormData = z.infer<typeof formSchema>;

const EditIndexInformationDialog: FC<{
  clientId: string;
  incidentDetails?: CaseIncidentDetails;
}> = ({ clientId, incidentDetails }) => {
  const {
    data: clientData,
    error: APIError,
    // isLoading,
  } = useSelector((state: RootState) => state.clientDetail.clientBasicDetails);



  // Use empty object with default values if there's an error or no data
  const safeData =
    APIError || !clientData
      ? {
        first_name: "",
        middle_name: "",
        last_name: "",
        nickname: "",
        gender: "",
        date_of_birth: "",
        ssn: "",
        driver_license_type: "",
        driver_license: "",
        source_type: "",
        source_detail: "",
        referred_by: "",
        note: "",
        deceased: false,
        language: "",
        race: "",
      }
      : clientData;

  // Destructure from safeData instead of data
  const {
    first_name,
    middle_name,
    last_name,
    nickname,
    gender,
    date_of_birth,
    ssn,
    driver_license_type,
    driver_license,
    source_type,
    source_detail,
    referred_by,
    note,
    deceased,
    language,
    race,
  } = safeData;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

  // Add hooks for both source details and tags
  const { data: sourceDetails, isLoading: isLoadingSourceDetails } =
    useSourceDetails();
  const { data: sourceTags, isLoading: isLoadingSourceTags } = useSourceTags();

  // Add this state near other state declarations
  const [sourceTypeSearch, setSourceTypeSearch] = useState("");
  const [sourceDetailSearch, setSourceDetailSearch] = useState("");

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: first_name,
      lastName: last_name,
      middleName: middle_name,
      nickname: nickname,
      gender: gender as "Male" | "Female" | "Non-binary",
      ssn: ssn,
      licenseNumber: driver_license,
      sourceType: source_type,
      referredBy: referred_by,
      note: note || "",
      deceased: deceased,
      dob: date_of_birth ? startOfDay(parseISO(date_of_birth)) : undefined,
      doi: incidentDetails?.incident_date
        ? new Date(incidentDetails.incident_date)
        : undefined,
      licenseType: driver_license_type,
      sourceDetail: source_detail,
      language: language,
      race: race,
    },
  });

  // Add useEffect to update form when incidentDetails changes
  useEffect(() => {
    if (incidentDetails?.incident_date) {
      form.setValue("doi", new Date(incidentDetails.incident_date));
    }
  }, [incidentDetails, form]);

  const { mutateAsync: updateClientBasicDetails } =
    useUpdateClientBasicDetailsMutation(clientId);

  // Add resetFormToInitialValues function
  const resetFormToInitialValues = useCallback(() => {
    form.reset({
      firstName: first_name,
      lastName: last_name,
      middleName: middle_name,
      nickname: nickname,
      gender: gender as "Male" | "Female" | "Non-binary",
      ssn: ssn,
      licenseNumber: driver_license,
      sourceType: source_type,
      referredBy: referred_by,
      note: note || "",
      deceased: deceased,
      dob: date_of_birth ? startOfDay(parseISO(date_of_birth)) : undefined,
      doi: incidentDetails?.incident_date
        ? new Date(incidentDetails.incident_date)
        : undefined,
      licenseType: driver_license_type,
      sourceDetail: source_detail,
      language: language,
      race: race,
    });
  }, [
    first_name,
    last_name,
    middle_name,
    nickname,
    gender,
    ssn,
    driver_license,
    source_type,
    referred_by,
    note,
    deceased,
    date_of_birth,
    incidentDetails,
    driver_license_type,
    source_detail,
    language,
    race,
  ]);

  // Add useEffect to reset form when data changes
  useEffect(() => {
    resetFormToInitialValues();
  }, [resetFormToInitialValues]);

  const onSubmit = async (formData: FormData) => {
    try {
      setError(null);
      setIsSubmitting(true);

      // Transform the form data to match API expectations
      const clientPayload = {
        first_name: formData.firstName,
        middle_name: formData.middleName,
        last_name: formData.lastName,
        nickname: formData.nickname,
        gender: formData.gender as Gender,
        date_of_birth: formData.dob
          ? format(startOfDay(formData.dob), "yyyy-MM-dd")
          : undefined,
        ssn: formData.ssn,
        driver_license_type: formData.licenseType,
        driver_license: formData.licenseNumber,
        source_type: formData.sourceType,
        source_detail: formData.sourceDetail,
        referred_by: formData.referredBy,
        note: formData.note || "",
        deceased: formData.deceased,
        language: formData.language,
        race: formData.race,
      };



      // Make API call to update client details
      await updateClientBasicDetails({
        ...clientPayload,
        note: formData.note || "",
      });

      // On success: close dialog and reset form
      resetFormToInitialValues();
      setOpen(false);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while updating details"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(newOpen);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    resetFormToInitialValues();
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Index Information</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-6 py-4 overflow-y-auto max-h-[80vh] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {/* Group 1: Date of Incident - Make it full width */}
                {/* <div className="w-full">
                                    <h3 className="text-sm font-medium mb-4 text-muted-foreground">Incident Details</h3>
                                    <FormField
                                        control={form.control}
                                        name="doi"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Date of Loss *</FormLabel>
                                                <FormControl>
                                                    <CustomDateInput
                                                        value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                        onChange={(value: string) => {
                                                            // Try to parse the date
                                                            const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                                                            if (!isNaN(parsedDate.getTime())) {
                                                                field.onChange(parsedDate);
                                                            }
                                                        }}
                                                        error={!!form.formState.errors.doi}
                                                        maxDate={new Date()}
                                                        onError={(message: string) => {
                                                            form.setError("doi", {
                                                                type: "manual",
                                                                message
                                                            });
                                                        }}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div> */}

                {/* Group 2: Personal Information - Update to 4 columns */}
                <div>
                  <h3 className="text-sm font-medium mb-4 text-muted-foreground">
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    {/* First row */}
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name *</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="middleName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Middle Name</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name *</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="nickname"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nickname</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Second row */}
                    <FormField
                      control={form.control}
                      name="ssn"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SSN</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ""}
                              onChange={(e) => {
                                // Remove all non-digits
                                let value = e.target.value.replace(/\D/g, '');

                                // Limit to 9 digits
                                value = value.slice(0, 9);

                                // Add hyphens if we have enough digits
                                if (value.length >= 3) {
                                  value = value.slice(0, 3) + "-" + value.slice(3);
                                }
                                if (value.length >= 6) {
                                  value = value.slice(0, 6) + "-" + value.slice(6);
                                }

                                field.onChange(value);
                              }}
                              placeholder="123-345-6789"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="dob"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date of Birth</FormLabel>
                          <FormControl>
                            <CustomDateInput
                              value={
                                field.value
                                  ? format(startOfDay(field.value), "dd-MM-yyyy")
                                  : ""
                              }
                              onChange={(value: string) => {
                                // Try to parse the date and set to start of day
                                const parsedDate = parse(
                                  value,
                                  "dd-MM-yyyy",
                                  new Date()
                                );
                                if (!isNaN(parsedDate.getTime())) {
                                  field.onChange(startOfDay(parsedDate));
                                }
                              }}
                              error={!!form.formState.errors.dob}
                              maxDate={new Date()}
                              onError={(message: string) => {
                                form.setError("dob", {
                                  type: "manual",
                                  message,
                                });
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="licenseType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Driver License</FormLabel>
                          <FormControl>
                            <div className="flex gap-2">
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger className="w-[100px]">
                                  <SelectValue placeholder="Type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="--">--</SelectItem>
                                  <SelectItem value="dl">DL</SelectItem>
                                  <SelectItem value="id">ID</SelectItem>
                                  <SelectItem value="permit">Permit</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormField
                                control={form.control}
                                name="licenseNumber"
                                render={({ field }) => (
                                  <Input
                                    {...field}
                                    className="flex-1"
                                    placeholder="Enter license number"
                                  />
                                )}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gender</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value || ""}
                              className="flex flex-col space-y-1"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="Male" id="male" />
                                <Label htmlFor="male">Male</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="Female" id="female" />
                                <Label htmlFor="female">Female</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem
                                  value="Non-binary"
                                  id="Non-binary"
                                />
                                <Label htmlFor="Non-binary">Non-binary</Label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Group 3: Source Information - Update to 3 columns */}
                <div>
                  <h3 className="text-sm font-medium mb-4 text-muted-foreground">
                    Source Information
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="sourceType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Source Type *</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                setSourceTypeSearch(""); // Clear search when selection is made
                              }}
                              value={field.value || source_type}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select source type" />
                              </SelectTrigger>
                              <SelectContent>
                                <div className="flex items-center px-3 pb-2">
                                  <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                                  <input
                                    className="flex h-9 w-full rounded-md bg-transparent py-1 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="Search source type..."
                                    value={sourceTypeSearch}
                                    onChange={(e) => setSourceTypeSearch(e.target.value)}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </div>
                                {isLoadingSourceTags ? (
                                  <SelectItem value="loading">Loading...</SelectItem>
                                ) : (
                                  <div className="max-h-[200px] overflow-y-auto">
                                    {(sourceTags || [])
                                      .filter(tag =>
                                        tag.name.toLowerCase().includes(sourceTypeSearch.toLowerCase())
                                      )
                                      .map((tag) => (
                                        <SelectItem key={tag.id} value={tag.name}>
                                          {tag.name}
                                        </SelectItem>
                                      ))}
                                    {sourceTypeSearch && (sourceTags || [])
                                      .filter(tag =>
                                        tag.name.toLowerCase().includes(sourceTypeSearch.toLowerCase())
                                      ).length === 0 && (
                                        <div className="px-2 py-2 text-sm text-muted-foreground text-center">
                                          No results found
                                        </div>
                                      )}
                                  </div>
                                )}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="sourceDetail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Source Detail</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                setSourceDetailSearch(""); // Clear search when selection is made
                              }}
                              value={field.value || ""}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select source detail" />
                              </SelectTrigger>
                              <SelectContent>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    className="mb-2 w-full justify-center text-sm"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      field.onChange("");
                                      setSourceDetailSearch(""); // Clear search when selection is cleared
                                    }}
                                  >
                                    Clear Selection
                                  </Button>
                                )}
                                <div className="flex items-center px-3 pb-2">
                                  <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                                  <input
                                    className="flex h-9 w-full rounded-md bg-transparent py-1 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="Search source detail..."
                                    value={sourceDetailSearch}
                                    onChange={(e) => setSourceDetailSearch(e.target.value)}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </div>
                                {isLoadingSourceDetails ? (
                                  <SelectItem value="loading">Loading...</SelectItem>
                                ) : (
                                  <div className="max-h-[200px] overflow-y-auto">
                                    {(sourceDetails || [])
                                      .filter(detail =>
                                        detail.name.toLowerCase().includes(sourceDetailSearch.toLowerCase())
                                      )
                                      .map((detail) => (
                                        <SelectItem
                                          key={detail.id}
                                          value={detail.name}
                                        >
                                          {detail.name}
                                        </SelectItem>
                                      ))}
                                    {sourceDetailSearch && (sourceDetails || [])
                                      .filter(detail =>
                                        detail.name.toLowerCase().includes(sourceDetailSearch.toLowerCase())
                                      ).length === 0 && (
                                        <div className="px-2 py-2 text-sm text-muted-foreground text-center">
                                          No results found
                                        </div>
                                      )}
                                  </div>
                                )}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="referredBy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Referred By</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter referral source"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                {/* Group 5: Language and Race - Update layout */}
                <div>
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Language</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select language" />
                              </SelectTrigger>
                              <SelectContent>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    className="mb-2 w-full justify-center text-sm"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      field.onChange("");
                                    }}
                                  >
                                    Clear Selection
                                  </Button>
                                )}
                                <SelectItem value="English">English</SelectItem>
                                <SelectItem value="Arabic">Arabic</SelectItem>
                                <SelectItem value="Armenian">Armenian</SelectItem>
                                <SelectItem value="Bengali">Bengali</SelectItem>
                                <SelectItem value="Cantonese">Cantonese</SelectItem>
                                <SelectItem value="Creole">Creole</SelectItem>
                                <SelectItem value="French">French</SelectItem>
                                <SelectItem value="French Creole">French Creole</SelectItem>
                                <SelectItem value="German">German</SelectItem>
                                <SelectItem value="Greek">Greek</SelectItem>
                                <SelectItem value="Gujarati">Gujarati</SelectItem>
                                <SelectItem value="Hebrew">Hebrew</SelectItem>
                                <SelectItem value="Hindi">Hindi</SelectItem>
                                <SelectItem value="Hmong">Hmong</SelectItem>
                                <SelectItem value="Italian">Italian</SelectItem>
                                <SelectItem value="Japanese">Japanese</SelectItem>
                                <SelectItem value="Korean">Korean</SelectItem>
                                <SelectItem value="Mandarin">Mandarin</SelectItem>
                                <SelectItem value="Persian">Persian</SelectItem>
                                <SelectItem value="Polish">Polish</SelectItem>
                                <SelectItem value="Portuguese">Portuguese</SelectItem>
                                <SelectItem value="Punjabi">Punjabi</SelectItem>
                                <SelectItem value="Russian">Russian</SelectItem>
                                <SelectItem value="Spanish">Spanish</SelectItem>
                                <SelectItem value="Tagalog">Tagalog</SelectItem>
                                <SelectItem value="Telugu">Telugu</SelectItem>
                                <SelectItem value="Urdu">Urdu</SelectItem>
                                <SelectItem value="Vietnamese">Vietnamese</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="race"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Race</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select race" />
                              </SelectTrigger>
                              <SelectContent>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    className="mb-2 w-full justify-center text-sm"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      field.onChange("");
                                    }}
                                  >
                                    Clear Selection
                                  </Button>
                                )}
                                <SelectItem value="Asian">Asian</SelectItem>
                                <SelectItem value="Black or African American">
                                  Black or African American
                                </SelectItem>
                                <SelectItem value="Caucasian">Caucasian</SelectItem>
                                <SelectItem value="Hispanic">Hispanic</SelectItem>
                                <SelectItem value="Middle Eastern">Middle Eastern</SelectItem>
                                <SelectItem value="Native American">Native American</SelectItem>
                                <SelectItem value="Other">Other</SelectItem>
                                <SelectItem value="Pacific Islander">Pacific Islander</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Group 4: Additional Information - Update layout */}
                <div>
                  <h3 className="text-sm font-medium mb-4 text-muted-foreground">
                    Additional Information
                  </h3>
                  <div className="col-span-2">
                    <FormField
                      control={form.control}
                      name="note"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Note</FormLabel>
                          <FormControl>
                            <div className="bg-white border rounded-md">
                              <CustomCKEditor
                                initialValue={field.value || ''}
                                onChange={(value: string) => field.onChange(value)}
                                placeholder="Take a note..."
                                minHeight="100px"
                                  className="min-h-[100px] resize-none bg-gray-50"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex items-start pt-7">
                    <FormField
                      control={form.control}
                      name="deceased"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className="text-sm font-medium">
                              Deceased
                            </FormLabel>
                            <FormMessage />
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>


              {error && <div className="text-red-500 text-sm">{error}</div>}

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  disabled={isSubmitting}
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>
                
                <Button
                  type="submit"
                  disabled={isSubmitting || !form.formState.isDirty}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save changes"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
};

export default EditIndexInformationDialog;
