// src/app/client-details/components/AddEditProperDamage.tsx

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useState } from "react";
import { Plus } from "lucide-react";
import { Edit } from "lucide-react";
import {
  useCreatePropertyDamageMutation,
  useUpdatePropertyDamageMutation,
} from "@/services/case-management/clientDetailService";
import { useInsuranceCompaniesQuery } from "@/services/orgAPIs";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

enum DamageType {
  MAJOR = "Major",
  MINOR = "Minor",
  MODERATE = "Moderate",
}

const formSchema = z.object({
  insurance: z.string().optional(),
  damage: z.enum([DamageType.MAJOR, DamageType.MINOR, DamageType.MODERATE]),
  frameDamage: z.boolean().optional().default(false),
  totalLoss: z.boolean().optional().default(false),
  registeredOwner: z.string().optional(),
  autoBodyShop: z.string().optional(),
  estimate: z.string().optional(),
  final: z.string().optional(),
  make: z.string().optional(),
  model: z.string().optional(),
  color: z.string().optional(),
  year: z.string().optional(),
  plateNumber: z.string().optional(),
  vinNumber: z
    .string()
    .optional()
    .refine((val) => !val || /^[A-HJ-NPR-Z0-9]{17}$/i.test(val), {
      message: "VIN must be 17 characters (excluding I, O, Q) and alphanumeric",
    }),
  mileage: z
    .string()
    .optional()
    .transform((val) => (!val ? null : val)),
  note: z.string().optional(),
  damageDescription: z.string().optional(),
});

interface AddEditProperDamageProps {
  isEditing?: boolean;
  defaultValues?: z.infer<typeof formSchema>;
  caseId: string;
}

export default function AddEditProperDamage({
  isEditing = false,
  defaultValues,
  caseId,
}: AddEditProperDamageProps) {
  const createMutation = useCreatePropertyDamageMutation(caseId);
  const updateMutation = useUpdatePropertyDamageMutation(caseId);
  const { data: insuranceCompanies, isLoading: insuranceLoading } =
    useInsuranceCompaniesQuery();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues || {
      insurance: "",
      damage: DamageType.MAJOR,
      frameDamage: false,
      totalLoss: false,
      registeredOwner: "",
      autoBodyShop: "",
      estimate: "",
      final: "",
      make: "",
      model: "",
      color: "",
      year: "",
      plateNumber: "",
      vinNumber: "",
      mileage: "",
      note: "",
      damageDescription: "",
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [open, setOpen] = useState(false);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);

      const requestData = {
        case: caseId,
        damage: values.damage,
        frame_damage: values.frameDamage ?? false,
        total_loss: values.totalLoss ?? false,
        registered_owner: values.registeredOwner || null,
        auto_body_shop: values.autoBodyShop || null,
        estimate: values.estimate || null,
        final: values.final || null,
        vehicle_make: values.make || null,
        vehicle_model: values.model || null,
        color: values.color || null,
        vehicle_year: values.year ? values.year.toString() : null,
        license_plate: values.plateNumber || null,
        vehicle_vin: values.vinNumber || null,
        mileage: values.mileage ? values.mileage.toString() : null,
        note: values.note || null,
        insurance_company: values.insurance || null,
        damage_description: values.damageDescription || null,
      };

      if (isEditing) {
        await updateMutation.mutateAsync(requestData);
      } else {
        await createMutation.mutateAsync(requestData);
      }
      setOpen(false);
    } catch (error) {
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {isEditing ? (
          <Edit
            className="h-4 w-4 text-green-600 cursor-pointer"
            onClick={() => setOpen(true)}
          />
        ) : (
          <Button
            variant="link"
            className="text-green-600"
            onClick={() => setOpen(true)}
          >
            <Plus className="h-4 w-4 stroke-[1.5]" />
            Property Damage
          </Button>
        )}
      </DialogTrigger>
      <DialogContent
        className="max-w-4xl"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Property Damage" : "Add Property Damage"}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="max-h-[80vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
              <div className="grid grid-cols-2 gap-4">
                {/* Insurance */}
                <FormField
                  control={form.control}
                  name="insurance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Insurance</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select insurance" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {field.value && (
                            <Button
                              variant="ghost"
                              className="mt-2 w-full justify-center text-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                field.onChange("");
                                form.setValue("insurance", undefined);
                              }}
                            >
                              Clear Selection
                            </Button>
                          )}
                          {insuranceLoading ? (
                            <SelectItem value="loading">
                              Loading...
                            </SelectItem>
                          ) : (
                            insuranceCompanies?.map((company) => (
                              <SelectItem
                                key={company.id}
                                value={company.id.toString()}
                              >
                                {company.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                {/* Damage */}
                <FormField
                  control={form.control}
                  name="damage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Damage *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select damage type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(DamageType).map((damageType) => (
                            <SelectItem key={damageType} value={damageType}>
                              {damageType}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                {/* Checkboxes */}
                <FormField
                  control={form.control}
                  name="frameDamage"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Frame Damage</FormLabel>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="totalLoss"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Total Loss</FormLabel>
                    </FormItem>
                  )}
                />

                {/* Text inputs */}
                {[
                  "registeredOwner",
                  "autoBodyShop",
                  "model",
                  "color",
                ].map((fieldName) => (
                  <FormField
                    key={fieldName}
                    control={form.control}
                    name={fieldName as keyof z.infer<typeof formSchema>}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {fieldName
                            .replace(/([A-Z])/g, " $1")
                            .replace(/^./, (str) => str.toUpperCase())}
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value?.toString() || ""}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                ))}

                <FormField
                  control={form.control}
                  name="make"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Make</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="text"
                          value={field.value?.toString() || ""}
                          onChange={(e) => {
                            const value = e.target.value.replace(/[^\d]/g, "");
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="final"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Final</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="text"
                          value={field.value?.toString() || ""}
                          onChange={(e) => {
                            const value = e.target.value.replace(/[^\d]/g, "");
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Year dropdown */}
                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Year</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select year" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {field.value && (
                            <Button
                              variant="ghost"
                              className="mt-2 w-full justify-center text-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                field.onChange("");
                                form.setValue("year", undefined);
                              }}
                            >
                              Clear Selection
                            </Button>
                          )}
                          {/* Add year options */}
                          {Array.from({ length: 30 }, (_, i) => {
                            const year = new Date().getFullYear() - i;
                            return (
                              <SelectItem key={year} value={year.toString()}>
                                {year}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                {/* Update numeric input fields */}
                <FormField
                  control={form.control}
                  name="estimate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estimate ($)</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" min="0" step="0.01" />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Add these three fields explicitly */}
                <FormField
                  control={form.control}
                  name="plateNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plate #</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          value={field.value?.toString() || ""}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vinNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>VIN #</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          value={field.value?.toString() || ""}
                          onChange={(e) => {
                            // Convert to uppercase and remove invalid characters
                            const formatted = e.target.value
                              .toUpperCase()
                              .replace(/[IOQ]/g, "") // Remove I, O, Q
                              .replace(/[^A-Z0-9]/g, "") // Remove non-alphanumeric
                              .slice(0, 17); // Limit to 17 characters
                            field.onChange(formatted);
                          }}
                          maxLength={17}
                          placeholder="17 character VIN"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mileage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mileage</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="text"
                          value={field.value?.toString() || ""}
                          onChange={(e) => {
                            // Only allow numbers
                            const value = e.target.value.replace(
                              /[^\d]/g,
                              ""
                            );
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Note field */}
              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Note</FormLabel>
                    <FormControl>
                      <CustomCKEditor
                        initialValue={field.value}
                        onChange={(value: string) => {
                          field.onChange(value);
                        }}
                        placeholder="Enter a note here..."
                        minHeight="100px"
                        className="min-h-[100px] resize-none bg-gray-50"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Damage Description */}
              <FormField
                control={form.control}
                name="damageDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Damage Description</FormLabel>
                    <FormControl>
                      <CustomCKEditor
                        initialValue={field.value}
                        onChange={(value: string) => {
                          field.onChange(value);
                        }}
                        placeholder="Enter a damage description here..."
                        minHeight="100px"
                        className="min-h-[100px] resize-none bg-gray-50"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                onClick={(e) => {
                  form.handleSubmit((values) => onSubmit(values))(e);
                }}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saving..." : "Save"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}