"use client";

import React, { useEffect, FC } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Edit, Plus } from "lucide-react";
import { default as AddEditInsuranceCompany } from "@/app/client-details/components/AddEditInsurance";
import { useInsuranceCompaniesQuery } from "@/services/orgAPIs";
import { toast } from "@/hooks/use-toast";
import { useState } from "react";
import {
    InsuranceType,
    CoverageType,
    StackedStatus,
    ClientInsurance,
} from "@/type/case-management/clientDetailTypes";
import {
    useCreateClientInsuranceMutation,
    useUpdateClientInsuranceMutation,
} from "@/services/case-management/clientDetailService";
import { ClientInsuranceCreateRequest } from "@/type/case-management/clientDetailTypes";
import {
    UmUimLimit,
    MedpayLimit,
    PipLimit,
    CoverageStatus,
    LiabilityStatus,
    PolicyType,
    PlanType,
} from "@/constants/insurance";
import { useQueryClient } from "@tanstack/react-query";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
// import CustomCKEditor from "@/components/ckeditor/CKEditor";
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";
import { useClientInsuranceSyncMutation } from "@/services/case-management/caseSyncService";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

const formSchema = z
    .object({
        noInsurance: z.boolean(),
        company: z.string(),
        planType: z.nativeEnum(PlanType).optional(),
        claimNumber: z.string().refine(
            (val) => !val || val.length >= 3,
            "Claim number must be at least 3 characters"
        ).optional(),
        policyNumber: z.string().optional(),
        medpayClaimNumber: z.string().optional(),
        insured: z.string().optional(),
        umUim: z.nativeEnum(UmUimLimit).optional(),
        medpay: z.nativeEnum(MedpayLimit).optional(),
        pip: z.nativeEnum(PipLimit).optional(),
        deductible: z
            .string()
            .refine(
                (val) => !val || !isNaN(Number(val)),
                "Deductible must be a number"
            )
            .optional(),
        coverageStatus: z.nativeEnum(CoverageStatus).optional(),
        liabilityStatus: z.nativeEnum(LiabilityStatus).optional(),
        liabilityType: z.string().optional(),
        liabilityPercent: z.string().default("100"),
        policyType: z.nativeEnum(PolicyType).optional(),
        insuranceType: z.nativeEnum(InsuranceType).optional(),
        coverageType: z.nativeEnum(CoverageType).optional(),
        stacked: z.nativeEnum(StackedStatus).optional(),
        vehicles: z.string().default("1"),
        claimNote: z.string().optional().nullable(),
        liability_percentage: z.string().default("100"),
    })
    .refine(
        (data) => {
            if (data.noInsurance) {
                return !data.company;
            }
            return !!data.company;
        },
        {
            message: "Company is required unless No Insurance is checked",
            path: ["company"],
        }
    );

interface Props {
    isEdit: boolean;
    caseId: string;
    insurance?: ClientInsurance;
    onInsuranceCreated?: (data: {
        insuranceId: string;
        companyId: string;
    }) => void;
}

const EditInsuranceDetailsDialog: FC<Props> = ({
    isEdit,
    caseId,
    insurance,
    onInsuranceCreated,
}) => {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);

    const createInsuranceMutation = useCreateClientInsuranceMutation(caseId);
    const updateInsuranceMutation = useUpdateClientInsuranceMutation(
        caseId,
        insurance?.id?.toString() || ""
    );
    const insuranceSync = useClientInsuranceSyncMutation(caseId);
    const { data: insuranceCompanies, isLoading: isLoadingCompanies } =
        useInsuranceCompaniesQuery();
    const queryClient = useQueryClient();
    // const [insuranceCompaniesLastUpdated, setInsuranceCompaniesLastUpdated] = useState(Date.now());
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            noInsurance: insurance?.no_insurance || false,
            company: insurance?.insurance_company?.id?.toString() || "",
            planType: (insurance?.plan_type as PlanType) || undefined,
            claimNumber: insurance?.claim_number || "",
            policyNumber: insurance?.policy_number || "",
            medpayClaimNumber: insurance?.medpay_claim_number || "",
            insured: insurance?.insured_name || "",
            umUim: (insurance?.um_uim as UmUimLimit) || undefined,
            medpay: (insurance?.medpay as MedpayLimit) || undefined,
            pip: (insurance?.pip as PipLimit) || undefined,
            deductible: insurance?.deductible || "",
            coverageStatus:
                (insurance?.coverage_status as CoverageStatus) || undefined,
            liabilityStatus:
                (insurance?.liability_status as LiabilityStatus) || undefined,
            liabilityType: insurance?.liability_type || "",
            liabilityPercent: insurance?.liability_percentage || "100",
            policyType: (insurance?.policy_type as PolicyType) || undefined,
            stacked: (insurance?.stacked as StackedStatus) || undefined,
            vehicles: insurance?.vehicles || "1",
            claimNote: insurance?.claim_note || "",
            insuranceType: (insurance?.insurance_type as InsuranceType) || undefined,
            coverageType: (insurance?.coverage_type as CoverageType) || undefined,
        },
    });

    // Watch the noInsurance field
    const noInsurance = form.watch("noInsurance");

    // Add effect to clear all fields when noInsurance is checked
    useEffect(() => {
        if (noInsurance) {
            form.setValue("company", "");
            form.setValue("planType", undefined);
            form.setValue("claimNumber", "");
            form.setValue("policyNumber", "");
            form.setValue("medpayClaimNumber", "");
            form.setValue("insured", "");
            form.setValue("umUim", undefined);
            form.setValue("medpay", undefined);
            form.setValue("pip", undefined);
            form.setValue("deductible", "");
            form.setValue("coverageStatus", undefined);
            form.setValue("liabilityStatus", undefined);
            form.setValue("liabilityType", "");
            form.setValue("liabilityPercent", "100");
            form.setValue("policyType", undefined);
            form.setValue("stacked", undefined);
            form.setValue("vehicles", "1");
            form.setValue("claimNote", undefined);
            form.setValue("insuranceType", undefined);
            form.setValue("coverageType", undefined);
            form.setValue("liability_percentage", "100");
        }
    }, [noInsurance, form]);

    const handleClose = () => {
        setOpen(false);
        queryClient.invalidateQueries({
            queryKey: ['clientInsurance', caseId]
        });
        form.reset({
            noInsurance: insurance?.no_insurance || false,
            company: insurance?.insurance_company?.id?.toString() || "",
            planType: insurance?.plan_type as PlanType || undefined,
            claimNumber: insurance?.claim_number || "",
            policyNumber: insurance?.policy_number || "",
            medpayClaimNumber: insurance?.medpay_claim_number || "",
            insured: insurance?.insured_name || "",
            umUim: insurance?.um_uim as UmUimLimit || undefined,
            medpay: insurance?.medpay as MedpayLimit || undefined,
            pip: insurance?.pip as PipLimit || undefined,
            deductible: insurance?.deductible || "",
            coverageStatus: insurance?.coverage_status as CoverageStatus || undefined,
            liabilityStatus: insurance?.liability_status as LiabilityStatus || undefined,
            liabilityType: insurance?.liability_type || "",
            liabilityPercent: insurance?.liability_percentage || "100",
            policyType: insurance?.policy_type as PolicyType || undefined,
            stacked: insurance?.stacked as StackedStatus || undefined,
            vehicles: "1",
            claimNote: insurance?.claim_note || "",
            insuranceType: insurance?.insurance_type as InsuranceType || undefined,
            coverageType: insurance?.coverage_type as CoverageType || undefined,
            liability_percentage: insurance?.liability_percentage || "100",
        });
    };

    // Add effect to reset form when insurance data changes
    useEffect(() => {
        if (insurance) {
            form.reset({
                noInsurance: insurance.no_insurance || false,
                company: insurance.insurance_company?.id?.toString() || "",
                planType: (insurance.plan_type as PlanType) || undefined,
                claimNumber: insurance.claim_number || "",
                policyNumber: insurance.policy_number || "",
                medpayClaimNumber: insurance.medpay_claim_number || "",
                insured: insurance.insured_name || "",
                umUim: (insurance.um_uim as UmUimLimit) || undefined,
                medpay: (insurance.medpay as MedpayLimit) || undefined,
                pip: (insurance.pip as PipLimit) || undefined,
                deductible: insurance.deductible || "",
                coverageStatus:
                    (insurance.coverage_status as CoverageStatus) || undefined,
                liabilityStatus:
                    (insurance.liability_status as LiabilityStatus) || undefined,
                liabilityType: insurance.liability_type || "",
                liabilityPercent: insurance.liability_percentage || "100",
                policyType: (insurance.policy_type as PolicyType) || undefined,
                stacked: (insurance.stacked as StackedStatus) || undefined,
                vehicles: insurance.vehicles || "1",
                claimNote: insurance.claim_note || "",
                insuranceType: (insurance.insurance_type as InsuranceType) || undefined,
                coverageType: (insurance.coverage_type as CoverageType) || undefined,
            });
        }
    }, [insurance, form]);

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(newOpen);
            }
        }
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    async function onSubmit(values: z.infer<typeof formSchema>) {
        try {
            if (!values.noInsurance && !values.company) {
                form.setError("company", {
                    type: "manual",
                    message: "Company is required when No Insurance is unchecked",
                });
                return;
            }

            const insuranceData: ClientInsuranceCreateRequest = {
                insurance_company: parseInt(values.company),
                no_insurance: values.noInsurance,
                plan_type: values.planType,
                claim_number: values.claimNumber,
                policy_number: values.policyNumber,
                medpay_claim_number: values.medpayClaimNumber,
                insured_name: values.insured,
                um_uim: values.umUim,
                medpay: values.medpay,
                pip: values.pip,
                deductible: values.deductible,
                coverage_status: values.coverageStatus,
                liability_status: values.liabilityStatus,
                liability_type: values.liabilityType,
                liability_percentage: values.liabilityPercent,
                policy_type: values.policyType,
                stacked: values.stacked,
                vehicles: values.vehicles,
                claim_note: values.claimNote || "",
            };
            if (isEdit && insurance?.id) {
                await updateInsuranceMutation.mutateAsync(insuranceData);
                // Invalidate the insurance list query to trigger a refetch
                await queryClient.invalidateQueries({
                    queryKey: ["clientInsuranceList", caseId],
                });
                toast({
                    title: "Success",
                    description: "Insurance details updated successfully",
                });
                setOpen(false);
            } else {
                const result = await createInsuranceMutation.mutateAsync(insuranceData);
                if (result) {
                    setOpen(false);
                    form.reset();
                    // Only open adjuster dialog for new insurance and when no_insurance is false
                    if (!values.noInsurance) {
                        onInsuranceCreated?.({
                            insuranceId: result.id.toString(),
                            companyId: values.company.toString(),
                        });
                    }
                }
            }
        } catch (error) {
            console.error("edit insurance details dialog error:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to save insurance details",
            });
        }
    }

    const handleEditClick = () => {
        const selectedCompany = form.getValues("company");
        if (!selectedCompany) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Please select a company first",
            });
            return false;
        }
        return true;
    };

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            setIsSyncing(true);

            // First update the insurance with current form values
            const values = form.getValues();
            const insuranceData: ClientInsuranceCreateRequest = {
                insurance_company: parseInt(values.company),
                no_insurance: values.noInsurance,
                plan_type: values.planType,
                claim_number: values.claimNumber,
                policy_number: values.policyNumber,
                medpay_claim_number: values.medpayClaimNumber,
                insured_name: values.insured,
                um_uim: values.umUim,
                medpay: values.medpay,
                pip: values.pip,
                deductible: values.deductible,
                coverage_status: values.coverageStatus,
                liability_status: values.liabilityStatus,
                liability_type: values.liabilityType,
                liability_percentage: values.liabilityPercent,
                policy_type: values.policyType,
                stacked: values.stacked,
                vehicles: values.vehicles,
                claim_note: values.claimNote || "",
            };

            let updatedInsurance;
            if (isEdit && insurance) {
                updatedInsurance = await updateInsuranceMutation.mutateAsync(insuranceData);
            } else {
                updatedInsurance = await createInsuranceMutation.mutateAsync(insuranceData);
            }

            await insuranceSync.mutateAsync({
                source_insurance_id: updatedInsurance.id,
                target_case_ids: selectedCaseIds
            });

            setIsSyncDialogOpen(false);
            setOpen(false);
            form.reset();

        } catch (error) {
            console.error("Failed to sync insurance:", error);
        } finally {
            setIsSyncing(false);
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {isEdit ? (
                        <Edit
                            className="h-4 w-4 text-green-600 cursor-pointer"
                            onClick={() => setOpen(true)}
                        />
                    ) : (
                        <Button
                            variant="link"
                            className="text-green-600"
                            onClick={() => setOpen(true)}
                        >
                            <Plus className="h-4 w-4 stroke-[1.5]" />
                            Insurance
                        </Button>
                    )}
                </DialogTrigger>
                <DialogContent className="max-w-6xl">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? "Edit Insurance Details" : "Add Insurance Details"}
                        </DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                        <form
                            onSubmit={form.handleSubmit(onSubmit)}
                            className="space-y-4 mb-8"
                        >
                            <div className="overflow-y-auto max-h-[80vh] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <div className="space-y-4">
                                    <FormField
                                        control={form.control}
                                        name="noInsurance"
                                        render={({ field }) => (
                                            <FormItem className="flex items-center space-x-2">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                                <FormLabel className="text-sm text-gray-600">
                                                    no insurance
                                                </FormLabel>
                                            </FormItem>
                                        )}
                                    />

                                    <div className="grid grid-cols-2 gap-8">
                                        <FormField
                                            control={form.control}
                                            name="company"
                                            render={({ field, fieldState }) => (
                                                <FormItem>
                                                    <FormLabel className="text-sm text-gray-600">
                                                        Company{" "}
                                                        {!noInsurance && (
                                                            <span className="text-red-500">*</span>
                                                        )}
                                                    </FormLabel>
                                                    <Select
                                                        onValueChange={field.onChange}
                                                        value={field.value}
                                                        disabled={noInsurance}
                                                        onOpenChange={(open) => {
                                                            if (open) {
                                                                // Refresh insurance companies list when select is opened
                                                                queryClient.refetchQueries({
                                                                    queryKey: ['insuranceCompanies']
                                                                });
                                                            }
                                                        }}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger
                                                                className={`bg-white ${fieldState.error ? "border-red-500" : ""
                                                                    }`}
                                                            >
                                                                <SelectValue placeholder={isLoadingCompanies ? "Loading..." : "Select company"} />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            {insuranceCompanies?.map((company) => (
                                                                <SelectItem
                                                                    key={`${company.id}`}
                                                                    value={company.id.toString()}
                                                                >
                                                                    {company.name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {fieldState.error && (
                                                        <div className="text-sm text-red-500 mt-1">
                                                            {fieldState.error.message}
                                                        </div>
                                                    )}
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="planType"
                                            render={({ field, fieldState }) => (
                                                <FormItem>
                                                    <FormLabel className="text-sm text-gray-600">
                                                        plan type
                                                    </FormLabel>
                                                    <Select
                                                        onValueChange={field.onChange}
                                                        value={field.value || ""}
                                                        disabled={noInsurance}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger
                                                                className={`bg-white ${fieldState.error ? "border-red-500" : ""
                                                                    }`}
                                                            >
                                                                <SelectValue placeholder="Select plan type" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            {field.value && (
                                                                <Button
                                                                    variant="ghost"
                                                                    className="mt-2 w-full justify-center text-sm"
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        field.onChange("");
                                                                        form.setValue("planType", undefined);
                                                                    }}
                                                                >
                                                                    Clear Selection
                                                                </Button>
                                                            )}
                                                            {Object.values(PlanType).map((type) => (
                                                                <SelectItem key={type} value={type}>
                                                                    {type}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {fieldState.error && (
                                                        <div className="text-sm text-red-500 mt-1">
                                                            {fieldState.error.message}
                                                        </div>
                                                    )}
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <div className="flex gap-4">
                                        <AddEditInsuranceCompany
                                            isEdit={false}
                                            trigger={
                                                <Button
                                                    variant="outline"
                                                    className="border-teal-600 text-teal-600 hover:bg-teal-50"
                                                    disabled={noInsurance}
                                                >
                                                    ADD
                                                </Button>
                                            }
                                            onSuccess={(newInsuranceId) => {
                                                if (newInsuranceId) {
                                                    form.setValue("company", newInsuranceId.toString(), {
                                                        shouldValidate: true,
                                                        shouldDirty: true,
                                                        shouldTouch: true
                                                    });

                                                    queryClient.invalidateQueries({ queryKey: ['insuranceCompanies'] });

                                                    setTimeout(() => {
                                                        form.trigger("company");
                                                    }, 100);
                                                }
                                            }}
                                        />

                                        <AddEditInsuranceCompany
                                            isEdit={true}
                                            company={form.getValues("company")}
                                            trigger={
                                                <Button
                                                    variant="outline"
                                                    className="border-teal-600 text-teal-600 hover:bg-teal-50"
                                                    onClick={handleEditClick}
                                                    disabled={noInsurance}
                                                >
                                                    EDIT
                                                </Button>
                                            }
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="claimNumber"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Claim #</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        {...field}
                                                        disabled={noInsurance}
                                                        className={fieldState.error ? "border-red-500" : ""}
                                                    />
                                                </FormControl>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="policyNumber"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Policy #</FormLabel>
                                                <FormControl>
                                                    <Input {...field} disabled={noInsurance} />
                                                </FormControl>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="medpayClaimNumber"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Medpay Claim #</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        {...field}
                                                        disabled={noInsurance}
                                                        className={fieldState.error ? "border-red-500" : ""}
                                                    />
                                                </FormControl>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="insured"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Insured</FormLabel>
                                                <FormControl>
                                                    <Input {...field} disabled={noInsurance} />
                                                </FormControl>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="umUim"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>UM / UIM</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value || ""}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger
                                                            className={`bg-white ${fieldState.error ? "border-red-500" : ""}`}
                                                        >
                                                            <SelectValue placeholder="Select UM / UIM" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                    form.setValue("umUim", undefined);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {Object.values(UmUimLimit).map((limit) => (
                                                            <SelectItem key={limit} value={limit}>
                                                                {limit}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="medpay"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Medpay</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value || ""}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select medpay" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                    form.setValue("medpay", undefined);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {Object.values(MedpayLimit).map((limit) => (
                                                            <SelectItem key={limit} value={limit}>
                                                                {limit}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="pip"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>PIP</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value || ""}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger
                                                            className={`bg-white ${fieldState.error ? "border-red-500" : ""}`}
                                                        >
                                                            <SelectValue placeholder="Select PIP" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                    form.setValue("pip", undefined);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {Object.values(PipLimit).map((limit) => (
                                                            <SelectItem key={limit} value={limit}>
                                                                {limit}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="deductible"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Deductible</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        {...field}
                                                        type="number"
                                                        disabled={noInsurance}
                                                        className={fieldState.error ? "border-red-500" : ""}
                                                        placeholder="Enter deductible amount"
                                                    />
                                                </FormControl>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="coverageStatus"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Coverage Status</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value || ""}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger
                                                            className={`bg-white ${fieldState.error ? "border-red-500" : ""}`}
                                                        >
                                                            <SelectValue placeholder="Select coverage status" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                    form.setValue("coverageStatus", undefined);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {Object.values(CoverageStatus).map((status) => (
                                                            <SelectItem key={status} value={status}>
                                                                {status.toLowerCase()}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <div>
                                        <FormLabel>Liability Details</FormLabel>
                                        <div className="grid grid-cols-3 gap-4">
                                            <FormField
                                                control={form.control}
                                                name="liabilityStatus"
                                                render={({ field, fieldState }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-xs text-gray-500">
                                                            Status
                                                        </FormLabel>
                                                        <Select
                                                            onValueChange={field.onChange}
                                                            value={field.value || ""}
                                                            disabled={noInsurance}
                                                        >
                                                            <FormControl>
                                                                <SelectTrigger
                                                                    className={`bg-white ${fieldState.error ? "border-red-500" : ""}`}
                                                                >
                                                                    <SelectValue placeholder="Select status" />
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                {field.value && (
                                                                    <Button
                                                                        variant="ghost"
                                                                        className="mt-2 w-full justify-center text-sm"
                                                                        onClick={(e) => {
                                                                            e.preventDefault();
                                                                            field.onChange("");
                                                                            form.setValue("liabilityStatus", undefined);
                                                                        }}
                                                                    >
                                                                        Clear Selection
                                                                    </Button>
                                                                )}
                                                                {Object.values(LiabilityStatus).map(
                                                                    (status) => (
                                                                        <SelectItem key={status} value={status}>
                                                                            {status}
                                                                        </SelectItem>
                                                                    )
                                                                )}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="liabilityType"
                                                render={({ field, fieldState }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-xs text-gray-500">
                                                            Type
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                {...field}
                                                                placeholder="Enter liability type"
                                                                disabled={noInsurance}
                                                                className={`bg-white ${fieldState.error ? "border-red-500" : ""
                                                                    }`}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="liabilityPercent"
                                                render={({ field, fieldState }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-xs text-gray-500">
                                                            Percentage
                                                        </FormLabel>
                                                        <Select
                                                            onValueChange={field.onChange}
                                                            value={field.value || "100"}
                                                            disabled={noInsurance}
                                                        >
                                                            <FormControl>
                                                                <SelectTrigger
                                                                    className={`bg-white ${fieldState.error ? "border-red-500" : ""
                                                                        }`}
                                                                >
                                                                    <SelectValue placeholder="Select percentage">
                                                                        {field.value
                                                                            ? `${field.value}%`
                                                                            : "Select percentage"}
                                                                    </SelectValue>
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                {[...Array(101)].map((_, i) => (
                                                                    <SelectItem key={i} value={`${i}`}>
                                                                        {`${i}%`}
                                                                    </SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    </div>

                                    <FormField
                                        control={form.control}
                                        name="policyType"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Policy Type</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value || ""}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger
                                                            className={`bg-white ${fieldState.error ? "border-red-500" : ""}`}
                                                        >
                                                            <SelectValue placeholder="Select policy type" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                    form.setValue("policyType", undefined);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {Object.values(PolicyType).map((type) => (
                                                            <SelectItem key={type} value={type}>
                                                                {type}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="stacked"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Stacked</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value || ""}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger
                                                            className={`bg-white ${fieldState.error ? "border-red-500" : ""}`}
                                                        >
                                                            <SelectValue placeholder="Select stacked" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                    form.setValue("stacked", undefined);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {Object.values(StackedStatus).map((status) => (
                                                            <SelectItem key={status} value={status}>
                                                                {status}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="vehicles"
                                        render={({ field, fieldState }) => (
                                            <FormItem>
                                                <FormLabel>Vehicles</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    disabled={noInsurance}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger
                                                            className={`bg-white ${fieldState.error ? "border-red-500" : ""
                                                                }`}
                                                        >
                                                            <SelectValue placeholder="Select vehicles" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {[...Array(10)].map((_, i) => (
                                                            <SelectItem
                                                                key={i + 1}
                                                                value={(i + 1).toString()}
                                                            >
                                                                {i + 1}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {fieldState.error && (
                                                    <div className="text-sm text-red-500 mt-1">
                                                        {fieldState.error.message}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="claimNote"
                                        render={({ field }) => (
                                            <FormItem className="col-span-2">
                                                <FormLabel>Claim Note</FormLabel>
                                                <FormControl>
                                                    <div className="bg-white border rounded-md">
                                                        <CustomCKEditor
                                                            initialValue={field.value ?? ''}
                                                            onChange={(value: string) => {
                                                                field.onChange(value);
                                                            }}
                                                            placeholder="Enter claim notes here..."
                                                            minHeight="100px"
                                                            className="min-h-[100px] resize-none bg-gray-50"
                                                        />
                                                    </div>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end gap-4 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsSyncDialogOpen(true)}
                                    disabled={isSyncing}
                                >
                                    Sync Linked Cases
                                </Button>
                                <Button
                                    type="button"
                                    onClick={(e) => {
                                        form.handleSubmit((values) => onSubmit(values))(e);
                                    }}
                                >
                                    {isEdit ? "Update" : "Create"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />

            <SyncCaseDialog
                isOpen={isSyncDialogOpen}
                onClose={() => setIsSyncDialogOpen(false)}
                onSync={handleSync}
                isSyncing={isSyncing}
                caseId={caseId}
                syncType="client-insurance"
            />
        </>
    );
};

export default EditInsuranceDetailsDialog;
