import { FC } from 'react';
import { useInsuranceLegalRepresentationQuery } from '@/services/case-management/clientDetailService';
import { useDefendantInsuranceLegalRepresentationQuery } from '@/services/case-management/defendantService';
import { AttorneyResponse, LawFirmResponse } from '@/type/case-management/orgTypes';
import { PhoneLink } from '@/components/ui/phone-link';
import { MailLink } from '@/components/ui/mail-link';
import { AddressLink } from '@/components/gMap/address-link';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { InfoFieldGroup } from '@/components/ui/InfoField';
import { useParams } from "next/navigation";
import { Separator } from '@/components/ui/separator';

interface LegalRepresentationProps {
    caseId: string;
    insuranceId: string;
    defendantId?: string;
}

interface EntityCardProps {
    lawFirm: LawFirmResponse | null;
    attorney: AttorneyResponse | null;
    caseId: string;
}

const EntityCard: FC<EntityCardProps> = ({ lawFirm, attorney, caseId }) => {
    if (!lawFirm && !attorney) return null;

    return (
        <div className="space-y-6">
            {lawFirm && (
                <div className="space-y-6">
                    <InfoFieldGroup
                        fields={[
                            { label: "Law Firm", value: lawFirm.office_name || '—' },
                            { label: "Phone", value: lawFirm.phone, isPhone: true },
                            { label: "Cell", value: lawFirm.cell, isPhone: true },
                            { label: "Fax", value: lawFirm.fax, isPhone: true },
                            { label: "Email", value: lawFirm.email, isMail: true },
                            {
                                label: "Address", value: <AddressLink address={{
                                    street1: lawFirm.street1 || undefined,
                                    street2: lawFirm.street2 || undefined,
                                    city: lawFirm.city || undefined,
                                    state: lawFirm.state || undefined,
                                    zip_code: lawFirm.zip_code || undefined
                                }} />
                            }
                        ]}
                    />
                </div>
            )}

            {attorney && (
                <>
                    {lawFirm && <Separator className="my-6 bg-gray-200" />}
                    <div className="space-y-6">
                        <InfoFieldGroup
                            fields={[
                                { 
                                    label: "Attorney Name", 
                                    value: `${attorney.first_name} ${attorney.last_name}`.trim() || '—'
                                },
                                { label: "Phone", value: attorney.phone, isPhone: true },
                                { label: "Cell", value: attorney.cell, isPhone: true },
                                { label: "Fax", value: attorney.fax, isPhone: true },
                                { label: "Email", value: attorney.email, isMail: true },
                                {
                                    label: "Address", value: <AddressLink address={{
                                        street1: attorney.street1 || undefined,
                                        street2: attorney.street2 || undefined,
                                        city: attorney.city || undefined,
                                        state: attorney.state || undefined,
                                        zip_code: attorney.zip_code || undefined
                                    }} />
                                }
                            ]}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

const LegalRepresentation: FC<LegalRepresentationProps> = ({
    caseId,
    insuranceId,
    defendantId,
}) => {
    const { data: clientLegalRep, isLoading: isClientLoading } = useInsuranceLegalRepresentationQuery(
        caseId,
        insuranceId,
        { enabled: !defendantId }
    );
    const { data: defendantLegalRep, isLoading: isDefendantLoading } = useDefendantInsuranceLegalRepresentationQuery(
        caseId,
        defendantId || '',
        insuranceId,
        { enabled: !!defendantId }
    );

    const legalRepresentation = defendantId ? defendantLegalRep : clientLegalRep;
    const isLoading = defendantId ? isDefendantLoading : isClientLoading;

    // If caseId is not passed as prop, get it from the URL params
    const params = useParams<{ caseId: string }>();
    const effectiveCaseId = caseId || params?.caseId || '';

    if (isLoading) return <LoadingSpinner />;
    if (!legalRepresentation) return null;

    const { law_firm, attorney, co_counsel_law_firm, co_counsel_attorney } = legalRepresentation;

    const sections = [
        {
            id: 'law-firm',
            label: 'Law Firm',
            lawFirm: law_firm,
            attorney: attorney
        },
        {
            id: 'co-counsel',
            label: 'Co-Counsel Law Firm',
            lawFirm: co_counsel_law_firm,
            attorney: co_counsel_attorney
        }
    ];

    const availableTabs = sections.filter(section => section.lawFirm || section.attorney);

    if (availableTabs.length === 0) {
        return (
            <div className="p-6 text-center">
                <p className="text-sm text-[#060216]/70">No legal representation information available</p>
            </div>
        );
    }

    return (
        <div className="pb-4">
            <Tabs defaultValue={availableTabs[0].id} className="w-full">
                <div className="relative w-full">
                    <div className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white to-transparent pointer-events-none" />
                    <div className="absolute left-0 top-0 h-full w-8 bg-gradient-to-r from-white to-transparent pointer-events-none" />
                    <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                        <TabsList className="w-full">
                            {sections.map(tab => (
                                <TabsTrigger
                                    key={tab.id}
                                    value={tab.id}
                                    disabled={!tab.lawFirm && !tab.attorney}
                                    className={`${!tab.lawFirm && !tab.attorney ? 'hover:no-underline hover:text-muted-foreground cursor-not-allowed' : ''}`}
                                >
                                    {tab.label}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                    </div>
                </div>

                <div className="mt-6">
                    {sections.map(section => (
                        <TabsContent key={section.id} value={section.id}>
                            <EntityCard
                                lawFirm={section.lawFirm}
                                attorney={section.attorney}
                                caseId={effectiveCaseId}
                            />
                        </TabsContent>
                    ))}
                </div>
            </Tabs>
        </div>
    );
};

export default LegalRepresentation; 