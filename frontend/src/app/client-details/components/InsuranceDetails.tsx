'use client';

import { <PERSON>r<PERSON><PERSON><PERSON>, Shield, Trash2, InfoIcon } from "lucide-react";
import { LoadingSpinner } from "@/components/DocumentSidebar/components/LoadingSpinner";
import { useClientInsuranceListQuery, useDeleteClientInsuranceMutation } from '@/services/case-management/clientDetailService';
import Adjuster from "./Adjuster";
import LegalRepresentation from "./LegalRepresentation";
import AddEditInsuranceAdjusterDialog from "./AddEditInsuranceAdjusterDialog";
import AddEditInsuranceLegalRepresentation from "./AddEditInsuranceLegalRepresentation";
import InsuranceDetailsEditDialog from "./InsuranceDetailsEditDialog";
import { ClientInsurance } from "@/type/case-management/clientDetailTypes";
import { useState } from "react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { CopyNumber } from "@/components/ui/copy-number";
import { PhoneLink } from "@/components/ui/phone-link";
import { MailLink } from "@/components/ui/mail-link";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import { useParams } from "next/navigation";
import CKViewer from "@/components/ckeditor/CKViewer";

interface InsuranceDetailsProps {
    caseId: string;
}

export default function InsuranceDetails({ caseId }: InsuranceDetailsProps) {
    const { data: clientInsuranceList, isLoading: isClientLoading } = useClientInsuranceListQuery(caseId);
    // const params = useParams<{ caseId: string }>();
    // const urlCaseId = params?.caseId || caseId;

    if (isClientLoading) {
        return <LoadingSpinner />;
    }

    if (!clientInsuranceList || clientInsuranceList.length === 0) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div className="flex items-center gap-3">
                    <Shield className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-[#060216]">No insurance information available</p>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col gap-6">
            <Card className="border-none p-0">
                <CardContent className="p-0">
                    {clientInsuranceList.map((insurance: ClientInsurance, index: number) => (
                        <>
                            <div className="flex items-end justify-end">
                                <div className="flex items-center gap-2">
                                    <InsuranceDetailsEditDialog
                                        isEdit={true}
                                        caseId={caseId}
                                        insurance={insurance}
                                    />
                                    <DeleteInsuranceButton caseId={caseId} insuranceId={insurance.id.toString()} />
                                    <ManageEmailTemplate
                                        caseId={caseId}
                                        contextType={"client_insurance" as TemplateContextType}
                                        clientInsuranceId={insurance.id.toString()}
                                    />
                                </div>
                            </div>
                            <div key={insurance.id} className="bg-white p-3">
                                {insurance.no_insurance ? (
                                    <div>
                                        <p className="text-sm text-[#060216]/70 ml-7 mb-6">
                                            Client has confirmed they do not have insurance
                                        </p>
                                        <div className="bg-orange-50 p-6 rounded-lg">
                                            <div className="flex items-center gap-3 text-orange-700">
                                                <InfoIcon className="w-5 h-5" />
                                                <p className="text-sm">
                                                    This record indicates the client has no insurance. No additional insurance details are required.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        <div className="flex flex-col gap-8">
                                            <InfoFieldGroup
                                                fields={[
                                                    {
                                                        label: "Company",
                                                        value: insurance.insurance_company?.name || '—'
                                                    },
                                                    {
                                                        label: "Company Phone",
                                                        value: <PhoneLink phone={insurance.insurance_company?.phone} />
                                                    },
                                                    {
                                                        label: "Company Fax",
                                                        value: <PhoneLink phone={insurance.insurance_company?.fax} />
                                                    },
                                                    {
                                                        label: "Company Email",
                                                        value: <MailLink email={insurance.insurance_company?.email} caseId={caseId} />
                                                    },
                                                    {
                                                        label: "Plan Type",
                                                        value: insurance.plan_type || '—'
                                                    },
                                                    {
                                                        label: "Claim Number",
                                                        value: insurance.claim_number ? <CopyNumber value={insurance.claim_number} /> : '—'
                                                    },
                                                    {
                                                        label: "Policy Number",
                                                        value: insurance.policy_number ? <CopyNumber value={insurance.policy_number} /> : '—'
                                                    },
                                                    {
                                                        label: "Medpay Claim Number",
                                                        value: insurance.medpay_claim_number ? <CopyNumber value={insurance.medpay_claim_number} /> : '—'
                                                    },
                                                    {
                                                        label: "Insured",
                                                        value: insurance.insured_name || '—'
                                                    },
                                                    {
                                                        label: "UM/UIM",
                                                        value: insurance.um_uim || '—'
                                                    },
                                                    {
                                                        label: "Medpay",
                                                        value: insurance.medpay || '—'
                                                    },
                                                    {
                                                        label: "PIP",
                                                        value: insurance.pip || '—'
                                                    },
                                                    {
                                                        label: "Deductible",
                                                        value: insurance.deductible || '—'
                                                    },
                                                    {
                                                        label: "Coverage Status",
                                                        value: insurance.coverage_status || '—'
                                                    },
                                                    {
                                                        label: "Liability Status",
                                                        value: insurance.liability_status || '—'
                                                    },
                                                    {
                                                        label: "Liability Type",
                                                        value: insurance.liability_type || '—'
                                                    },
                                                    {
                                                        label: "Liability Percentage",
                                                        value: insurance.liability_percentage ? `${insurance.liability_percentage}%` : '—'
                                                    },
                                                    {
                                                        label: "Policy Type",
                                                        value: insurance.policy_type || '—'
                                                    },
                                                    {
                                                        label: "Stacked",
                                                        value: insurance.stacked || '—'
                                                    },
                                                    {
                                                        label: "Vehicles",
                                                        value: insurance.vehicles || '—'
                                                    },
                                                ]}
                                            />
                                        </div>

                                        {/* Claim Notes Section */}
                                        <div className="bg-[#F7F7F7] p-2 rounded-lg border border-[#00000014] mt-4">
                                            <div className="flex flex-col px-2 py-2">
                                                <span className="text-[#06021699] text-xs leading-[21px] font-medium font-Manrope mb-[2px]">
                                                    Claim Notes
                                                </span>
                                                <div className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
                                                    {insurance.claim_note ? <CKViewer content={insurance.claim_note} /> : "—"}
                                                </div>
                                            </div>
                                        </div>

                                        <Separator className="w-full my-6" />
                                        <div className="mt-6">
                                            <div className="flex items-center justify-between mb-4">
                                                <h2 className="text-[#060216] text-base leading-5 tracking-[0.15px] font-Manrope w-full flex flex-row items-center gap-2"
                                                    style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                                                >
                                                    <UserCircle className="h-4 w-4 text-[#060216]" />
                                                    <h2 className="text-base font-semibold leading-5 tracking-[0.15px]">Adjuster</h2>
                                                </h2>
                                                <AddEditInsuranceAdjusterDialog
                                                    caseId={caseId}
                                                    insuranceId={insurance.id.toString()}
                                                    insuranceCompanyId={insurance.insurance_company?.id?.toString() || ""}
                                                />
                                            </div>
                                            <Adjuster
                                                clientId={caseId}
                                                insuranceId={insurance.id.toString()}
                                            />
                                        </div>

                                        <div className="mt-6">
                                            <div className="flex items-center justify-between mb-4">
                                                <h2 className="text-[#060216] text-base leading-5 tracking-[0.15px] font-Manrope w-full flex flex-row items-center gap-2"
                                                    style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                                                >
                                                    <span className='text-base font-semibold leading-5 tracking-[0.15px]'>Legal Representation</span>
                                                </h2>
                                                <AddEditInsuranceLegalRepresentation
                                                    caseId={caseId}
                                                    insuranceId={insurance.id.toString()}
                                                />
                                            </div>
                                            <LegalRepresentation
                                                caseId={caseId}
                                                insuranceId={insurance.id.toString()}
                                            />
                                        </div>
                                    </>
                                )}
                                {index < clientInsuranceList.length - 1 && (
                                    <Separator className="my-6" />
                                )}
                            </div>
                        </>
                    ))}
                </CardContent>
            </Card>
        </div>
    );
}

function DeleteInsuranceButton({ caseId, insuranceId }: { caseId: string; insuranceId: string }) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const deleteInsurance = useDeleteClientInsuranceMutation();

    const handleDelete = () => {
        deleteInsurance.mutate({ caseId, insuranceId });
    };

    return (
        <>
            <button
                onClick={() => setIsDeleteDialogOpen(true)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title="Delete insurance"
            >
                <Trash2 className="h-4 w-4 text-red-500" />
            </button>
            <DeleteConfirmationDialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
                onConfirm={handleDelete}
                text="Insurance"
            />
        </>
    );
}