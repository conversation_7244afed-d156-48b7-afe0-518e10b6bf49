'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Footer,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useState, useEffect } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { useCreateDefendantPropertyDamageMutation, useUpdateDefendantPropertyDamageMutation } from '@/services/case-management/defendantService';
import { DefendantPropertyDamage } from "@/type/case-management/defendantTypes";
import { Loader2 } from "lucide-react";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import { useInsuranceCompaniesQuery } from "@/services/orgAPIs";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

const propertyDamageFormSchema = z.object({
    insurance: z.string().optional(),
    damage: z.string().min(1, "Damage type is required"),
    frameDamage: z.boolean().default(false),
    totalLoss: z.boolean().default(false),
    registeredOwner: z.string().optional(),
    autoBodyShop: z.string().optional(),
    estimate: z.string()
        .refine((val) => !val || /^\d*\.?\d*$/.test(val), {
            message: "Must be a valid number"
        })
        .optional(),
    final: z.string()
        .refine((val) => !val || /^\d*\.?\d*$/.test(val), {
            message: "Must be a valid number"
        })
        .optional(),
    make: z.string().optional(),
    model: z.string().optional(),
    color: z.string().optional(),
    year: z.string()
        .refine((val) => !val || /^\d{4}$/.test(val), {
            message: "Year must be a 4-digit number"
        })
        .optional(),
    plate: z.string()
        .regex(/^[A-Z0-9]*$/, "Plate number can only contain uppercase letters and numbers")
        .max(10, "Plate number cannot exceed 10 characters")
        .optional()
        .transform(val => val?.toUpperCase()),
    vin: z.string()
        .regex(/^[A-HJ-NPR-Z0-9]*$/, "VIN can only contain uppercase letters (except I, O, Q) and numbers")
        .refine(val => val === '' || val.length === 17, "VIN must be empty or exactly 17 characters")
        .optional()
        .transform(val => val?.toUpperCase()),
    mileage: z.string()
        .refine((val) => !val || /^\d*$/.test(val), {
            message: "Mileage must be a number"
        })
        .optional(),
    note: z.string().optional().nullable(),
});

type PropertyDamageFormValues = z.infer<typeof propertyDamageFormSchema>;

interface AddEditDefendantPropertyDamageProps {
    isEdit?: boolean;
    children: React.ReactNode;
    caseId: string;
    defendantId: string;
    propertyDamage?: DefendantPropertyDamage;
    onSuccess?: () => void;
    onOpenChange?: (open: boolean) => void;
}

const damageTypes = [
    "Major",
    "Minor",
    "Moderate"
] as const;

const YEARS = Array.from({ length: 131 }, (_, i) => (1900 + i).toString()).filter(year =>
    parseInt(year) <= 2030
);

export function AddEditDefendantPropertyDamage({
    isEdit = false,
    children,
    onSuccess,
    // onOpenChange,
    caseId,
    defendantId,
    propertyDamage
}: AddEditDefendantPropertyDamageProps) {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { data: insuranceCompanies, isLoading: insuranceLoading } = useInsuranceCompaniesQuery();

    const createPropertyDamage = useCreateDefendantPropertyDamageMutation(caseId, defendantId);
    const updatePropertyDamage = useUpdateDefendantPropertyDamageMutation(caseId, defendantId);

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty && form.formState.isSubmitSuccessful === false) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(true);
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        form.reset();
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const form = useForm<PropertyDamageFormValues>({
        resolver: zodResolver(propertyDamageFormSchema),
        defaultValues: {
            insurance: "",
            damage: "",
            frameDamage: false,
            totalLoss: false,
            registeredOwner: "",
            autoBodyShop: "",
            estimate: "",
            final: "",
            make: "",
            model: "",
            color: "",
            year: "",
            plate: "",
            vin: "",
            mileage: "",
            note: "",
        },
    });

    useEffect(() => {
        if (isEdit && propertyDamage) {
            form.reset({
                insurance: propertyDamage.insurance_company?.id?.toString() || "",
                damage: propertyDamage.damage || "",
                frameDamage: propertyDamage.frame_damage || false,
                totalLoss: propertyDamage.total_loss || false,
                registeredOwner: propertyDamage.registered_owner || "",
                autoBodyShop: propertyDamage.auto_body_shop || "",
                estimate: propertyDamage.estimate?.toString() || "",
                final: propertyDamage.final?.toString() || "",
                make: propertyDamage.make || "",
                model: propertyDamage.model || "",
                color: propertyDamage.color || "",
                year: propertyDamage.year?.toString() || "",
                plate: propertyDamage.plate || "",
                vin: propertyDamage.vin || "",
                mileage: propertyDamage.mileage?.toString() || "",
                note: propertyDamage.note || "",
            });
        }
    }, [isEdit, propertyDamage, form]);

    async function onSubmit(values: PropertyDamageFormValues) {
        try {
            setIsSubmitting(true);
            const requestData = {
                insurance_company: values.insurance || null,
                damage: values.damage,
                frame_damage: values.frameDamage,
                total_loss: values.totalLoss,
                registered_owner: values.registeredOwner || null,
                auto_body_shop: values.autoBodyShop || null,
                estimate: values.estimate || null,
                final: values.final || null,
                make: values.make || null,
                model: values.model || null,
                color: values.color || null,
                year: values.year || null,
                plate: values.plate || null,
                vin: values.vin || null,
                mileage: values.mileage || null,
                note: values.note || "",
            };

            if (isEdit) {
                await updatePropertyDamage.mutateAsync(requestData);
            } else {
                await createPropertyDamage.mutateAsync(requestData);
            }

            // Reset form with current values and close dialog
            form.reset(form.getValues());
            setOpen(false);
            onSuccess?.();
        } catch (error) {
            console.error(`Failed to ${isEdit ? 'update' : 'create'} property damage:`, error);
        } finally {
            setIsSubmitting(false);
        }
    }

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {children}
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? 'Edit Property Damage' : 'Add Property Damage'}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="max-h-[80vh] overflow-y-auto pr-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <div className="grid grid-cols-3 gap-4">
                                    {/* Insurance Company */}
                                    <FormField
                                        control={form.control}
                                        name="insurance"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Insurance Company</FormLabel>
                                                <Select onValueChange={field.onChange} value={field.value || ""}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select insurance company" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mb-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {insuranceLoading ? (
                                                            <SelectItem value="loading">Loading...</SelectItem>
                                                        ) : (
                                                            insuranceCompanies?.map((company) => (
                                                                <SelectItem key={company.id} value={company.id.toString()}>
                                                                    {company.name}
                                                                </SelectItem>
                                                            ))
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Damage Type */}
                                    <FormField
                                        control={form.control}
                                        name="damage"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Damage *</FormLabel>
                                                <Select onValueChange={field.onChange} value={field.value || ""}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select damage type" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mb-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {damageTypes.map((type) => (
                                                            <SelectItem key={type} value={type}>
                                                                {type}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Frame Damage Checkbox */}
                                    <FormField
                                        control={form.control}
                                        name="frameDamage"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-center space-x-3 space-y-0 pt-6">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                                <FormLabel>Frame Damage</FormLabel>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Total Loss Checkbox */}
                                    <FormField
                                        control={form.control}
                                        name="totalLoss"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-center space-x-3 space-y-0 pt-6">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                                <FormLabel>Total Loss</FormLabel>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-3 gap-4 mt-4">
                                    {/* Registered Owner */}
                                    <FormField
                                        control={form.control}
                                        name="registeredOwner"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Registered Owner</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Auto Body Shop */}
                                    <FormField
                                        control={form.control}
                                        name="autoBodyShop"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Auto Body Shop</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Estimate */}
                                    <FormField
                                        control={form.control}
                                        name="estimate"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Estimate</FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="number" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Final */}
                                    <FormField
                                        control={form.control}
                                        name="final"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Final</FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="number" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Make */}
                                    <FormField
                                        control={form.control}
                                        name="make"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Make</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Model */}
                                    <FormField
                                        control={form.control}
                                        name="model"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Model</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Color */}
                                    <FormField
                                        control={form.control}
                                        name="color"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Color</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Year */}
                                    <FormField
                                        control={form.control}
                                        name="year"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Year</FormLabel>
                                                <Select onValueChange={field.onChange} value={field.value || ""}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select year" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mb-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange("");
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {YEARS.map((year) => (
                                                            <SelectItem key={year} value={year}>
                                                                {year}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Plate */}
                                    <FormField
                                        control={form.control}
                                        name="plate"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Plate #</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        {...field}
                                                        maxLength={10}
                                                        value={field.value?.toUpperCase()}
                                                        onChange={e => field.onChange(e.target.value.toUpperCase())}
                                                        placeholder="ABC123"
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* VIN */}
                                    <FormField
                                        control={form.control}
                                        name="vin"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>VIN #</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        {...field}
                                                        maxLength={17}
                                                        value={field.value?.toUpperCase()}
                                                        onChange={e => field.onChange(e.target.value.toUpperCase())}
                                                        placeholder="1HGCM82633A123456"
                                                    />
                                                </FormControl>
                                                <p className="text-xs text-muted-foreground">
                                                    17 characters, letters (except I,O,Q) and numbers only
                                                </p>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Mileage */}
                                    <FormField
                                        control={form.control}
                                        name="mileage"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Mileage</FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="number" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                {/* Note */}
                                <div className="mt-4">
                                    <FormField
                                        control={form.control}
                                        name="note"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Note</FormLabel>
                                                <FormControl>
                                                    <div className="bg-white border rounded-md">
                                                        <CustomCKEditor
                                                            initialValue={field.value || ''}
                                                            onChange={(value: string) => field.onChange(value)}
                                                            placeholder="Take a note..."
                                                            minHeight="100px"
                                                            className="min-h-[100px] resize-none bg-gray-50"
                                                        />
                                                    </div>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </div>

                            <DialogFooter>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleDiscardChanges}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="button"
                                    onClick={(e) => {
                                        form.handleSubmit((values) => onSubmit(values))(e);
                                    }}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            {isEdit ? 'Updating...' : 'Creating...'}
                                        </>
                                    ) : (
                                        isEdit ? 'Save Changes' : 'Add'
                                    )}
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    );
} 