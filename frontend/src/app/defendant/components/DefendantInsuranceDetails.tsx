"use client";

import * as React from "react";
import {
  Shield,
  Trash2,
  UserCircle,
} from "lucide-react";
import { LoadingSpinner } from "@/components/DocumentSidebar/components/LoadingSpinner";
import {
  useDefendantInsuranceListQuery,
  useDeleteDefendantInsuranceMutation,
} from "@/services/case-management/defendantService";
// import Adjuster from "@/app/client-details/components/Adjuster";
// import LegalRepresentation from "@/app/client-details/components/LegalRepresentation";
// import AddEditInsuranceAdjusterDialog from "@/app/client-details/components/AddEditInsuranceAdjusterDialog";
// import AddEditInsuranceLegalRepresentation from "@/app/client-details/components/AddEditInsuranceLegalRepresentation";
import DefendantInsuranceDetailsEditDialog from "./DefendantInsuranceDetailsEditDialog";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { useState } from "react";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  // Remove these unused imports
  // Building2,
  // Hash,
  // Percent,
  // CheckCircle2,
  // DollarSign,
  // FileText,
  // Phone,
  // Printer,
  // Mail,
  // Keep any other imports that are being used
} from '@/components/ui/button';
import AddEditInsuranceAdjusterDialog from "@/app/client-details/components/AddEditInsuranceAdjusterDialog";
import Adjuster from "@/app/client-details/components/Adjuster";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import CKViewer from "@/components/ckeditor/CKViewer";


interface DefendantInsuranceDetailsProps {
  caseId: string;
  defendantId: string;
}

export default function DefendantInsuranceDetails({
  caseId,
  defendantId,
}: DefendantInsuranceDetailsProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedInsuranceId, setSelectedInsuranceId] = useState<string | null>(null);

  const { data: insuranceList, isLoading } = useDefendantInsuranceListQuery(
    caseId,
    defendantId
  );

  const deleteInsuranceMutation = useDeleteDefendantInsuranceMutation(
    caseId,
    defendantId
  );

  const handleDeleteInsurance = (insuranceId: string) => {
    setSelectedInsuranceId(insuranceId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (selectedInsuranceId) {
      deleteInsuranceMutation.mutate(selectedInsuranceId);
      setDeleteDialogOpen(false);
      setSelectedInsuranceId(null);
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        text="Defendant Insurance"
      />

      <div className="flex flex-col">
        <Card>
          <CardContent>
            {(!insuranceList || insuranceList.length === 0) ? (
              <div className="bg-white flex justify-center items-center w-full p-6">
                <div className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-gray-400" />
                  <p className="text-sm text-[#060216]">No insurance information available</p>
                </div>
              </div>
            ) : (
              insuranceList.map((insurance, index) => (
                <div key={insurance.id} className="space-y-6">
                  {index > 0 && <Separator className="my-6" />}

                  <div className="flex items-center justify-between gap-2 bg-[#F7F7F7] rounded-lg px-4">
                    <div>
                      <h2 className="text-lg font-bold text-[#060216] flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        {insurance.insurance_company?.name || "No Insurance Company"}
                      </h2>
                    </div>
                    <div className="flex items-center gap-2">
                      <DefendantInsuranceDetailsEditDialog
                        isEdit={true}
                        caseId={caseId}
                        defendantId={defendantId}
                        insurance={insurance}
                      />
                      <button
                        onClick={() => handleDeleteInsurance(insurance.id.toString())}
                        className="p-2 hover:bg-red-50 rounded-full transition-colors"
                        title="Delete insurance"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </button>
                      <ManageEmailTemplate
                        caseId={caseId}
                        defendantId={defendantId}
                        contextType={"defendant_insurance" as TemplateContextType}
                        defendantInsuranceId={insurance.id.toString()}
                      />
                    </div>
                  </div>

                  {insurance.no_insurance ? (
                    <p className="text-sm text-[#060216]/70 ml-7 mb-6">
                      Client has confirmed they do not have defendant insurance
                    </p>
                  ) : (
                    <>
                      <div className="space-y-[57px]">
                        <InfoFieldGroup
                          caseId={caseId}
                          fields={[
                            {
                              label: "Phone",
                              value: insurance.insurance_company?.phone || "—",
                              isPhone: true
                            },
                            {
                              label: "Cell",
                              value: insurance.insurance_company?.cell || "—",
                              isPhone: true
                            },
                            {
                              label: "Fax",
                              value: insurance.insurance_company?.fax || "—",
                              isPhone: true
                            },
                            {
                              label: "Email",
                              value: insurance.insurance_company?.email || "—",
                              isMail: true
                            },
                            {
                              label: "Claim Number",
                              value: insurance.claim_number || "—",
                              isNumber: true  
                            },
                            {
                              label: "Policy Number",
                              value: insurance.policy_number || "—",
                              isNumber: true
                            },
                            {
                              label: "Policy Limits",
                              value: insurance.policy_limits || "—"
                            },
                            {
                              label: "Liability Status",
                              value: insurance.liability_status || "—"
                            },
                            {
                              label: "Liability Percentage",
                              value: insurance.liability_percentage || "—"
                            },
                            {
                              label: "Coverage Status",
                              value: insurance.coverage_status || "—"
                            },
                            {
                              label: "Med Pay",
                              value: insurance.med_pay || "—"
                            }
                          ].concat(insurance.umbrella_policy ? [{
                            label: "Umbrella Policy",
                            value: `#${insurance.policy_number}`
                          }] : [])}
                        />

                        {insurance.claim_note && (
                          <div className="bg-[#F7F7F7] p-6 rounded-lg border border-[#000000]/[0.08]">
                            <div className="flex flex-col gap-[2px]">
                              <span className="text-[14px] text-[#060216]/70 font-medium">
                                Claim Note
                              </span>
                              <div className="text-[14px] text-[#060216] font-semibold">
                                <CKViewer content={insurance.claim_note} />
                              </div>
                            </div>
                          </div>
                        )}

                      </div>
                      <Separator className="w-full" />
                      <Card className="border-none p-0">
                        <CardContent className="p-0">
                          <div className="space-y-6">
                            <div className="flex items-center justify-between mb-4">
                              <h2 className="text-[#060216] text-base leading-5 tracking-[0.15px] font-Manrope flex items-center gap-2"
                                style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                              >
                                <UserCircle className="h-4 w-4 text-[#060216]" />
                                <span className='text-base font-semibold leading-5 tracking-[0.15px]'>Adjuster</span>
                              </h2>
                              <AddEditInsuranceAdjusterDialog
                                caseId={caseId}
                                insuranceId={insurance.id.toString()}
                                insuranceCompanyId={insurance.insurance_company?.id.toString() || ""}
                                defendantId={defendantId}
                              />
                            </div>
                            <div className="text-[#060216]">
                              <Adjuster
                                clientId={caseId}
                                insuranceId={insurance.id.toString()}
                                defendantId={defendantId}
                              />
                            </div>
                            {/* <div>
                                <div className="flex items-center justify-between mb-4">
                                  <h2
                                    className="text-[#060216] text-base leading-5 tracking-[0.15px] font-Manrope flex items-center gap-2"
                                    style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                                  >
                                    <span className="text-base font-semibold leading-5 tracking-[0.15px]">
                                      Legal Representation
                                    </span>
                                  </h2>

                                  <AddEditInsuranceLegalRepresentation
                                    caseId={caseId}
                                    insuranceId={insurance.id.toString()}
                                    defendantId={defendantId}
                                  />
                                </div>
                                <div className="text-[#060216]">
                                  <LegalRepresentation
                                    caseId={caseId}
                                    insuranceId={insurance.id.toString()}
                                    defendantId={defendantId}
                                  />
                                </div>
                              </div> */}
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  )}
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
