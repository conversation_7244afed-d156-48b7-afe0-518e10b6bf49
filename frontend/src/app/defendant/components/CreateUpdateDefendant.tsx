import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  Di<PERSON>Trigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Edit, Plus } from "lucide-react";
import {
  useCreateDefendantWithInsuranceMutation,
  useUpdateDefendantMutation,
} from "@/services/case-management/defendantService";
import { USStatesLabels } from "@/constants/commont";
// import { useInsuranceCompaniesQuery } from "@/services/orgAPIs";
import { PhoneNumberInput } from "@/components/ui/phone-number-input";
import { format, parse, startOfDay } from "date-fns";
import { DefendantDetailResponse } from "@/type/case-management/defendantTypes";
import { DefendantType } from "@/type/case-management/defendantTypes";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { useMatchingCases } from "@/hooks/useMatchingCases";
import { MatchingCasesModal } from "@/components/MatchingCasesModal";
import { RequiredLabel } from "@/components/ui/required-label";
import CustomCKEditor from "@/components/ckeditor/CKEditor";
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";
import { useDefendantSyncMutation } from "@/services/case-management/caseSyncService";
// import { useQueryClient } from "@tanstack/react-query";

const DRIVER_LICENSE_STATES = [
  "AK",
  "AL",
  "AR",
  "AZ",
  "CA",
  "CO",
  "CT",
  "DC",
  "DE",
  "FL",
  "GA",
  "HI",
  "IA",
  "ID",
  "IL",
  "IN",
  "KS",
  "KY",
  "LA",
  "MA",
  "MD",
  "ME",
  "MI",
  "MN",
  "MO",
  "MS",
  "MT",
  "NC",
  "ND",
  "NE",
  "NH",
  "NJ",
  "NM",
  "NV",
  "NY",
  "OH",
  "OK",
  "OR",
  "PA",
  "RI",
  "SC",
  "SD",
  "TN",
  "TX",
  "UT",
  "VA",
  "VT",
  "WA",
  "WI",
  "WV",
  "WY",
] as const;

const formatSSN = (value: string) => {
  // Remove all non-digits
  const digits = value.replace(/\D/g, "");

  // Format with dashes
  if (digits.length <= 3) {
    return digits;
  } else if (digits.length <= 5) {
    return `${digits.slice(0, 3)}-${digits.slice(3)}`;
  } else {
    return `${digits.slice(0, 3)}-${digits.slice(3, 5)}-${digits.slice(5, 9)}`;
  }
};

const formSchema = z.object({
  defendantType: z.nativeEnum(DefendantType, {
    required_error: "Defendant type is required",
  }),
  companyEntity: z.string().optional(),
  description: z.string().optional().nullable(),
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  registeredAgent: z.string().optional(),
  gender: z.enum(["Male", "Female", "Non-binary"]).optional(),
  socialSecurityNumber: z
    .string()
    .regex(
      /^\d{3}-\d{2}-\d{4}$/,
      "Please enter a valid SSN (e.g., ***********)"
    )
    .optional()
    .or(z.literal("")),
  dateOfBirth: z.date().optional(),
  driverLicense: z
    .object({
      type: z.enum(DRIVER_LICENSE_STATES).optional(),
      number: z
        .string()
        .regex(/^[A-Z0-9]+$/, "Please enter a valid license number")
        .optional()
        .or(z.literal("")),
    })
    .optional(),
  language: z
    .enum(
      [
        "ENGLISH",
        "ARABIC",
        "ARMENIAN",
        "BENGALI",
        "CANTONESE",
        "CREOLE",
        "FRENCH",
        "FRENCH_CREOLE",
        "GERMAN",
        "GREEK",
        "GUJARATI",
        "HEBREW",
        "HINDI",
        "HMONG",
        "ITALIAN",
        "JAPANESE",
        "KOREAN",
        "MANDARIN",
        "PERSIAN",
        "POLISH",
        "PORTUGUESE",
        "PUNJABI",
        "RUSSIAN",
        "SPANISH",
        "TAGALOG",
        "TELUGU",
        "URDU",
        "VIETNAMESE",
      ],
      {
        required_error: "Language is required",
      }
    )
    .optional(),
  race: z
    .enum(
      [
        "ASIAN",
        "BLACK_OR_AFRICAN_AMERICAN",
        "CAUCASIAN",
        "HISPANIC",
        "MIDDLE_EASTERN",
        "NATIVE_AMERICAN",
        "OTHER",
        "PACIFIC_ISLANDER",
      ],
      {
        required_error: "Race is required",
      }
    )
    .optional(),
  street1: z.string().optional(),
  street2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z
    .string()
    .regex(
      /^\d{5}(-\d{4})?$/,
      "Please enter a valid ZIP code (e.g., 12345 or 12345-6789)"
    )
    .optional()
    .or(z.literal("")),
  phone: z
    .object({
      number: z
        .string()
        .regex(
          /^\d{3}-\d{3}-\d{4}$/,
          "Please enter a valid phone number (e.g., ************)"
        )
        .optional()
        .or(z.literal("")),
      ext: z.string().optional(),
    })
    .optional(),
  cell: z
    .string()
    .regex(
      /^\d{3}-\d{3}-\d{4}$/,
      "Please enter a valid cell number (e.g., ************)"
    )
    .optional()
    .or(z.literal("")),
  email: z
    .string()
    .email("Please enter a valid email address")
    .optional()
    .or(z.literal("")),
  fax: z
    .string()
    .regex(
      /^\d{3}-\d{3}-\d{4}$/,
      "Please enter a valid fax number (e.g., ************)"
    )
    .optional()
    .or(z.literal("")),
  noInsurance: z.boolean().optional(),
  company: z.string().optional(),
  claimNumber: z.string().optional(),
  policyNumber: z.string().optional(),
  insured: z.string().optional(),
  policyLimits: z.string().optional(),
  liabilityStatus: z
    .object({
      status: z.string().optional(),
      percentage: z.string().optional(),
    })
    .optional(),
  coverageStatus: z.string().optional(),
  medPay: z.string().optional(),
  confirmed: z.boolean().optional(),
  claimNote: z.string().optional(),
});

// Helper function to create defendant data from form values
const createDefendantData = (values: z.infer<typeof formSchema>) => {
  return {
    defendant_type: values.defendantType,
    first_name: values.firstName,
    last_name: values.lastName,
    company_entity: values.companyEntity || undefined,
    registered_agent: values.registeredAgent || null,
    email: values.email || null,
    phone: values.phone?.number || null,
    phone_ext: values.phone?.ext || null,
    cell: values.cell || null,
    fax: values.fax || null,
    street1: values.street1 || null,
    street2: values.street2 || null,
    city: values.city || null,
    state: values.state || null,
    zip_code: values.zip || null,
    ssn: values.socialSecurityNumber || null,
    date_of_birth: values.dateOfBirth
      ? format(values.dateOfBirth, 'yyyy-MM-dd')
      : null,
    gender: values.gender || null,
    language: values.language || null,
    race: values.race || null,
    driver_license_type: values.driverLicense?.type || null,
    driver_license: values.driverLicense?.number || null,
    description: values.description || "",
    middle_name: values.middleName || null,
  };
};

// Helper function to create insurance data from form values
const createInsuranceData = (values: z.infer<typeof formSchema>) => {
  return {
    insurance_company: values.company ? Number(values.company) : null,
    no_insurance: values.noInsurance || false,
    claim_number: values.claimNumber || null,
    policy_number: values.policyNumber || null,
    insured: values.insured || null,
    policy_limits: values.policyLimits || null,
    liability_status: values.liabilityStatus?.status || null,
    liability_percentage: values.liabilityStatus?.percentage || null,
    coverage_status: values.coverageStatus || null,
    med_pay: values.medPay || null,
    confirmed: values.confirmed || false,
    claim_note: values.claimNote || null,
  };
};

export function CreateUpdateDefendant({
  isEditing = false,
  caseId,
  formData,
}: {
  isEditing: boolean;
  caseId: string;
  formData?: DefendantDetailResponse;
}) {
  const createDefendantWithInsurance =
    useCreateDefendantWithInsuranceMutation(caseId);
  const updateDefendant = useUpdateDefendantMutation(
    caseId,
    formData?.id.toString() || ""
  );
  const defendantSync = useDefendantSyncMutation(caseId);
  // const { data: insuranceCompanies, isLoading: isLoadingInsuranceCompanies } =
  //   useInsuranceCompaniesQuery();

  const {
    matchingClients,
    matchingDefendants,
    isModalOpen,
    closeModal,
    searchForMatchingCases,
  } = useMatchingCases();

  // const queryClient = useQueryClient();
  // const [insuranceCompaniesLastUpdated, setInsuranceCompaniesLastUpdated] = useState(Date.now());

  // // Function to refresh insurance companies list
  // const refreshInsuranceCompanies = useCallback(async () => {
  //   await queryClient.invalidateQueries({ queryKey: ['insuranceCompanies'] });
  //   await queryClient.refetchQueries({ queryKey: ['insuranceCompanies'] });
  //   setInsuranceCompaniesLastUpdated(Date.now());
  // }, [queryClient]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      defendantType: DefendantType.PRIVATE_PARTY,
      companyEntity: "",
      description: "",
      firstName: "",
      middleName: "",
      lastName: "",
      registeredAgent: "",
      gender: "Female",
      socialSecurityNumber: "",
      dateOfBirth: undefined,
      driverLicense: {
        type: undefined,
        number: "",
      },
      language: undefined,
      race: undefined,
      street1: "",
      street2: "",
      city: "",
      state: "",
      zip: "",
      phone: {
        number: "",
        ext: "",
      },
      cell: "",
      email: "",
      fax: "",
      noInsurance: false,
      company: "",
      claimNumber: "",
      policyNumber: "",
      insured: "",
      policyLimits: "",
      liabilityStatus: {
        status: "",
        percentage: "",
      },
      coverageStatus: "",
      medPay: "",
      confirmed: true,
      claimNote: "",
    },
  });

  const [open, setOpen] = useState(false);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

  // Use effect to populate form data when formData is available
  useEffect(() => {
    if (formData) {
      form.reset({
        defendantType: formData.defendant_type as DefendantType,
        companyEntity: formData.company_entity || "",
        firstName: formData.first_name || "",
        lastName: formData.last_name || "",
        middleName: formData.middle_name || "",
        registeredAgent: formData.registered_agent || "",
        gender:
          (formData.gender as "Male" | "Female" | "Non-binary") || "Female",
        socialSecurityNumber: formData.ssn || "",
        dateOfBirth: formData.date_of_birth
          ? new Date(formData.date_of_birth + "T00:00:00")
          : undefined,
        driverLicense: {
          type:
            (formData.driver_license_type as (typeof DRIVER_LICENSE_STATES)[number]) ||
            undefined,
          number: formData.driver_license || "",
        },
        language:
          (formData.language as z.infer<typeof formSchema>["language"]) ||
          undefined,
        race:
          (formData.race as z.infer<typeof formSchema>["race"]) || undefined,
        street1: formData.street1 || "",
        street2: formData.street2 || "",
        city: formData.city || "",
        state: formData.state || "",
        zip: formData.zip_code || "",
        phone: {
          number: formData.phone || "",
          ext: formData.phone_ext || "",
        },
        cell: formData.cell || "",
        email: formData.email || "",
        fax: formData.fax || "",
        description: formData.description || "",
      });
    }
  }, [formData, form]);

  function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      // Check for matching clients before submitting
      if (values.firstName && values.lastName && values.dateOfBirth) {
        const searchParams = {
          first_name: values.firstName,
          last_name: values.lastName,
          date_of_birth: format(values.dateOfBirth, 'yyyy-MM-dd'),
          case_id: caseId
        };

        // Make this an async function to properly handle the await
        (async () => {
          // Search for matching cases and return early to prevent form submission
          // if matches are found - the modal will be shown automatically
          await searchForMatchingCases(searchParams);

          // If we have matches, don't proceed with form submission yet
          // The user will need to close the modal first
          if (matchingClients.length > 0 || matchingDefendants.length > 0) {
            return;
          }

          // If no matches, proceed with form submission
          submitForm(values);
        })();

        // Return early to prevent the regular form submission
        return;
      }

      // If we don't have the required fields for matching, proceed with form submission
      submitForm(values);
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }

  // Extract the form submission logic to a separate function
  function submitForm(values: z.infer<typeof formSchema>) {
    try {
      if (isEditing && formData) {
        // Call update mutation if editing
        const defendantData = createDefendantData(values);
        updateDefendant.mutate(defendantData, {
          onSuccess: () => {
            setOpen(false);
            form.reset();
          },
        });
      } else {
        // Call create mutation if creating new
        createDefendantWithInsurance.mutate(
          {
            defendant: createDefendantData(values),
            insurance: createInsuranceData(values),
          },
          {
            onSuccess: () => {
              setOpen(false);
              form.reset();
            },
          }
        );
      }
    } catch (error) {
      console.error("Error creating/updating defendant:", error);
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset();
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const handleSync = async (selectedCaseIds: string[]) => {
    try {
      setIsSyncing(true);
      
      // First update/create the defendant with current form values
      const values = form.getValues();
      const defendantData = createDefendantData(values);

      let updatedDefendant;
      if (isEditing && formData?.id) {
        // Update existing defendant
        updatedDefendant = await updateDefendant.mutateAsync(defendantData);
        await defendantSync.mutateAsync({
          source_defendant_id: updatedDefendant.id,
          target_case_ids: selectedCaseIds
        });
      } else {
        updatedDefendant = await createDefendantWithInsurance.mutateAsync({
          defendant: {
            ...defendantData,
          },
          insurance: createInsuranceData(values)
        });
        await defendantSync.mutateAsync({
          source_defendant_id: updatedDefendant.defendant.id,
          target_case_ids: selectedCaseIds
        });
      }

      setIsSyncDialogOpen(false);
      setOpen(false);
      form.reset();
      
    } catch (error) {
      console.error("Failed to sync defendant:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEditing ? (
            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
          ) : (
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Defendant
            </Button>
          )}
        </DialogTrigger>
        <DialogContent
          className="max-w-6xl bg-white flex flex-col overflow-hidden"
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
          onEscapeKeyDown={(e) => {
            e.preventDefault();
          }}
        >
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {isEditing ? "Edit Defendant" : "Add New Defendant"}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="h-full flex flex-col"
            >
              <div className="flex-1 max-h-[80vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <div className="space-y-6 border-b pb-6">
                  <h2 className="text-lg font-semibold flex items-center gap-2 text-gray-800">
                    Personal Information
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="defendantType"
                      render={({ field }) => (
                        <FormItem>
                          <RequiredLabel>Defendant Type</RequiredLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(DefendantType).map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type.replace(/_/g, " ")}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="companyEntity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company/Entity </FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem className="col-span-full">
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <div className="bg-white border rounded-md">
                              <CustomCKEditor
                                initialValue={field.value || ''}
                                onChange={(value: string) => field.onChange(value)}
                                placeholder="Enter a description..."
                                minHeight="100px"
                                className="min-h-[100px] resize-none bg-gray-50"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <RequiredLabel>First Name</RequiredLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="middleName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Middle Name</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <RequiredLabel >Last Name</RequiredLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="registeredAgent"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Registered Agent</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gender</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value}
                              className="flex space-x-4"
                            >
                              <FormItem className="flex items-center space-x-2">
                                <RadioGroupItem value="Male" />
                                <FormLabel className="font-normal">
                                  Male
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <RadioGroupItem value="Female" />
                                <FormLabel className="font-normal">
                                  Female
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2">
                                <RadioGroupItem value="Non-binary" />
                                <FormLabel className="font-normal">
                                  Non-binary
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="socialSecurityNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Social Security Number</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              maxLength={11}
                              onChange={(e) => {
                                const formatted = formatSSN(e.target.value);
                                field.onChange(formatted);
                              }}
                              placeholder="***********"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date of Birth</FormLabel>
                          <FormControl>
                            <CustomDateInput
                              value={field.value ? format(startOfDay(field.value), "dd-MM-yyyy") : ""}
                              onChange={(value: string) => {
                                // Try to parse the date
                                const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                                if (!isNaN(parsedDate.getTime())) {
                                  field.onChange(startOfDay(parsedDate));
                                }
                              }}
                              error={!!form.formState.errors.dateOfBirth}
                              maxDate={new Date()}
                              onError={(message: string) => {
                                form.setError("dateOfBirth", {
                                  type: "manual",
                                  message
                                });
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex gap-2">
                      <FormField
                        control={form.control}
                        name="driverLicense.type"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>Driver License type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="State" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    className="mt-2 w-full justify-center text-sm"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      field.onChange("");
                                      form.setValue("driverLicense.type", undefined);
                                    }}
                                  >
                                    Clear Selection
                                  </Button>
                                )}
                                {DRIVER_LICENSE_STATES.map((state) => (
                                  <SelectItem key={state} value={state}>
                                    {state}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="driverLicense.number"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>License Number</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Language</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select language" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent searchable>
                              {field.value && (
                                <Button
                                  variant="ghost"
                                  className="mt-2 w-full justify-center text-sm"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    field.onChange("");
                                    form.setValue("language", undefined);
                                  }}
                                >
                                  Clear Selection
                                </Button>
                              )}
                              <SelectItem value="ENGLISH">English</SelectItem>
                              <SelectItem value="ARABIC">Arabic</SelectItem>
                              <SelectItem value="ARMENIAN">
                                Armenian
                              </SelectItem>
                              <SelectItem value="BENGALI">Bengali</SelectItem>
                              <SelectItem value="CANTONESE">
                                Cantonese
                              </SelectItem>
                              <SelectItem value="CREOLE">Creole</SelectItem>
                              <SelectItem value="FRENCH">French</SelectItem>
                              <SelectItem value="FRENCH_CREOLE">
                                French Creole
                              </SelectItem>
                              <SelectItem value="GERMAN">German</SelectItem>
                              <SelectItem value="GREEK">Greek</SelectItem>
                              <SelectItem value="GUJARATI">
                                Gujarati
                              </SelectItem>
                              <SelectItem value="HEBREW">Hebrew</SelectItem>
                              <SelectItem value="HINDI">Hindi</SelectItem>
                              <SelectItem value="HMONG">Hmong</SelectItem>
                              <SelectItem value="ITALIAN">Italian</SelectItem>
                              <SelectItem value="JAPANESE">
                                Japanese
                              </SelectItem>
                              <SelectItem value="KOREAN">Korean</SelectItem>
                              <SelectItem value="MANDARIN">
                                Mandarin
                              </SelectItem>
                              <SelectItem value="PERSIAN">Persian</SelectItem>
                              <SelectItem value="POLISH">Polish</SelectItem>
                              <SelectItem value="PORTUGUESE">
                                Portuguese
                              </SelectItem>
                              <SelectItem value="PUNJABI">Punjabi</SelectItem>
                              <SelectItem value="RUSSIAN">Russian</SelectItem>
                              <SelectItem value="SPANISH">Spanish</SelectItem>
                              <SelectItem value="TAGALOG">Tagalog</SelectItem>
                              <SelectItem value="TELUGU">Telugu</SelectItem>
                              <SelectItem value="URDU">Urdu</SelectItem>
                              <SelectItem value="VIETNAMESE">
                                Vietnamese
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="race"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Race</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select race" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {field.value && (
                                <Button
                                  variant="ghost"
                                  className="mt-2 w-full justify-center text-sm"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    field.onChange("");
                                    form.setValue("race", undefined);
                                  }}
                                >
                                  Clear Selection
                                </Button>
                              )}
                              <SelectItem value="ASIAN">Asian</SelectItem>
                              <SelectItem value="BLACK_OR_AFRICAN_AMERICAN">
                                Black or African American
                              </SelectItem>
                              <SelectItem value="CAUCASIAN">
                                Caucasian
                              </SelectItem>
                              <SelectItem value="HISPANIC">
                                Hispanic
                              </SelectItem>
                              <SelectItem value="MIDDLE_EASTERN">
                                Middle Eastern
                              </SelectItem>
                              <SelectItem value="NATIVE_AMERICAN">
                                Native American
                              </SelectItem>
                              <SelectItem value="OTHER">Other</SelectItem>
                              <SelectItem value="PACIFIC_ISLANDER">
                                Pacific Islander
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="space-y-6">
                  <h2 className="text-lg font-semibold flex items-center gap-2 text-gray-800">
                    Contact Information
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="street1"
                      render={({ field }) => (
                        <FormItem className="col-span-full">
                          <FormLabel>Street 1</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="street2"
                      render={({ field }) => (
                        <FormItem className="col-span-full">
                          <FormLabel>Street 2</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select state" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent searchable>
                              {field.value && (
                                <Button
                                  variant="ghost"
                                  className="mt-2 w-full justify-center text-sm"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    field.onChange("");
                                    form.setValue("state", undefined);
                                  }}
                                >
                                  Clear Selection
                                </Button>
                              )}
                              {Object.entries(USStatesLabels).map(
                                ([value, label]) => (
                                  <SelectItem key={value} value={value}>
                                    {label}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="zip"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ZIP</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex gap-2">
                      <FormField
                        control={form.control}
                        name="phone.number"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <PhoneNumberInput
                                {...field}
                                value={field.value || ""}
                                className="bg-white"
                                placeholder="************"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone.ext"
                        render={({ field }) => (
                          <FormItem className="w-24">
                            <FormLabel>Ext</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="cell"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cell</FormLabel>
                          <FormControl>
                            <PhoneNumberInput
                              {...field}
                              value={field.value || ""}
                              className="bg-white"
                              placeholder="************"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="fax"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Fax</FormLabel>
                          <FormControl>
                            <PhoneNumberInput
                              {...field}
                              value={field.value || ""}
                              className="bg-white"
                              placeholder="************"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input type="email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter className="flex justify-end gap-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsSyncDialogOpen(true)}
                  disabled={isSyncing}
                >
                  Sync Linked Cases
                </Button>
                <Button
                  type="button"
                  onClick={(e) => {
                    form.handleSubmit((values) => onSubmit(values))(e);
                  }}
                >
                  {isEditing ? "Update" : "Create"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleClose}
        onCancel={handleClose}
      />

      <SyncCaseDialog
        isOpen={isSyncDialogOpen}
        onClose={() => setIsSyncDialogOpen(false)}
        onSync={handleSync}
        isSyncing={isSyncing}
        caseId={caseId}
        syncType="defendants"
      />

      {/* Matching Cases Modal */}
      <MatchingCasesModal
        isOpen={isModalOpen}
        onClose={closeModal}
        matchingClients={matchingClients}
        matchingDefendants={matchingDefendants}
      />
    </>
  );
}
