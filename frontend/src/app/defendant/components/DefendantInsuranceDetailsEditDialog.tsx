"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Edit, Plus } from "lucide-react";
import { useInsuranceCompaniesQuery } from "@/services/orgAPIs";
import { useState, useEffect } from "react";
import {
  DefendantInsuranceCreateRequest,
  DefendantInsuranceListItem,
  DefendantInsuranceUpdateRequest,
  PolicyLimits,
  LiabilityStatus,
  LiabilityPercentage,
  CoverageStatus,
  MedPayLimit,
} from "@/type/case-management/defendantTypes";
import {
  useCreateDefendantInsuranceMutation,
  useUpdateDefendantInsuranceMutation,
} from "@/services/case-management/defendantService";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { default as AddEditInsuranceCompany } from "@/app/client-details/components/AddEditInsurance";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

type StatusFieldName =
  | "policyLimits"
  | "liabilityStatus"
  | "liabilityPercentage"
  | "coverageStatus"
  | "medPay";

const formSchema = z.discriminatedUnion("noInsurance", [
  z.object({
    noInsurance: z.literal(true),
    company: z.string(),
    claimNumber: z.string().optional(),
    policyNumber: z.string().optional(),
    insured: z.string().optional(),
    policyLimits: z.string().optional(),
    liabilityStatus: z.string().optional(),
    liabilityPercentage: z.string().optional(),
    coverageStatus: z.string().optional(),
    medPay: z.string().optional(),
    claimNote: z
      .string()
      .optional(),
    umbrella: z.boolean().optional(),
    confirmed: z.boolean().optional(),
  }),
  z.object({
    noInsurance: z.literal(false),
    company: z.string().min(1, "Insurance company is required"),
    claimNumber: z.string().optional(),
    policyNumber: z.string().optional(),
    insured: z.string().optional(),
    policyLimits: z.string().optional(),
    liabilityStatus: z.string().optional(),
    liabilityPercentage: z.string().optional(),
    coverageStatus: z.string().optional(),
    medPay: z.string().optional(),
    claimNote: z
      .string()
      .optional(),
    umbrella: z.boolean().optional(),
    confirmed: z.boolean().optional(),
  }),
]);

interface Props {
  isEdit: boolean;
  caseId: string;
  defendantId: string;
  insurance?: DefendantInsuranceListItem;
}

const DefendantInsuranceDetailsEditDialog: React.FC<Props> = ({
  isEdit,
  caseId,
  defendantId,
  insurance,
}) => {
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const createMutation = useCreateDefendantInsuranceMutation(
    caseId,
    defendantId
  );
  const updateMutation = useUpdateDefendantInsuranceMutation(
    caseId,
    defendantId,
    insurance?.id?.toString() || ""
  );
  const { data: insuranceCompanies } = useInsuranceCompaniesQuery();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      noInsurance: false,
      company: "",
      claimNumber: "",
      policyNumber: "",
      insured: "",
      policyLimits: "",
      liabilityStatus: "",
      liabilityPercentage: "",
      coverageStatus: "",
      medPay: "",
      claimNote: "",
      umbrella: false,
      confirmed: false,
    },
  });

  const noInsurance = form.watch("noInsurance");

  useEffect(() => {
    if (noInsurance) {
      form.setValue("company", "");
      form.setValue("claimNumber", "");
      form.setValue("policyNumber", "");
      form.setValue("insured", "");
      form.setValue("policyLimits", "");
      form.setValue("liabilityStatus", "");
      form.setValue("liabilityPercentage", "");
      form.setValue("coverageStatus", "");
      form.setValue("medPay", "");
      form.setValue("claimNote", "");
      form.setValue("umbrella", false);
      form.setValue("confirmed", false);
    }
  }, [noInsurance, form]);

  useEffect(() => {
    if (isEdit && insurance) {
      form.reset({
        noInsurance: insurance.no_insurance || false,
        company: insurance.insurance_company?.id?.toString() || "",
        claimNumber: insurance.claim_number || "",
        policyNumber: insurance.policy_number || "",
        insured: insurance.insured || "",
        policyLimits: insurance.policy_limits || "",
        liabilityStatus: insurance.liability_status || "",
        liabilityPercentage: insurance.liability_percentage || "",
        coverageStatus: insurance.coverage_status || "",
        medPay: insurance.med_pay || "",
        claimNote: insurance.claim_note || "",
        umbrella: insurance.umbrella_policy || false,
        confirmed: insurance.confirmed || false,
      });
    }
  }, [isEdit, insurance, form]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      const insuranceData:
        | DefendantInsuranceCreateRequest
        | DefendantInsuranceUpdateRequest = {
        defendant: Number(defendantId),
        insurance_company: parseInt(values.company),
        no_insurance: values.noInsurance,
        claim_number: values.claimNumber,
        policy_number: values.policyNumber,
        insured: values.insured,
        policy_limits: values.policyLimits,
        liability_status: values.liabilityStatus,
        liability_percentage: values.liabilityPercentage,
        coverage_status: values.coverageStatus,
        med_pay: values.medPay,
        confirmed: values.confirmed,
        claim_note: values.claimNote,
        umbrella_policy: values.umbrella,
      };

      if (isEdit) {
        await updateMutation.mutateAsync(insuranceData);
      } else {
        await createMutation.mutateAsync(insuranceData);
      }

      setOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    setShowUnsavedAlert(false);
    form.reset();
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    console.log("isDirty", isDirty);
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEdit ? (
            <Edit
              className="h-4 w-4 text-green-600 cursor-pointer hover:text-green-700 transition-colors"
              onClick={() => setOpen(true)}
            />
          ) : (
            <Button
              variant="link"
              className="text-green-600"
              onClick={() => setOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2 stroke-[1.5]" />
              Insurance
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-6xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {isEdit ? "Edit Insurance" : "Add Defendant Insurance"}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Card className="p-6 space-y-6 max-h-[70vh] overflow-y-auto">
                {/* No Insurance Checkbox */}
                <FormField
                  control={form.control}
                  name="noInsurance"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-blue-600"
                        />
                      </FormControl>
                      <FormLabel className="font-medium text-gray-700">
                        No Insurance
                      </FormLabel>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-3 gap-6">
                  {/* Company and Umbrella Policy */}
                  <FormField
                    control={form.control}
                    name="company"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Company{" "}
                          {!noInsurance && (
                            <span className="text-red-500">*</span>
                          )}
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ""}
                          disabled={noInsurance}
                        >
                          <FormControl>
                            <SelectTrigger className="h-10 border-gray-200 focus:ring-2 focus:ring-blue-100">
                              <SelectValue placeholder="Select company" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent searchable>
                            {insuranceCompanies?.map((company) => (
                              <SelectItem
                                key={company.id}
                                value={company.id.toString()}
                                className="hover:bg-blue-50"
                              >
                                {company.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <div className="flex justify-start mt-2 space-x-2">
                          <AddEditInsuranceCompany
                            isEdit={false}
                            trigger={
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="text-teal-600 border-teal-600 hover:bg-teal-50"
                                disabled={noInsurance}
                              >
                                ADD
                              </Button>
                            }
                            onSuccess={(newInsuranceId) => {
                              if (newInsuranceId) {
                                form.setValue("company", newInsuranceId.toString(), {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                  shouldTouch: true
                                });
                                setTimeout(() => {
                                  form.trigger("company");
                                }, 100);
                              }
                            }}
                          />
                          {field.value && (
                            <AddEditInsuranceCompany
                              isEdit={true}
                              company={field.value}
                              trigger={
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  className="text-blue-600 border-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                  disabled={noInsurance}
                                >
                                  EDIT
                                </Button>
                              }
                            />
                          )}
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="umbrella"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-blue-600"
                            disabled={noInsurance}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Umbrella Policy
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-3 gap-6">
                  {/* Claim #, Policy #, and Insured */}
                  <FormField
                    control={form.control}
                    name="claimNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Claim #
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="h-10 border-gray-200 focus:ring-2 focus:ring-blue-100"
                            disabled={noInsurance}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="policyNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Policy #
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="h-10 border-gray-200 focus:ring-2 focus:ring-blue-100"
                            disabled={noInsurance}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insured"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Insured
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="h-10 border-gray-200 focus:ring-2 focus:ring-blue-100"
                            disabled={noInsurance}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-5 gap-6">
                  {/* Status Fields */}
                  {[
                    "policyLimits",
                    "liabilityStatus",
                    "liabilityPercentage",
                    "coverageStatus",
                    "medPay",
                  ].map((fieldName) => (
                    <FormField
                      key={fieldName}
                      control={form.control}
                      name={fieldName as StatusFieldName}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">
                            {fieldName
                              .replace(/([A-Z])/g, " $1")
                              .replace(/^./, (str) => str.toUpperCase())}
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                            disabled={noInsurance}
                          >
                            <FormControl>
                              <SelectTrigger className="h-10 border-gray-200 focus:ring-2 focus:ring-blue-100">
                                <SelectValue
                                  placeholder={
                                    fieldName === "liabilityPercentage"
                                      ? "Select %"
                                      : "Select"
                                  }
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {field.value && (
                                <Button
                                  variant="ghost"
                                  className="mb-2 w-full justify-center text-sm"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    field.onChange("");
                                  }}
                                >
                                  Clear Selection
                                </Button>
                              )}
                              {fieldName === "policyLimits" ? (
                                Object.values(PolicyLimits).map((limit) => (
                                  <SelectItem
                                    key={limit}
                                    value={limit}
                                    className="hover:bg-blue-50"
                                    disabled={noInsurance}
                                  >
                                    {limit}
                                  </SelectItem>
                                ))
                              ) : fieldName === "liabilityStatus" ? (
                                Object.values(LiabilityStatus).map((status) => (
                                  <SelectItem
                                    key={status}
                                    value={status}
                                    className="hover:bg-blue-50"
                                  >
                                    {status}
                                  </SelectItem>
                                ))
                              ) : fieldName === "liabilityPercentage" ? (
                                Object.values(LiabilityPercentage).map(
                                  (percent) => (
                                    <SelectItem
                                      key={percent}
                                      value={percent}
                                      className="hover:bg-blue-50"
                                    >
                                      {percent}
                                    </SelectItem>
                                  )
                                )
                              ) : fieldName === "coverageStatus" ? (
                                Object.values(CoverageStatus).map((status) => (
                                  <SelectItem
                                    key={status}
                                    value={status}
                                    className="hover:bg-blue-50"
                                  >
                                    {status}
                                  </SelectItem>
                                ))
                              ) : fieldName === "medPay" ? (
                                Object.values(MedPayLimit).map((limit) => (
                                  <SelectItem
                                    key={limit}
                                    value={limit}
                                    className="hover:bg-blue-50"
                                  >
                                    {limit}
                                  </SelectItem>
                                ))
                              ) : (
                                <SelectItem value="-">-</SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  ))}
                </div>

                {/* Claim Note */}
                <FormField
                  control={form.control}
                  name="claimNote"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Claim Note
                      </FormLabel>
                      <FormControl>
                        <CustomCKEditor
                          initialValue={field.value || ''}
                          onChange={(value: string) => field.onChange(value)}
                          placeholder="Take a claim note..."
                          minHeight="100px"
                          className="min-h-[100px] resize-none bg-gray-50"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Confirmed Checkbox */}
                <FormField
                  control={form.control}
                  name="confirmed"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-blue-600"
                          disabled={noInsurance}
                        />
                      </FormControl>
                      <FormLabel className="font-medium text-gray-700">
                        Confirmed
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </Card>

              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelClick}
                  className="px-6"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="px-6 bg-blue-600 hover:bg-blue-700"
                >
                  {isEdit ? "Update" : "Save"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleClose}
        onCancel={handleClose}
      />
    </>
  );
};

export default DefendantInsuranceDetailsEditDialog;
