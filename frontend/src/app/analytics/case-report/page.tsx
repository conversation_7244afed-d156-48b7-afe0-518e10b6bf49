"use client";

import React, { useState, useEffect, FC, ReactNode, Fragment, useMemo } from "react";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";
import axios from "axios";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronLeft, ChevronRight, CalendarIcon, X, ChevronDown } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useSelector } from "react-redux";
import { RootState, setSelectedNavigation } from "@/store";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

import {
  useCaseListQuery,
  useCaseStatusQuery,
  useCaseStatisticsQuery,
  useCheckDepositsQuery,
  useClientsQuery,
  useClientsOpenQuery,
  useClientsReferredInQuery,
  useClientsSubbedOutQuery,
  useClientsIntakeReadyQuery,
  useClientsRetainedQuery,
  useDefendantTypesQuery,
  useHealthProvidersQuery,
  useInsuranceAdjustersQuery,
  useInsuranceCompaniesQuery,
  useLienHoldersQuery,
  useMedpayRequestsQuery,
  useMedpayDepositsQuery,
  useNotesQuery,
  useSourcePerformanceQuery,
  useMedicalRequestsQuery,
  useStatuteQuery,
  useDemandsQuery,
  useSettlementQuery,
  useSettlementManagementQuery,
  useLitigationManagementQuery,
  useClientTrustQuery,
  useClientTrustAttorneyFeesQuery,
  useClientTrustIssuedPaymentsQuery,
  useClientTrustCheckDepositsQuery,
  useClientTrustConsolidatedQuery,
  useCaseReviewQuery,
  useCaseCostsQuery,
  useContactsQuery,
  useCaseEventsCalendarQuery,
  useCaseListClosedQuery,
  useCaseListRejectedQuery,
  useCaseListDroppedQuery,
  getReportEndpoint,
  useEstimatedValueQuery,
} from "@/services/case-management/caseAnalyticsService";

import {
  ReportType,
  CaseReportFilters,
  CaseStatusResponse,
  CheckDepositsResponse,
  ClientsResponse,
  DefendantTypesResponse,
  HealthProvidersResponse,
  InsuranceAdjustersResponse,
  InsuranceCompaniesResponse,
  LienHoldersResponse,
  FilterOption,
  WorkerOption,
  FilterOptions,
  SummaryTilesProps,
  DynamicTableProps,
  CaseListSummary,
  CaseStatusSummary,
  StatisticsSummary,
  CheckDepositsSummary,
  ClientsSummary,
  DefendantTypesSummary,
  HealthProvidersSummary,
  InsuranceAdjustersSummary,
  InsuranceCompaniesSummary,
  LienHoldersSummary,
  ReportSummary,
  LienStats,
  CaseWorker,
  convertInsuranceCompanySummary,
  TableRowTypeMap,
  TableData,
  StatuteSummary,
  DemandsSummary,
  SettlementSummary,
  MedpayRequestsSummary,
  MedpayDepositsSummary,
  NotesSummary,
  SourcePerformanceSummary,
  MedicalRequestsSummary,
  MedpayRequestsResponse,
  MedpayDepositsResponse,
  SourcePerformanceResponse,
  MedicalRequestsResponse,
  SettlementResponse,
  DemandsResponse,
  StatuteResponse,
  NotesResponse,
  CaseListResponse,
  ClientTrustResponse,
  ClientTrustSummary,
  ClientTrustConsolidatedResponse,
  ClientTrustConsolidatedSummary,
  SettlementManagementSummary,
  LitigationManagementSummary,
  LitigationManagementResponse,
  SettlementManagementResponse,
  BaseTableRow,
  InsuranceAdjusterRow,
  HealthProviderRow,
  DefendantTypeRow,
  CaseAmounts,
  CaseCounts,
  CaseReviewResponse,
  CaseReviewSummary,
  StatisticsResponse,
  CaseCostResponse,
  CaseCostSummary,
  ContactsResponse,
  ContactsSummary,
  ContactRelatedCase,
  CaseEventsCalendarSummary,
  CaseEventsCalendarResponse,
  EstimatedValueResponse,
  EstimatedValueSummary,
  ValueRangeFilter,
} from "@/type/case-management/caseAnalyticsTypes";

import { formatText } from "@/utils/string";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { PhoneLink } from "@/components/ui/phone-link";
import { ManagementReportTiles } from "@/components/analytics/ManagementReportTiles";
import { RangeSliderFilter } from "@/components/analytics/RangeSliderFilter";
import CKViewer from "@/components/ckeditor/CKViewer";

interface DateRangePickerProps {
  dateRange: DateRange | undefined;
  setDateRange: (dateRange: DateRange | undefined) => void;
}

// Update the DateRangePicker component to fix the closing issue
const DateRangePicker: FC<DateRangePickerProps> = ({ dateRange, setDateRange }) => {
  // Add state to control the popover's open state
  const [isOpen, setIsOpen] = useState(false);
  // Track date selection within the component
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>(dateRange);

  // When dateRange prop changes, update internal state
  useEffect(() => {
    setSelectedRange(dateRange);
  }, [dateRange]);

  // Handle applying the selected range
  const handleApplyRange = () => {
    setDateRange(selectedRange);
    setIsOpen(false);
  };

  // Handle clearing the selected range
  const handleClearRange = () => {
    setSelectedRange(undefined);
    setDateRange(undefined);
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-12 px-4 bg-white relative group transition-all",
            !dateRange && "text-muted-foreground",
            "hover:border-primary/50 focus:border-primary"
          )}
        >
          <div className="flex items-center w-full">
            <CalendarIcon className="mr-3 h-5 w-5 shrink-0 text-gray-400 group-hover:text-primary transition-colors" />
            <span className="flex-grow">
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    <span className="font-medium">{format(dateRange.from, "LLL dd, y")}</span>
                    <span className="mx-2 text-gray-400">→</span>
                    <span className="font-medium">{format(dateRange.to, "LLL dd, y")}</span>
                  </>
                ) : (
                  <span className="font-medium">{format(dateRange.from, "LLL dd, y")}</span>
                )
              ) : (
                <span>Select Date Range</span>
              )}
            </span>
            {dateRange?.from && (
              <X
                className="h-4 w-4 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearRange();
                }}
              />
            )}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 shadow-xl shadow-gray-200/60 border border-gray-200" align="start">
        <div className="overflow-hidden rounded-md">
          <div className="bg-white p-3 border-b border-gray-100">
            <div className="text-sm font-medium text-gray-900 mb-1">Select a date range</div>
            <div className="text-xs text-gray-500">Click two dates to select a range</div>
          </div>
          <Calendar
            initialFocus
            mode="range"
            selected={selectedRange}
            onSelect={(newDateRange: DateRange | undefined) => {
              // Only update internal state, don't apply yet
              setSelectedRange(newDateRange);
            }}
            numberOfMonths={2}
            className="rounded-md"
          />
          <div className="flex items-center justify-end gap-2 p-3 border-t border-gray-100 bg-gray-50">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearRange}
              className="h-9 px-4"
            >
              Clear
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleApplyRange}
              className="h-9 px-4 bg-primary hover:bg-primary/90"
            >
              Apply Range
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

const parseDateRange = (dateRangeString: string | undefined): DateRange | undefined => {
  if (!dateRangeString) return undefined;
  try {
    const range = JSON.parse(dateRangeString);
    if (!range.from) return undefined;

    const fromDate = new Date(range.from);
    fromDate.setHours(0, 0, 0, 0);

    let toDate;
    if (range.to) {
      toDate = new Date(range.to);
      toDate.setHours(23, 59, 59, 999);
    }

    return {
      from: fromDate,
      to: toDate
    };
  } catch (e) {
    console.error('Error parsing date range:', e);
    return undefined;
  }
};

// Report options for the dropdown
const reportOptions = [
  { value: "caseList", label: "My Cases/Case List" },
  { value: "caseListClosed", label: "Closed Cases" },
  { value: "caseListRejected", label: "Rejected Cases" },
  { value: "caseListDropped", label: "Dropped Cases" },
  { value: "caseReview", label: "Review Cases" },
  { value: "caseStatus", label: "Case Status" },
  { value: "caseCosts", label: "Case Costs" },
  { value: "caseEventsCalendar", label: "Calendar" },
  { value: "clientTrust", label: "Client Trust Transactions" },
  { value: "clientTrustAttorneyFees", label: "Fees In" },
  { value: "clientTrustIssuedPayments", label: "Issued Payments" },
  { value: "clientTrustCheckDeposits", label: "Check Deposits" },
  { value: "clientTrustConsolidated", label: "Client Trust" },
  { value: "clients", label: "Clients" },
  { value: "clientsOpen", label: "Open Cases" },
  { value: "clientsReferredIn", label: "Referred In" },
  { value: "clientsSubbedOut", label: "Subbed Out" },
  { value: "clientsIntakeReady", label: "Intake" },
  { value: "clientsRetained", label: "Retained" },
  { value: "defendantTypes", label: "Defendant Types" },
  { value: "demands", label: "Demands" },
  { value: "estimatedValue", label: "Estimated Value" },
  { value: "healthProviders", label: "Health Providers" },
  { value: "insuranceAdjusters", label: "Insurance Adjusters" },
  { value: "insuranceCompanies", label: "Insurance Companies" },
  { value: "lienHolders", label: "Lien Holders" },
  { value: "litigationManagement", label: "Litigation Management" },
  { value: "medpayRequests", label: "MedPay Requests" },
  { value: "medpayDeposits", label: "MedPay Deposits" },
  { value: "medicalRequests", label: "Medical Requests" },
  { value: "notes", label: "Notes" },
  { value: "settlement", label: "Settlement" },
  { value: "settlementManagement", label: "Settlement Management" },
  { value: "sourcePerformance", label: "Source Performance" },
  { value: "statistics", label: "Statistics" },
  { value: "statute", label: "Statute" },
  // { value: "checkDeposits", label: "Check Deposits" },
  { value: "contacts", label: "Contacts" },
];

// Reusable card component with expansion capability
const SummaryCard = ({
  title,
  value,
  subtitle = null,
  cardId,
  expandable = false,
  expandedContent = null,
  isExpanded = false,
  onToggleExpand = () => { },
  clickable = false,
  filterValue = "",
  isActive = false,
  onTileClick = () => { },
  color = 'green',
}: {
  title: string;
  value: ReactNode;
  subtitle?: ReactNode;
  cardId: string;
  expandable?: boolean;
  expandedContent?: ReactNode;
  isExpanded?: boolean;
  onToggleExpand?: (cardId: string) => void;
  clickable?: boolean;
  filterValue?: string;
  isActive?: boolean;
  onTileClick?: (filterValue: string) => void;
  color?: 'green' | 'blue' | 'red' | 'yellow' | 'purple' | 'orange';
}) => {
  return (
    <div
      className={cn(
        "relative overflow-hidden group transition-all duration-300 ease-in-out h-full min-h-[90px]",
        "bg-white border rounded-lg shadow-sm hover:shadow-md",
        clickable ? "cursor-pointer" : "",
        isActive ? "ring-2 ring-primary ring-offset-1 border-primary/30" : "border-gray-200"
      )}
      onClick={() => clickable && onTileClick(filterValue)}
    >
      <div className={cn(
        "px-3 py-2 flex flex-col h-full relative",
        isActive && "bg-primary/5"
      )}>
        {/* Add a colored accent bar at the top */}
        <div className={`absolute top-0 left-0 right-0 h-1 bg-${color}-500`}></div>

        <h3 className={`text-xs font-medium text-${color}-600 uppercase tracking-wide mb-1`}>{title}</h3>
        <div className="mt-auto flex items-end justify-between">
          <div className="flex flex-col">
            <span className={`text-lg font-bold text-${color}-700 tracking-tight leading-none`}>
              {value}
            </span>
            {subtitle && <span className={`text-xs text-${color}-600 mt-1`}>{subtitle}</span>}
          </div>

          {expandable && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggleExpand(cardId);
              }}
              className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus-visible:ring focus-visible:ring-primary/50"
            >
              {isExpanded ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="18 15 12 9 6 15"></polyline>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              )}
            </button>
          )}
        </div>

        {isExpanded && expandedContent && (
          <div className="mt-2 pt-2 border-t border-gray-100 animate-in fade-in-50 duration-300 text-sm">
            {expandedContent}
          </div>
        )}
      </div>
    </div>
  );
};

// Now update the DynamicTable component
const DynamicTable = ({ data, reportType, isLoading = false }: DynamicTableProps & { isLoading?: boolean }) => {
  console.log("reportType", reportType);

  // Add sort state
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // Sort data based on current sort configuration
  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      // Handle different types of values
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      // Convert to strings for comparison
      const aString = String(aValue).toLowerCase();
      const bString = String(bValue).toLowerCase();

      if (sortConfig.direction === 'asc') {
        return aString.localeCompare(bString);
      } else {
        return bString.localeCompare(aString);
      }
    });
  }, [data, sortConfig]);

  // Handle column header click for sorting
  const handleSort = (key: string) => {
    setSortConfig(current => {
      if (!current || current.key !== key) {
        return { key, direction: 'asc' };
      }
      if (current.direction === 'asc') {
        return { key, direction: 'desc' };
      }
      return null;
    });
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm bg-white p-6">
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="w-12 h-12 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
          <div className="text-gray-500 font-medium">Loading data...</div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-16 bg-gray-50/50 rounded-lg border border-gray-200/70 shadow-sm">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="p-4 bg-gray-100 rounded-full text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
              <polyline points="13 2 13 9 20 9"></polyline>
            </svg>
          </div>
          <h3 className="text-gray-700 font-medium text-lg">No data available</h3>
          <p className="text-gray-500">Try adjusting your filters to see results</p>
        </div>
      </div>
    );
  }

  // Get the first row to determine columns
  const firstRow = data[0] || {};
  const columnKeys = Object.keys(firstRow);

  // Special handling for different report types
  const getColumnHeader = (key: string): string => {
    // Format the column header based on the key
    return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Helper to render cell content based on type and value
  const renderCellContent = (row: BaseTableRow | HealthProviderRow
    | InsuranceAdjusterRow | DefendantTypeRow | LienStats | CaseAmounts | CaseCounts | CaseWorker[]
    | LitigationManagementResponse | SettlementManagementResponse
    | ClientTrustSummary | SourcePerformanceSummary | MedicalRequestsSummary | StatuteSummary | DemandsSummary | SettlementSummary
    | LitigationManagementSummary | SettlementManagementSummary
    , key: string) => {
    const value = Object.fromEntries(Object.entries(row))[key];

    // Handle case IDs by creating hyperlinks
    if ((key === 'case') &&
      typeof value === 'string') {
      const caseId = value.split(':').pop()?.trim() || '';
      const newValue = value.replace(caseId, '').replace(':', '');
      setSelectedNavigation(`cases/${caseId}`);
      return (
        <Link
          href={`/dashboard/case-view/${caseId}`}
          className="text-primary hover:text-primary/80 underline font-medium flex items-center gap-1"
          target="_blank"
        >
          <span>{newValue}</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-0.5">
            <path d="M7 17L17 7" />
            <path d="M7 7h10v10" />
          </svg>
        </Link>
      );
    }

    // Handle address field (standalone JSON object)
    if (key === 'address' && typeof value === 'object' && value !== null && !Array.isArray(value)) {
      const address = value as {
        street1?: string;
        street2?: string;
        city?: string;
        state?: string;
        zip_code?: string;
      };

      const addressParts = [
        address.street1,
        address.street2,
        address.city,
        address.state,
        address.zip_code
      ].filter(Boolean);

      if (addressParts.length === 0) return "—";

      const formattedAddress = addressParts.join(", ");
      const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(formattedAddress)}`;

      return (
        <div className="inline-flex items-start px-2.5 py-1.5 rounded-md bg-blue-50/50 border border-blue-100/50">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5 mt-0.5 text-blue-500">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          <a
            href={mapUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            {formattedAddress}
          </a>
        </div>
      );
    }

    // Handle related_cases array
    if (key === 'related_cases' && Array.isArray(value) ) {
      if (value.length === 0) {
        return "—";
      }
      return (
        <div className="max-h-[200px] overflow-y-auto p-2 border border-gray-200 rounded-md bg-gray-50/60 shadow-sm scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <div className="space-y-2">
            {value.map((caseItem: ContactRelatedCase, index: number) => {
              const caseId = caseItem.id;
              return (
                <div key={index} className="p-2 bg-white rounded border border-gray-100 hover:shadow-md transition-all">
                  <Link
                    href={`/dashboard/case-view/${caseId}`}
                    className="text-primary hover:text-primary/80 font-medium flex items-center gap-1"
                    target="_blank"
                  >
                    <span>{caseItem.id}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-0.5">
                      <path d="M7 17L17 7" />
                      <path d="M7 7h10v10" />
                    </svg>
                  </Link>
                  <div className="text-sm mt-1 flex flex-col">
                    <span className="font-medium">{caseItem.client_name}</span>
                    <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        {format(new Date(caseItem.incident_date || ''), 'MM/dd/yyyy')}
                      </div>
                      <span>•</span>
                      <div className={`text-xs px-1.5 py-0.5 rounded ${
                        caseItem.organization_status === 'Closed' ? 'bg-gray-100 text-gray-700' :
                        caseItem.organization_status === 'Treating' ? 'bg-green-100 text-green-700' :
                        'bg-blue-100 text-blue-700'
                      }`}>
                        {caseItem.organization_status}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    // Handle related_cases_count
    if (key === 'related_cases_count' && typeof value === 'number') {
      const bgColor = value > 0 ? 'bg-blue-50' : 'bg-gray-50';
      const textColor = value > 0 ? 'text-blue-600' : 'text-gray-600';
      const borderColor = value > 0 ? 'border-blue-100' : 'border-gray-200';

      return (
        <div className={`inline-flex items-center px-2.5 py-1 rounded-full ${bgColor} ${textColor} ${borderColor} border text-xs font-medium`}>
          {value} related case{value !== 1 ? 's' : ''}
        </div>
      );
    }

    // Handle case type formatting
    if (key === 'case_type') {
      return formatText(value as string);
    }

    // Handle defendant type formatting
    if (key === 'defendant_type') {
      return formatText(value as string);
    }

    // Handle type formatting for settlement management
    if (key === 'type' && reportType === 'settlementManagement') {
      return formatText(value as string);
    }

    // Handle client trust entry type formatting
    if (key === 'client_trust_entry_type' && reportType === 'clientTrust') {
      return formatText(value as string);
    }

    // Handle notes content with RichTextViewer
    if (['content', 'note'].includes(key) && typeof value === 'string') {
      return (
        <div className="max-w-2xl h-[200px] bg-white rounded-lg border border-gray-200 p-2">
          <div className="h-[180px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <CKViewer content={value} />
          </div>
        </div>
      );
    }

    if (reportType === "litigationManagement" && ["description", "status"].includes(key)) {
      return <RichTextViewer data={value as string} />;
    }

    // Handle team members array
    if (key === "team" && Array.isArray(value)) {
      return (
        <div className="flex items-center space-x-2 max-w-xl">
          {value.map((member: CaseWorker, index: number) => (
            <div
              key={index}
              className="flex items-center gap-2 p-2 rounded-md bg-gray-50/80 border border-gray-100 min-w-[180px]"
            >
              <div className="h-8 w-8 rounded-full bg-primary/15 flex items-center justify-center text-primary text-xs font-medium">
                {member.name.charAt(0).toUpperCase()}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{member.name}</span>
                <span className="text-xs text-gray-500">{member.role.replace(/_/g, ' ')}</span>
              </div>
            </div>
          ))}
        </div>
      );
    }

    // Handle contacts array for health providers
    if (key === "contacts") {
      try {
        // Parse the value if it's a string
        const contacts = typeof value === 'string' ? JSON.parse(value) : value;

        if (!Array.isArray(contacts)) return "—";

        return (
          <div className="space-y-2.5 max-w-xs">
            {contacts.map((contact, index) => (
              <div key={index} className="bg-gray-50/80 p-2.5 rounded-md border border-gray-100 shadow-sm">
                <div className="flex items-center gap-2.5 mb-1.5">
                  <div className="h-7 w-7 rounded-full bg-primary/15 flex items-center justify-center text-primary text-xs font-medium">
                    {contact.type.charAt(0)}
                  </div>
                  <span className="text-sm font-medium">{contact.name}</span>
                </div>
                {contact.phone && (
                  <div className="text-xs text-gray-600 flex items-center gap-1.5 ml-9 my-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                    {contact.phone ?
                      contact.phone.split('\n').map((phone: string, index: number) => (
                        <Fragment key={index}>
                          <PhoneLink phone={phone} />
                          {index < contact.phone.split('\n').length - 1 && <br />}
                        </Fragment>
                      ))
                      : '—'}

                  </div>
                )}
                {contact.email && (
                  <div className="text-xs text-gray-600 flex items-center gap-1.5 ml-9 my-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    {contact.email ?
                      contact.email.split('\n').map((email: string, index: number) => (
                        <Fragment key={index}>
                          <a href={`mailto:${email}`} className="text-blue-600 hover:underline font-Manrope flex items-center">
                            {email}
                          </a>
                          {index < contact.email.split('\n').length - 1 && <br />}
                        </Fragment>
                      ))
                      : '—'}
                  </div>
                )}
                {contact.address && (
                  <div className="text-xs text-gray-600 flex items-center gap-1.5 ml-9 my-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    <a
                      href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
                        [contact.address.city, contact.address.state, contact.address.zip_code]
                          .filter(Boolean)
                          .join(", ")
                      )}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {[contact.address.city, contact.address.state, contact.address.zip_code]
                        .filter(Boolean)
                        .join(", ")}
                    </a>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      } catch (error) {
        console.error('Error parsing contacts:', error);
        return "—";
      }
    }

    if (key === "_debug" && typeof value === "object") {
      const debug = value as {
        total_cases_with_duration: number;
        total_duration_days: number;
        current_cases: number;
      };

      return (
        <div className="space-y-1.5 text-xs text-gray-500 font-mono bg-gray-50/80 p-2.5 rounded-md border border-gray-100 shadow-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-600">Total duration (days):</span>
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 font-medium">
              {debug.total_duration_days}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-600">Current cases:</span>
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 font-medium">
              {debug.current_cases}
            </Badge>
          </div>
        </div>
      );
    }

    if (['statute', 'expiration'].includes(key)) {
      if (value === null || value === undefined || value === '') return '—';
      const date = new Date(value);
      const today = new Date();
      const hasExpired = date < today;
      return (
        <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${hasExpired
            ? 'bg-red-50 text-red-700 border border-red-100'
            : 'bg-green-50 text-green-700 border border-green-100'
          } shadow-sm`}>
          <svg
            className={`mr-1.5 h-2 w-2 ${hasExpired ? 'text-red-500' : 'text-green-500'}`}
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx="4" cy="4" r="3" />
          </svg>
          {format(new Date(value), 'MM/dd/yyyy')}
        </div>
      );
    }


    // Handle dates
    if (typeof value === 'string' && (
      key.includes('date') ||
      key.includes('touched') ||
      key.includes('created') ||
      key.includes('updated') ||
      key.includes('statute')
    )) {
      try {
        // Check if it's an ISO date format
        if (value.match(/^\d{4}-\d{2}-\d{2}/) || value.includes('T')) {
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            return (
              <div className="flex items-center">
                <div className="w-3 h-3 mr-1.5 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </div>
                <span>{format(date, 'MM/dd/yyyy')}</span>
              </div>
            );
          }
        }
      } catch (e) {
        // If error parsing date, fall back to original value
        console.error("Error parsing date:", e);
      }
    }

    if (key === 'expired') {
      if (value === null || value === undefined || value === '') return '—';
      const hasExpired = !!value;

      if(typeof value === 'string') return value;
      return (
        <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${hasExpired
            ? 'bg-red-50 text-red-700 border border-red-100'
            : 'bg-green-50 text-green-700 border border-green-100'
          } shadow-sm`}>
          <svg
            className={`mr-1.5 h-2 w-2 ${hasExpired ? 'text-red-500' : 'text-green-500'}`}
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx="4" cy="4" r="3" />
          </svg>
          {hasExpired ? "Yes" : "No"}
        </div>
      );
    }

    // Handle boolean values
    if (typeof value === 'boolean') {
      return value ? (
        <div className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-100 shadow-sm">
          <svg className="mr-1 h-2 w-2 text-green-500" fill="currentColor" viewBox="0 0 8 8">
            <circle cx="4" cy="4" r="3" />
          </svg>
          Yes
        </div>
      ) : (
        <div className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200 shadow-sm">
          <svg className="mr-1 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
            <circle cx="4" cy="4" r="3" />
          </svg>
          No
        </div>
      );
    }

    // Handle numeric values
    if (typeof value === 'number') {
      // Format currency values
      if (
        ['deposited', 'request', 'third_party_policy_limit', 'calculated_estimated_value', 'loss_of_wages', 'uim_policy_limit', 'medical_bills_total', 'estimated_value', 'medical_bills', 'amount', 'total',
          'total_debits', 'total_deposits', 'fee_taken',
          'fees', 'bills', 'costs', 'demand', 'offer', 'balance',
          'net_to_client', 'liens', 'total_settlements',
          'total_medical_bills', 'amount_original', 'amount_adjusted',
          'final_amount', 'original_amount', 'amount_paid', 'requested', 'total_meds',
          'est_fees',
          'cost_per_month', 'fees_per_case', 'total_fees', 'accepted_offer', 'initial_offer', 'total_settlement', 'total_fees'].includes(key)
        || key.includes('amount')
      ) {
        const color = value >= 0 && !['total_debits'].includes(key) ? 'green' : 'red';
        const bgColor = value >= 0 && !['total_debits'].includes(key) ? 'green-50' : 'red-50';
        const borderColor = value >= 0 && !['total_debits'].includes(key) ? 'green-100' : 'red-100';

        return (
          <div className={`inline-flex px-2.5 py-1 rounded-md text-${color}-600 bg-${bgColor} border border-${borderColor} font-medium shadow-sm`}>
            ${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </div>
        );
      }

      // Format percentages
      if (key.includes('percent') || key.includes('rate') || key.includes('reduction') || key.includes('conversion')) {
        return (
          <div className="inline-flex items-center px-2.5 py-1 rounded-md bg-blue-50 text-blue-600 border border-blue-100 font-medium shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 14.5l8-8.5"></path>
              <path d="M8 9v5.5h5.5"></path>
            </svg>
            {value}%
          </div>
        );
      }
    }

    if (['client_comm', 'phone', 'office_phone', 'fax', 'cell'].includes(key)) {
      if (value === null) return '—';
      return (
        <div className="inline-flex items-start px-2.5 py-1.5 rounded-md bg-blue-50/50 border border-blue-100/50">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5 mt-0.5 text-blue-500">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
          </svg>
          <div>
            {value.split('\n').map((phone: string, index: number) => (
              <Fragment key={index}>
                <PhoneLink phone={phone} />
                {index < value.split('\n').length - 1 && <br />}
              </Fragment>
            ))}
          </div>
        </div>
      );
    }

    if (['email'].includes(key)) {
      if (value === null) return '—';
      return (
        <div className="inline-flex items-start px-2.5 py-1.5 rounded-md bg-blue-50/50 border border-blue-100/50">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5 mt-0.5 text-blue-500">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
          <div>
            {value.split('\n').map((email: string, index: number) => (
              <Fragment key={index}>
                <a href={`mailto:${email}`} className="text-blue-600 hover:underline font-Manrope flex items-center">
                  {email}
                </a>
                {index < value.split('\n').length - 1 && <br />}
              </Fragment>
            ))}
          </div>
        </div>
      );
    }

    // Handle objects (like in notes report)
    if ((typeof value === 'object' || Array.isArray(value)) && value !== null) {
      if (Array.isArray(value) && key === 'tags') {
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item: { name: string, id: string }) => (
              <Badge key={item.name} variant="outline" className="bg-primary/10 text-primary border-primary/20 shadow-sm">
                {item.name}
              </Badge>
            ))}
          </div>
        );
      }
      if ('name' in value) return value.name.replace(/_/g, ' ');
      if ('id' in value) return value.id;
      return JSON.stringify(value);
    }

    // Replace underscores with spaces for string values
    if (typeof value === 'string') {
      // If this is an ID or other code that shouldn't have spaces, don't replace
      if (key === 'id' || key.endsWith('_id') || key === 'code' || key.endsWith('_code')) {
        return <span className="font-mono text-xs">{value}</span>;
      }
      return value.replace(/_/g, ' ');
    }

    // Return default value or placeholder for empty/null values
    return value !== null && value !== undefined ? value : "—";
  };

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm bg-white">
      <div className="overflow-x-auto">
        <div className="h-full flex flex-col">
          <Table className="w-full">
            <TableHeader className="sticky top-0 z-40 block after:absolute after:inset-0 after:bg-gray-50/90 after:content-[''] after:-z-10"> {/* Enhanced sticky header with pseudo-element for solid background */}
              <TableRow className="bg-gray-50/80 border-b border-gray-200 table w-full table-fixed">
                {columnKeys.map((key, index) => (
                  <TableHead
                    key={key}
                    className={cn(
                      "px-4 py-3 text-xs font-bold text-gray-700 uppercase h-11 whitespace-nowrap border-r border-gray-200 last:border-r-0",
                      key === "id" && "w-[80px]",
                      (index === 0 || key === "case") && "sticky left-0 z-50 shadow-[1px_0_0_0_#e5e7eb]",
                      (index === 0 || key === "case") && "bg-gray-50/95", // Ensure background for sticky columns
                      // Set max width for headers
                      "max-w-[200px] overflow-hidden text-ellipsis"
                    )}
                  >
                    <div
                      className="flex items-center gap-1 group cursor-pointer hover:text-primary transition-colors duration-150"
                      onClick={() => handleSort(key)}
                    >
                      {getColumnHeader(key)}
                      <div className="flex flex-col">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="10"
                          height="10"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={cn(
                            "text-gray-400 transition-opacity",
                            sortConfig?.key === key && sortConfig.direction === 'asc' ? 'opacity-100 text-primary' : 'opacity-0 group-hover:opacity-100'
                          )}
                        >
                          <path d="M18 15l-6-6-6 6"/>
                        </svg>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="10"
                          height="10"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={cn(
                            "text-gray-400 transition-opacity",
                            sortConfig?.key === key && sortConfig.direction === 'desc' ? 'opacity-100 text-primary' : 'opacity-0 group-hover:opacity-100'
                          )}
                        >
                          <path d="M6 9l6 6 6-6"/>
                        </svg>
                      </div>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody className="block overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100" style={{maxHeight: 'calc(600px - 5.5rem)'}}> {/* Made only TableBody scrollable with calculated height */}
              {sortedData.map((row, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  className="group hover:bg-gray-50/70 transition-colors duration-150 border-b border-gray-200 last:border-b-0 table w-full table-fixed"
                >
                  {columnKeys.map((key, index) => (
                    <TableCell
                      key={`${rowIndex}-${key}`}
                      className={cn(
                        // Set fixed width constraints with appropriate overflow behavior
                        "px-4 py-3.5 min-w-[120px] max-w-[250px] border-r border-gray-200/70 last:border-r-0 group/cell",
                        // Handle specific content types differently
                        ["content", "note", "description"].includes(key) ? "max-h-[200px] overflow-y-auto" : "",
                        ["team", "contacts", "related_cases"].includes(key) ? "overflow-x-auto" : "overflow-hidden text-ellipsis",
                        key === "id" && "font-mono text-xs text-gray-500 w-[80px]",
                        key === "case_status" && "font-medium",
                        (key === "case" || index === 0) && "sticky left-0 z-30 shadow-[1px_0_0_0_#e5e7eb]",
                        (key === "case" || index === 0) && "bg-white group-hover:bg-gray-50/70", // Ensure background for sticky columns
                        key !== "id" && "hover:bg-gray-50/90 transition-colors duration-150" // hover effect for each cell except ID
                      )}
                    >
                      <div className="relative w-full max-h-[300px] overflow-auto">
                        {renderCellContent(row, key)}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
            <TableFooter className="bg-gray-50/50 sticky bottom-0 z-20 block"> {/* Made footer sticky at bottom */}
              <TableRow className="border-t border-gray-200 table w-full table-fixed">
                <TableCell colSpan={columnKeys.length} className="px-4 py-2.5 text-xs text-gray-500">
                  <div className="flex items-center justify-between flex-wrap gap-2">
                    <div className="flex items-center space-x-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      </svg>
                      <span>Showing {data.length} result{data.length !== 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                      </svg>
                      <span>Click column headers to sort</span>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </div>
      </div>
    </div>
  );
};

// Pagination component
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const Pagination = ({ currentPage, totalPages, onPageChange }: PaginationProps) => {
  const [inputPage, setInputPage] = useState<string>("");

  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Show all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first, last, current, and pages around current
      if (currentPage <= 3) {
        // Current page is near the beginning
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push(null); // Ellipsis
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Current page is near the end
        pages.push(1);
        pages.push(null); // Ellipsis
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Current page is in the middle
        pages.push(1);
        pages.push(null); // Ellipsis
        pages.push(currentPage - 1);
        pages.push(currentPage);
        pages.push(currentPage + 1);
        pages.push(null); // Ellipsis
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handleDirectPageInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const pageNum = parseInt(inputPage);
      if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
        onPageChange(pageNum);
        setInputPage(""); // Clear input after navigation
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numeric input
    const value = e.target.value.replace(/[^0-9]/g, '');
    setInputPage(value);
  };

  return (
    <div className="flex items-center justify-center space-x-2 mt-6 mb-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="h-9 w-9 p-0 rounded-md border-gray-200 hover:bg-gray-50 hover:text-gray-900 transition-colors"
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {getPageNumbers().map((page, index) =>
        page === null ? (
          <span key={index} className="px-1 text-gray-400">
            •••
          </span>
        ) : (
          <Button
            key={index}
            variant={currentPage === page ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(page as number)}
            className={cn(
              "h-9 w-9 p-0 rounded-md transition-colors",
              currentPage === page
                ? "bg-primary text-white hover:bg-primary/90 shadow-sm"
                : "border-gray-200 hover:bg-gray-50 hover:text-gray-900"
            )}
          >
            {page}
          </Button>
        )
      )}

      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="h-9 w-9 p-0 rounded-md border-gray-200 hover:bg-gray-50 hover:text-gray-900 transition-colors"
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>

      <div className="text-sm text-gray-500 ml-2">
        Page {currentPage} of {totalPages}
      </div>

      <div className="flex items-center ml-4">
        <span className="text-sm text-gray-500 mr-2">Go to:</span>
        <input
          type="text"
          value={inputPage}
          onChange={handleInputChange}
          onKeyDown={handleDirectPageInput}
          className="h-9 w-16 px-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary/50"
          placeholder="#"
          aria-label="Go to page"
          title="Press Enter to go to this page"
        />
      </div>
    </div>
  );
};

// Add proper type for date parameters
interface DateSelectProps {
  date: Date | undefined;
  onSelect: (date: Date | undefined) => void;
  label: string;
  placeholder: string;
}

const DateSelect: FC<DateSelectProps> = ({ date, onSelect, label, placeholder }) => (
  <div className="flex-1">
    <label className="text-sm font-medium">{label}</label>
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-10",
            !date && "text-muted-foreground"
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4 shrink-0" />
          {date ? format(date, "LLL dd, y") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={(newDate: Date | undefined) => {
            onSelect(newDate);
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  </div>
);

// Main component
export default function CaseReportPage() {
  // Get URL search parameters and router
  const searchParams = useSearchParams();
  const router = useRouter();

  // Move useSelector to component level
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const [isExporting, setIsExporting] = useState(false);

  // State for selected report type and filters
  const [selectedReport, setSelectedReport] = useState<ReportType>(
    // Get report type from URL or use default, with null checking
    (searchParams?.get("report") as ReportType) || "caseList"
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterOpts, setFilterOptions] = useState<FilterOptions>({});
  // const [filterDialogOpen, setFilterDialogOpen] = useState(false);

  // Update the default filters
  console.log("Selected report:", filterOpts);

  const defaultFilters: CaseReportFilters = {
    page: 1,
    page_size: 25,
    view: "all_cases",
    date_field: "created_at", // Set a default date field
  };

  // Initialize state with default filters
  const [filters, setFilters] = useState<CaseReportFilters>({
    page: 1,
    page_size: 25,
    view: "all_cases",
    date_field: "created_at", // Set a default date field
  });

  // Add new state for date selection
  const [selectedFromDate, setSelectedFromDate] = useState<Date | undefined>();
  const [selectedToDate, setSelectedToDate] = useState<Date | undefined>();

  const [appliedFilters, setAppliedFilters] = useState<CaseReportFilters>(defaultFilters);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
    setFilters(prev => ({ ...prev, page: 1 }));
  }, [selectedReport]);

  // Update page in filters when currentPage changes
  useEffect(() => {
    setFilters(prev => ({ ...prev, page: currentPage }));
  }, [currentPage]);

  // Set default report type when component mounts
  useEffect(() => {
    if (!selectedReport) {
      setSelectedReport("caseList");
    }
  }, [selectedReport]);

  // Query data based on selected report type
  const {
    data: caseListData,
    isLoading: isCaseListLoading,
    isError: isCaseListError,
  } = useCaseListQuery(
    selectedReport === "caseList" ? appliedFilters : undefined
  );

  const {
    data: caseListClosedData,
    isLoading: isCaseListClosedLoading,
    isError: isCaseListClosedError,
  } = useCaseListClosedQuery(
    selectedReport === "caseListClosed" ? appliedFilters : undefined
  );

  const {
    data: caseListRejectedData,
    isLoading: isCaseListRejectedLoading,
    isError: isCaseListRejectedError,
  } = useCaseListRejectedQuery(
    selectedReport === "caseListRejected" ? appliedFilters : undefined
  );

  const {
    data: caseListDroppedData,
    isLoading: isCaseListDroppedLoading,
    isError: isCaseListDroppedError,
  } = useCaseListDroppedQuery(
    selectedReport === "caseListDropped" ? appliedFilters : undefined
  );

  const {
    data: caseStatusData,
    isLoading: isCaseStatusLoading,
    isError: isCaseStatusError,
  } = useCaseStatusQuery(
    selectedReport === "caseStatus" ? appliedFilters : undefined
  );

  const {
    data: statisticsData,
    isLoading: isStatisticsLoading,
    isError: isStatisticsError,
  } = useCaseStatisticsQuery(
    selectedReport === "statistics" ? appliedFilters : undefined
  );

  const {
    data: checkDepositsData,
    isLoading: isCheckDepositsLoading,
    isError: isCheckDepositsError,
  } = useCheckDepositsQuery(
    selectedReport === "checkDeposits" ? appliedFilters : undefined
  );

  const {
    data: clientsData,
    isLoading: isClientsLoading,
    isError: isClientsError,
  } = useClientsQuery(
    selectedReport === "clients" ? appliedFilters : undefined
  );

  const {
    data: defendantTypesData,
    isLoading: isDefendantTypesLoading,
    isError: isDefendantTypesError,
  } = useDefendantTypesQuery(
    selectedReport === "defendantTypes" ? appliedFilters : undefined
  );

  const {
    data: healthProvidersData,
    isLoading: isHealthProvidersLoading,
    isError: isHealthProvidersError,
  } = useHealthProvidersQuery(
    selectedReport === "healthProviders" ? appliedFilters : undefined
  );

  const {
    data: insuranceAdjustersData,
    isLoading: isInsuranceAdjustersLoading,
    isError: isInsuranceAdjustersError,
  } = useInsuranceAdjustersQuery(
    selectedReport === "insuranceAdjusters" ? appliedFilters : undefined
  );

  const {
    data: insuranceCompaniesData,
    isLoading: isInsuranceCompaniesLoading,
    isError: isInsuranceCompaniesError,
  } = useInsuranceCompaniesQuery(
    selectedReport === "insuranceCompanies" ? appliedFilters : undefined
  );

  const {
    data: lienHoldersData,
    isLoading: isLienHoldersLoading,
    isError: isLienHoldersError,
  } = useLienHoldersQuery(
    selectedReport === "lienHolders" ? appliedFilters : undefined
  );

  const {
    data: medpayRequestsData,
    isLoading: isMedpayRequestsLoading,
    isError: isMedpayRequestsError,
  } = useMedpayRequestsQuery(
    selectedReport === "medpayRequests" ? appliedFilters : undefined
  );

  const {
    data: medpayDepositsData,
    isLoading: isMedpayDepositsLoading,
    isError: isMedpayDepositsError,
  } = useMedpayDepositsQuery(
    selectedReport === "medpayDeposits" ? appliedFilters : undefined
  );

  const {
    data: notesData,
    isLoading: isNotesLoading,
    isError: isNotesError,
  } = useNotesQuery(
    selectedReport === "notes" ? appliedFilters : undefined
  );

  const {
    data: sourcePerformanceData,
    isLoading: isSourcePerformanceLoading,
    isError: isSourcePerformanceError,
  } = useSourcePerformanceQuery(
    selectedReport === "sourcePerformance" ? appliedFilters : undefined
  );

  const {
    data: medicalRequestsData,
    isLoading: isMedicalRequestsLoading,
    isError: isMedicalRequestsError,
  } = useMedicalRequestsQuery(
    selectedReport === "medicalRequests" ? appliedFilters : undefined
  );

  const {
    data: statuteData,
    isLoading: isStatuteLoading,
    isError: isStatuteError,
  } = useStatuteQuery(
    selectedReport === "statute" ? appliedFilters : undefined
  );

  const {
    data: demandsData,
    isLoading: isDemandsLoading,
    isError: isDemandsError,
  } = useDemandsQuery(
    selectedReport === "demands" ? appliedFilters : undefined
  );

  const {
    data: settlementData,
    isLoading: isSettlementLoading,
    isError: isSettlementError,
  } = useSettlementQuery(
    selectedReport === "settlement" ? appliedFilters : undefined
  );

  const {
    data: settlementManagementData,
    isLoading: isSettlementManagementLoading,
    isError: isSettlementManagementError,
  } = useSettlementManagementQuery(
    selectedReport === "settlementManagement" ? appliedFilters : undefined
  );

  const {
    data: litigationManagementData,
    isLoading: isLitigationManagementLoading,
    isError: isLitigationManagementError,
  } = useLitigationManagementQuery(
    selectedReport === "litigationManagement" ? appliedFilters : undefined
  );

  const {
    data: clientTrustData,
    isLoading: isClientTrustLoading,
    isError: isClientTrustError,
  } = useClientTrustQuery(
    selectedReport === "clientTrust" ? appliedFilters : undefined
  );

  const {
    data: clientTrustAttorneyFeesData,
    isLoading: isClientTrustAttorneyFeesLoading,
    isError: isClientTrustAttorneyFeesError,
  } = useClientTrustAttorneyFeesQuery(
    selectedReport === "clientTrustAttorneyFees" ? appliedFilters : undefined
  );

  const {
    data: clientTrustIssuedPaymentsData,
    isLoading: isClientTrustIssuedPaymentsLoading,
    isError: isClientTrustIssuedPaymentsError,
  } = useClientTrustIssuedPaymentsQuery(
    selectedReport === "clientTrustIssuedPayments" ? appliedFilters : undefined
  );

  const {
    data: clientTrustCheckDepositsData,
    isLoading: isClientTrustCheckDepositsLoading,
    isError: isClientTrustCheckDepositsError,
  } = useClientTrustCheckDepositsQuery(
    selectedReport === "clientTrustCheckDeposits" ? appliedFilters : undefined
  );

  const {
    data: clientTrustConsolidatedData,
    isLoading: isClientTrustConsolidatedLoading,
    isError: isClientTrustConsolidatedError,
  } = useClientTrustConsolidatedQuery(
    selectedReport === "clientTrustConsolidated" ? appliedFilters : undefined
  );

  const {
    data: caseReviewData,
    isLoading: isCaseReviewLoading,
    isError: isCaseReviewError,
  } = useCaseReviewQuery(
    selectedReport === "caseReview" ? appliedFilters : undefined
  );

  const {
    data: clientsOpenData,
    isLoading: isClientsOpenLoading,
    isError: isClientsOpenError,
  } = useClientsOpenQuery(
    selectedReport === "clientsOpen" ? appliedFilters : undefined
  );

  const {
    data: clientsReferredInData,
    isLoading: isClientsReferredInLoading,
    isError: isClientsReferredInError,
  } = useClientsReferredInQuery(
    selectedReport === "clientsReferredIn" ? appliedFilters : undefined
  );

  const {
    data: clientsSubbedOutData,
    isLoading: isClientsSubbedOutLoading,
    isError: isClientsSubbedOutError,
  } = useClientsSubbedOutQuery(
    selectedReport === "clientsSubbedOut" ? appliedFilters : undefined
  );

  const {
    data: clientsIntakeReadyData,
    isLoading: isClientsIntakeReadyLoading,
    isError: isClientsIntakeReadyError,
  } = useClientsIntakeReadyQuery(
    selectedReport === "clientsIntakeReady" ? appliedFilters : undefined
  );

  const {
    data: clientsRetainedData,
    isLoading: isClientsRetainedLoading,
    isError: isClientsRetainedError,
  } = useClientsRetainedQuery(
    selectedReport === "clientsRetained" ? appliedFilters : undefined
  );

  // Add case costs query after the existing query declarations
  const {
    data: caseCostsData,
    isLoading: isCaseCostsLoading,
    isError: isCaseCostsError,
  } = useCaseCostsQuery(
    selectedReport === "caseCosts" ? appliedFilters : undefined
  );

  // Add after the caseCosts query
  const {
    data: contactsData,
    isLoading: isContactsLoading,
    isError: isContactsError,
  } = useContactsQuery(
    selectedReport === "contacts" ? appliedFilters : undefined
  );

  // Add the case events calendar query hook
  const {
    data: caseEventsCalendarData,
    isLoading: isCaseEventsCalendarLoading,
    isError: isCaseEventsCalendarError,
  } = useCaseEventsCalendarQuery(
    selectedReport === "caseEventsCalendar" ? appliedFilters : undefined
  );

  // Add the query hook for Estimated Value Report
  const {
    data: estimatedValueData,
    isLoading: isEstimatedValueLoading,
    isError: isEstimatedValueError,
  } = useEstimatedValueQuery(
    selectedReport === "estimatedValue" ? appliedFilters : undefined
  );

  // Add debug logging to track data flow
  useEffect(() => {
    const currentData = getReportData();
    console.log("Data received:", currentData);
    console.log("Selected report:", selectedReport);
    console.log("Applied filters:", appliedFilters);
  }, [selectedReport, appliedFilters]);

  // Update the getSummaryData function for statistics

  const isLoading = () => {
    if (!selectedReport) return false;

    switch (selectedReport) {
      case 'caseList':
        return isCaseListLoading;
      case 'caseListClosed':
        return isCaseListClosedLoading;
      case 'caseListRejected':
        return isCaseListRejectedLoading;
      case 'caseListDropped':
        return isCaseListDroppedLoading;
      case 'caseStatus':
        return isCaseStatusLoading;
      case 'statistics':
        return isStatisticsLoading;
      case 'checkDeposits':
        return isCheckDepositsLoading;
      case 'clients':
        return isClientsLoading;
      case 'clientsOpen':
        return isClientsOpenLoading;
      case 'clientsReferredIn':
        return isClientsReferredInLoading;
      case 'clientsSubbedOut':
        return isClientsSubbedOutLoading;
      case 'clientsIntakeReady':
        return isClientsIntakeReadyLoading;
      case 'clientsRetained':
        return isClientsRetainedLoading;
      case 'defendantTypes':
        return isDefendantTypesLoading;
      case 'healthProviders':
        return isHealthProvidersLoading;
      case 'insuranceAdjusters':
        return isInsuranceAdjustersLoading;
      case 'insuranceCompanies':
        return isInsuranceCompaniesLoading;
      case 'lienHolders':
        return isLienHoldersLoading;
      case 'medpayRequests':
        return isMedpayRequestsLoading;
      case 'medpayDeposits':
        return isMedpayDepositsLoading;
      case 'notes':
        return isNotesLoading;
      case 'sourcePerformance':
        return isSourcePerformanceLoading;
      case 'medicalRequests':
        return isMedicalRequestsLoading;
      case 'statute':
        return isStatuteLoading;
      case 'demands':
        return isDemandsLoading;
      case 'settlement':
        return isSettlementLoading;
      case 'settlementManagement':
        return isSettlementManagementLoading;
      case 'litigationManagement':
        return isLitigationManagementLoading;
      case 'clientTrust':
        return isClientTrustLoading;
      case 'clientTrustAttorneyFees':
        return isClientTrustAttorneyFeesLoading;
      case 'clientTrustIssuedPayments':
        return isClientTrustIssuedPaymentsLoading;
      case 'clientTrustCheckDeposits':
        return isClientTrustCheckDepositsLoading;
      case 'clientTrustConsolidated':
        return isClientTrustConsolidatedLoading;
      case 'caseReview':
        return isCaseReviewLoading;
      case 'caseCosts':
        return isCaseCostsLoading;
      case 'contacts':
        return isContactsLoading;
      case 'caseEventsCalendar':
        return isCaseEventsCalendarLoading;
      case 'estimatedValue':
        return isEstimatedValueLoading;
      default:
        return false;
    }
  };

  const isError = () => {
    if (!selectedReport) return false;

    switch (selectedReport) {
      case 'caseList':
        return isCaseListError;
      case 'caseListClosed':
        return isCaseListClosedError;
      case 'caseListRejected':
        return isCaseListRejectedError;
      case 'caseListDropped':
        return isCaseListDroppedError;
      case 'caseStatus':
        return isCaseStatusError;
      case 'statistics':
        return isStatisticsError;
      case 'checkDeposits':
        return isCheckDepositsError;
      case 'clients':
        return isClientsError;
      case 'clientsOpen':
        return isClientsOpenError;
      case 'clientsReferredIn':
        return isClientsReferredInError;
      case 'clientsSubbedOut':
        return isClientsSubbedOutError;
      case 'clientsIntakeReady':
        return isClientsIntakeReadyError;
      case 'clientsRetained':
        return isClientsRetainedError;
      case 'defendantTypes':
        return isDefendantTypesError;
      case 'healthProviders':
        return isHealthProvidersError;
      case 'insuranceAdjusters':
        return isInsuranceAdjustersError;
      case 'insuranceCompanies':
        return isInsuranceCompaniesError;
      case 'lienHolders':
        return isLienHoldersError;
      case 'medpayRequests':
        return isMedpayRequestsError;
      case 'medpayDeposits':
        return isMedpayDepositsError;
      case 'notes':
        return isNotesError;
      case 'sourcePerformance':
        return isSourcePerformanceError;
      case 'medicalRequests':
        return isMedicalRequestsError;
      case 'statute':
        return isStatuteError;
      case 'demands':
        return isDemandsError;
      case 'settlement':
        return isSettlementError;
      case 'settlementManagement':
        return isSettlementManagementError;
      case 'litigationManagement':
        return isLitigationManagementError;
      case 'clientTrust':
        return isClientTrustError;
      case 'clientTrustAttorneyFees':
        return isClientTrustAttorneyFeesError;
      case 'clientTrustIssuedPayments':
        return isClientTrustIssuedPaymentsError;
      case 'clientTrustCheckDeposits':
        return isClientTrustCheckDepositsError;
      case 'clientTrustConsolidated':
        return isClientTrustConsolidatedError;
      case 'caseReview':
        return isCaseReviewError;
      case 'caseCosts':
        return isCaseCostsError;
      case 'contacts':
        return isContactsError;
      case 'caseEventsCalendar':
        return isCaseEventsCalendarError;
      case 'estimatedValue':
        return isEstimatedValueError;
      default:
        return false;
    }
  };

  // Update the getReportData function to handle the new report type
  const getReportData = () => {
    if (!selectedReport) return null;

    // Add debugging for client report types
    if (selectedReport.startsWith('clients')) {
      console.log(`Getting report data for client report type: ${selectedReport}`);
    }

    switch (selectedReport) {
      case 'caseList':
        return caseListData;
      case 'caseListClosed':
        return caseListClosedData;
      case 'caseListRejected':
        return caseListRejectedData;
      case 'caseListDropped':
        return caseListDroppedData;
      case 'caseStatus':
        return caseStatusData;
      case 'statistics':
        return statisticsData;
      case 'checkDeposits':
        return checkDepositsData;
      case 'clients':
        return clientsData;
      case 'clientsOpen':
        return clientsOpenData;
      case 'clientsReferredIn':
        return clientsReferredInData;
      case 'clientsSubbedOut':
        return clientsSubbedOutData;
      case 'clientsIntakeReady':
        return clientsIntakeReadyData;
      case 'clientsRetained':
        return clientsRetainedData;
      case 'defendantTypes':
        return defendantTypesData;
      case 'healthProviders':
        return healthProvidersData;
      case 'insuranceAdjusters':
        return insuranceAdjustersData;
      case 'insuranceCompanies':
        return insuranceCompaniesData;
      case 'lienHolders':
        return lienHoldersData;
      case 'medpayRequests':
        return medpayRequestsData;
      case 'medpayDeposits':
        return medpayDepositsData;
      case 'notes':
        return notesData;
      case 'sourcePerformance':
        return sourcePerformanceData;
      case 'medicalRequests':
        return medicalRequestsData;
      case 'statute':
        return statuteData;
      case 'demands':
        return demandsData;
      case 'settlement':
        return settlementData;
      case 'settlementManagement':
        return settlementManagementData;
      case 'litigationManagement':
        return litigationManagementData;
      case 'clientTrust':
        return clientTrustData;
      case 'clientTrustAttorneyFees':
        return clientTrustAttorneyFeesData;
      case 'clientTrustIssuedPayments':
        return clientTrustIssuedPaymentsData;
      case 'clientTrustCheckDeposits':
        return clientTrustCheckDepositsData;
      case 'clientTrustConsolidated':
        return clientTrustConsolidatedData;
      case 'caseReview':
        return caseReviewData;
      case 'caseCosts':
        return caseCostsData;
      case 'contacts':
        return contactsData;
      case 'caseEventsCalendar':
        return caseEventsCalendarData;
      case 'estimatedValue':
        return estimatedValueData;
      default:
        return null;
    }
  };

  const SummaryTiles = ({ data, reportType, onTileClick = () => { } }: SummaryTilesProps) => {
    console.log(onTileClick, "onTileClick");
    const [expandedCards, setExpandedCards] = useState<Record<string, boolean>>({});

    // Add more detailed debugging
    console.log(`SummaryTiles received data for report type: ${reportType}`, data);

    if (!data) {
      console.log(`No data provided to SummaryTiles for report type: ${reportType}`);
      return null;
    }

    // Check if this is a client report type
    const isClientReport = reportType.startsWith('clients');
    if (isClientReport) {
      console.log(`This is a client report: ${reportType}`);
      console.log('Client summary data:', data);
    }

    const toggleCardExpansion = (cardId: string) => {
      setExpandedCards(prev => ({
        ...prev,
        [cardId]: !prev[cardId]
      }));
    };

    // Management report cases are now handled separately
    console.log("Incoming report type for summary tiles", reportType);

    const renderTiles = () => {
      console.log(`renderTiles called for report type: ${reportType}`);
      switch (reportType) {
        case 'caseList':
          const caseListSummary = data as CaseListSummary;
          const reportPrefix = reportType.replace(/([A-Z])/g, '-$1').toLowerCase();

          return (
            <>
              <SummaryCard
                title="Total Cases"
                value={caseListSummary.total_cases}
                cardId={`${reportPrefix}-totalCases`}
                isExpanded={expandedCards[`${reportPrefix}-totalCases`]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Average Case Age"
                value={`${caseListSummary.average_case_age} days`}
                cardId={`${reportPrefix}-avgAge`}
                isExpanded={expandedCards[`${reportPrefix}-avgAge`]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Touched Last 7 Days Count"
                value={`${caseListSummary.touched_recently_7_days_count}`}
                cardId={`${reportPrefix}-touched`}
                isExpanded={expandedCards[`${reportPrefix}-touched`]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Touched Last 7 Days Percentage"
                value={`${caseListSummary.touched_last_7_days_percentage}%`}
                cardId={`${reportPrefix}-touched`}
                isExpanded={expandedCards[`${reportPrefix}-touched`]}
                onToggleExpand={toggleCardExpansion}
              />

              <SummaryCard
                title="Aged Over 1 Year"
                value={`${caseListSummary.aged_over_1_year}%`}
                cardId={`${reportPrefix}-aged`}
                isExpanded={expandedCards[`${reportPrefix}-aged`]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'caseListClosed':
        case 'caseListRejected':
        case 'caseListDropped':
          const caseClosedListSummary = data as CaseListSummary;
          const reportClosedPrefix = reportType.replace(/([A-Z])/g, '-$1').toLowerCase();

          return (
            <>
              <SummaryCard
                title="Total Cases"
                value={caseClosedListSummary.total_cases}
                cardId={`${reportClosedPrefix}-totalCases`}
                isExpanded={expandedCards[`${reportClosedPrefix}-totalCases`]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Average Case Age"
                value={`${caseClosedListSummary.average_case_age} days`}
                cardId={`${reportClosedPrefix}-avgAge`}
                isExpanded={expandedCards[`${reportClosedPrefix}-avgAge`]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'caseStatus':
          const summaryDataT = (data as CaseStatusSummary);

          return (
            <>
              <SummaryCard
                title="Longest Average Status"
                value={summaryDataT.longest_average_status.status}
                subtitle={`${summaryDataT.longest_average_status.days} days`}
                cardId="caseStatus-longest"
                isExpanded={expandedCards["caseStatus-longest"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Shortest Average Status"
                value={summaryDataT.shortest_average_status.status}
                subtitle={`${summaryDataT.shortest_average_status.days} days`}
                cardId="caseStatus-shortest"
                isExpanded={expandedCards["caseStatus-shortest"]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'statistics':
          return (
            <>
              <SummaryCard
                title="Total Cases"
                value={(data as StatisticsSummary).total}
                cardId="statistics-total"
                isExpanded={expandedCards["statistics-total"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Active Cases"
                value={(data as StatisticsSummary).active_cases}
                cardId="statistics-activeCases"
                isExpanded={expandedCards["statistics-activeCases"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Settlements"
                value={(data as StatisticsSummary).settlements}
                cardId="statistics-settlements"
                isExpanded={expandedCards["statistics-settlements"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Settlement Total"
                value={`$${(data as StatisticsSummary)?.settlement_total?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="statistics-settlementTotal"
                isExpanded={expandedCards["statistics-settlementTotal"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Lawsuits Filed"
                value={(data as StatisticsSummary).lawsuits_filed}
                cardId="statistics-lawsuitsFiled"
                isExpanded={expandedCards["statistics-lawsuitsFiled"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Referrals"
                value={(data as StatisticsSummary).referrals}
                cardId="statistics-referrals"
                isExpanded={expandedCards["statistics-referrals"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Dropped Cases"
                value={(data as StatisticsSummary).dropped}
                cardId="statistics-dropped"
                isExpanded={expandedCards["statistics-dropped"]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'checkDeposits':
          const summaryData = data as CheckDepositsSummary;

          return (
            <>
              <SummaryCard
                title="Total Checks"
                value={summaryData.checks ? summaryData.checks : "—"}
                cardId="checkDeposits-totalChecks"
                isExpanded={expandedCards["checkDeposits-totalChecks"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Total Amount"
                value={`$${summaryData.total !== undefined ? summaryData.total.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : "—"}`}
                cardId="checkDeposits-totalAmount"
                isExpanded={expandedCards["checkDeposits-totalAmount"]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'clients':
        case 'clientsOpen':
        case 'clientsReferredIn':
        case 'clientsSubbedOut':
        case 'clientsIntakeReady':
        case 'clientsRetained':
          console.log(`Rendering summary tiles for client report type: ${reportType}`);
          console.log('Raw data in renderTiles:', data);
          const clientsSummary = data as ClientsSummary;
          console.log('Client summary data after casting:', clientsSummary);
          console.log('Total clients value:', clientsSummary.total_clients);
          console.log('Birthdays this month value:', clientsSummary.birthdays_this_month);

          // Check if the data has the expected structure
          if (!clientsSummary || typeof clientsSummary.total_clients === 'undefined') {
            console.error(`Missing expected data for ${reportType}`, clientsSummary);
            return (
              <div className="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                Error: Missing summary data for this report type
              </div>
            );
          }

          const clientReportPrefix = reportType.replace(/([A-Z])/g, '-$1').toLowerCase();

          return (
            <>
              <SummaryCard
                title="Total Clients"
                value={clientsSummary.total_clients}
                cardId={`${clientReportPrefix}-totalClients`}
                isExpanded={expandedCards[`${clientReportPrefix}-totalClients`]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Birthdays This Month"
                value={clientsSummary.birthdays_this_month}
                cardId={`${clientReportPrefix}-birthdaysThisMonth`}
                isExpanded={expandedCards[`${clientReportPrefix}-birthdaysThisMonth`]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'defendantTypes':
          return (
            <>
              <SummaryCard
                title="Public Entity"
                value={(data as DefendantTypesSummary).public_entity}
                cardId="defendantTypes-publicEntity"
                isExpanded={expandedCards["defendantTypes-publicEntity"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Federal Government Entity"
                value={(data as DefendantTypesSummary).fed_gov_entity}
                cardId="defendantTypes-fedGovEntity"
                isExpanded={expandedCards["defendantTypes-fedGovEntity"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Commercial"
                value={(data as DefendantTypesSummary).commercial}
                cardId="defendantTypes-commercial"
                isExpanded={expandedCards["defendantTypes-commercial"]}
                onToggleExpand={toggleCardExpansion}
              />
              <SummaryCard
                title="Private Party"
                value={(data as DefendantTypesSummary).private_party}
                cardId="defendantTypes-privateParty"
                isExpanded={expandedCards["defendantTypes-privateParty"]}
                onToggleExpand={toggleCardExpansion}
              />
            </>
          );
        case 'healthProviders':
          return (
            <>
              <SummaryCard
                title="Total Providers"
                value={(data as HealthProvidersSummary).total_providers}
                cardId="healthProviders-totalProviders"
              />
              <SummaryCard
                title="Accepts Liens"
                value={(data as HealthProvidersSummary).accepts_liens}
                cardId="healthProviders-acceptsLiens"
              />
              <SummaryCard
                title="No Records Service"
                value={(data as HealthProvidersSummary).no_records_service}
                cardId="healthProviders-noRecordsService"
              />
              <SummaryCard
                title="Do Not Use"
                value={(data as HealthProvidersSummary).do_not_use}
                cardId="healthProviders-doNotUse"
              />
              {Object.entries((data as HealthProvidersSummary).by_specialty as Record<string, number>).map(([specialty, count]) => (
                <SummaryCard
                  key={specialty}
                  title={specialty}
                  value={count}
                  cardId={`healthProviders-specialty-${specialty}`}
                  expandable={true}
                  expandedContent={
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      {Object.entries((data as HealthProvidersSummary).by_specialty as Record<string, number>).map(([specialty, count]) => (
                        <div key={specialty} className="text-sm text-gray-500">{specialty}: {count}</div>
                      ))}
                    </div>
                  }
                />
              ))}
            </>
          );
        case 'insuranceAdjusters':
          return (
            <>
              <SummaryCard
                title="Total Adjusters"
                value={(data as InsuranceAdjustersSummary).total_adjusters}
                cardId="insuranceAdjusters-totalAdjusters"
              />
              {Object.entries((data as InsuranceAdjustersSummary).by_company as Record<string, number>).map(([company, count]) => (
                <SummaryCard
                  key={company}
                  title={company}
                  value={count}
                  cardId={`insuranceAdjusters-company-${company}`}
                  expandable={true}
                  expandedContent={
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      {Object.entries((data as InsuranceAdjustersSummary).by_company as Record<string, number>).map(([company, count]) => (
                        <div key={company} className="text-sm text-gray-500">{company}: {count}</div>
                      ))}
                    </div>
                  }
                />
              ))}
            </>
          );
        case 'insuranceCompanies':
          return (
            <>
              <SummaryCard
                title="Total Companies"
                value={(data as InsuranceCompaniesSummary).total_companies}
                cardId="insuranceCompanies-totalCompanies"
              />
              <SummaryCard
                title="Client Insurance Companies"
                value={(data as InsuranceCompaniesSummary).by_type.client}
                cardId="insuranceCompanies-clientInsuranceCompanies"
              />
              <SummaryCard
                title="Defendant Insurance Companies"
                value={(data as InsuranceCompaniesSummary).by_type.defendant}
                cardId="insuranceCompanies-defendantInsuranceCompanies"
              />
            </>
          );
        case 'lienHolders':
          console.log((data as LienHoldersSummary), "data as LienHoldersSummary");
          return (
            <>
              <SummaryCard
                title="Total Lien Holders"
                value={(data as LienHoldersSummary).total_lien_holders}
                cardId="lienHolders-totalLienHolders"
              />
              <SummaryCard
                title="Total Health Liens"
                value={(data as LienHoldersSummary).total_health_liens}
                cardId="lienHolders-totalHealthLiens"
              />
            </>
          );
        case 'medpayRequests':
          console.log("Medpay Requests", data);
          return (
            <>
              <SummaryCard
                title="MedPay Requests"
                value={(data as MedpayRequestsSummary).medpay_requests}
                cardId="medpayRequests-medpayRequests"
              />
              <SummaryCard
                title="Total Demanded"
                value={`$${(data as MedpayRequestsSummary).total_demanded?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="medpayRequests-totalDemanded"
              />
            </>
          );
        case 'medpayDeposits':
          console.log("Medpay Deposits", data);
          const medpayDepositsSummary = data as MedpayDepositsSummary;
          return (
            <>
              <SummaryCard
                title="Total Deposits"
                value={medpayDepositsSummary.total_deposits}
                cardId="medpayDeposits-totalDeposits"
              />
              <SummaryCard
                title="Total Amount"
                value={`$${medpayDepositsSummary.total_amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="medpayDeposits-totalAmount"
              />
              <SummaryCard
                title="Paid Amount"
                value={`$${medpayDepositsSummary.paid_amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="medpayDeposits-paidAmount"
              />
              <SummaryCard
                title="Pending Amount"
                value={`$${medpayDepositsSummary.pending_amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="medpayDeposits-pendingAmount"
              />
              <SummaryCard
                title="Client MedPay"
                value={`$${medpayDepositsSummary.client_medpay_amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="medpayDeposits-clientMedpay"
                color="blue"
              />
              <SummaryCard
                title="Third Party MedPay"
                value={`$${medpayDepositsSummary.third_party_medpay_amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="medpayDeposits-thirdPartyMedpay"
                color="purple"
              />
            </>
          );
        case 'notes':
          const notesSummary = data as NotesSummary;
          return (
            <>
              <SummaryCard
                title="Total Notes"
                value={notesSummary.total_notes}
                cardId="notes-totalNotes"
              />
              {Object.entries(notesSummary.by_tags).slice(0, 3).map(([tag, count]) => (
                <SummaryCard
                  key={tag}
                  title={tag}
                  value={count}
                  cardId={`notes-tag-${tag}`}
                  expandable={true}
                  expandedContent={
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      {Object.entries(notesSummary.by_tags).map(([tag, count]) => (
                        <div key={tag} className="text-sm text-gray-500">{tag}: {count}</div>
                      ))}
                    </div>
                  }
                />
              ))}
            </>
          );

        // case 'sourcePerformance':
        //   const sourceSummary = data as SourcePerformanceSummary;
        //   return (
        //     <>
        //       <SummaryCard
        //         title="Total Leads"
        //         value={sourceSummary.total_leads}
        //         cardId="sourcePerformance-totalLeads"
        //       />
        //       <SummaryCard
        //         title="Total Retained"
        //         value={sourceSummary.total_retained}
        //         cardId="sourcePerformance-totalRetained"
        //       />
        //       <SummaryCard
        //         title="Retention Rate"
        //         value={`${sourceSummary.retention_rate}%`}
        //         cardId="sourcePerformance-retentionRate"
        //       />
        //       <SummaryCard
        //         title="Total Settlements"
        //         value={`$${sourceSummary.total_settlements?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
        //         cardId="sourcePerformance-totalSettlements"
        //       />
        //       <SummaryCard
        //         title="Total Fees"
        //         value={`$${sourceSummary.total_fees?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
        //         cardId="sourcePerformance-totalFees"
        //       />
        //       <SummaryCard
        //         title="Settled Cases"
        //         value={sourceSummary.total_settled_cases}
        //         cardId="sourcePerformance-settledCases"
        //       />
        //     </>
        //   );

        case 'medicalRequests':
          const medicalSummary = data as MedicalRequestsSummary;
          return (
            <>
              <SummaryCard
                title="Total Requests"
                value={medicalSummary.total_requests}
                cardId="medicalRequests-totalRequests"
              />
              <SummaryCard
                title="Billing Requests"
                value={medicalSummary.billing_requests}
                cardId="medicalRequests-billingRequests"
              />
              <SummaryCard
                title="Records Requests"
                value={medicalSummary.records_requests}
                cardId="medicalRequests-recordsRequests"
              />
            </>
          );

        case 'statute':
          const statuteSummary = data as StatuteSummary;
          return (
            <>
              <SummaryCard
                title="Next 7 Days"
                value={statuteSummary.next_7_days}
                cardId="statute-next7Days"
              />
              <SummaryCard
                title="Next 30 Days"
                value={statuteSummary.next_30_days}
                cardId="statute-next30Days"
              />
              <SummaryCard
                title="Next 60 Days"
                value={statuteSummary.next_60_days}
                cardId="statute-next60Days"
              />
              <SummaryCard
                title="Next 90 Days"
                value={statuteSummary.next_90_days}
                cardId="statute-next90Days"
              />
              <SummaryCard
                title="Total"
                value={statuteSummary.total}
                cardId="statute-total"
              />
            </>
          );

        case 'demands':
          const demandsSummary = data as DemandsSummary;
          return (
            <>
              <SummaryCard
                title="Demands Sent"
                value={demandsSummary.demands_sent}
                cardId="demands-demandsSent"
              />
              <SummaryCard
                title="Total Demanded"
                value={`$${demandsSummary.total_demanded?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="demands-totalDemanded"
              />
              <SummaryCard
                title="Third Party Demands"
                value={demandsSummary.third_party_demands}
                cardId="demands-thirdPartyDemands"
              />
              <SummaryCard
                title="UM/UIM Demands"
                value={demandsSummary.um_uim_demands}
                cardId="demands-umUimDemands"
              />
              <SummaryCard
                title="Expired"
                value={demandsSummary.expired}
                cardId="demands-expired"
                color="red"
              />
            </>
          );

        case 'settlement':
          const settlementSummary = data as SettlementSummary;
          return (
            <>
              <SummaryCard
                title="Total Settlements"
                value={`$${settlementSummary.total?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="settlement-totalSettlements"
              />
              <SummaryCard
                title="Offers"
                value={settlementSummary.offers}
                cardId="settlement-offers"
              />
              <SummaryCard
                title="Average Per Case"
                value={`$${settlementSummary.avg_per_case?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="settlement-avgPerCase"
              />
            </>
          );

        case 'settlementManagement':
        case 'litigationManagement':
        case 'sourcePerformance':
          const reportData = getReportData();
          // These cases are now handled by the ManagementReportTiles component
          return <ManagementReportTiles data={reportData as (SettlementManagementResponse | LitigationManagementResponse | SourcePerformanceResponse)} reportType={selectedReport} onTileClick={handleTileClick} />;

        case 'clientTrust':
          const clientTrustSummary = data as ClientTrustSummary;
          return (
            <>
              <SummaryCard
                title="Total Deposits"
                value={`$${clientTrustSummary.total_deposits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="clientTrust-totalDeposits"
              />
              <SummaryCard
                title="Total Debits"
                color="red"
                value={`$${clientTrustSummary.total_debits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="clientTrust-totalDebits"
              />
              <SummaryCard
                title="Current Balance"
                value={`$${clientTrustSummary.current_balance?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="clientTrust-currentBalance"
                color={clientTrustSummary.current_balance > 0 ? 'green' : 'red'}
              />
              <SummaryCard
                title="Total Entries"
                value={clientTrustSummary.total_entries.toString()}
                cardId="clientTrust-totalEntries"
              />
              <SummaryCard
                title="Trust Count"
                value={clientTrustSummary.trust_count.toString()}
                cardId="clientTrust-trustCount"
              />
            </>
          );
        
        case 'clientTrustAttorneyFees':
          const clientTrustAttorneyFeesSummary = data as ClientTrustSummary;
          return (
            <>
              <SummaryCard
                title="Total Fees In"
                color="green"
                value={`$${clientTrustAttorneyFeesSummary.total_debits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="clientTrust-totalDebits"
              />
              <SummaryCard
                title="Checks In"
                value={clientTrustAttorneyFeesSummary.total_entries.toString()}
                cardId="clientTrust-totalEntries"
              />
            </>
          );
        
          case 'clientTrustIssuedPayments':
            const clientTrustIssuedPaymentsSummary = data as ClientTrustSummary;
            return (
              <>
                <SummaryCard
                  title="Total Issued Payments"
                  color="green"
                  value={`$${clientTrustIssuedPaymentsSummary.total_debits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                  cardId="clientTrust-totalDebits"
                />
                <SummaryCard
                  title="Checks In"
                  value={clientTrustIssuedPaymentsSummary.total_entries.toString()}
                  cardId="clientTrust-totalEntries"
                />
              </>
            );

        case 'clientTrustConsolidated':
          const clientTrustConsolidatedSummary = data as ClientTrustConsolidatedSummary;
            return (
              <>
                <SummaryCard
                  title="Total Deposits"
                  value={`$${clientTrustConsolidatedSummary.total_deposits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                  cardId="clientTrust-totalDeposits"
                />
                <SummaryCard
                  title="Total Debits"
                  color="red"
                  value={`$${clientTrustConsolidatedSummary.total_debits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                  cardId="clientTrust-totalDebits"
                />
                <SummaryCard
                  title="Current Balance"
                  value={`$${clientTrustConsolidatedSummary.current_balance?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                  cardId="clientTrust-currentBalance"
                  color={clientTrustConsolidatedSummary.current_balance > 0 ? 'green' : 'red'}
                />
                <SummaryCard
                  title="Total Trusts"
                  value={clientTrustConsolidatedSummary.case_count.toString()}
                  cardId="clientTrust-trustCount"
                />
              </>
            );
        case 'clientTrustCheckDeposits':
          const clientTrustCheckDepositsSummary = data as ClientTrustSummary;
          return (
            <>
              <SummaryCard
                title="Total Check Deposits"
                value={`$${clientTrustCheckDepositsSummary.total_deposits?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="clientTrust-totalDeposits"
              />
              <SummaryCard
                title="Checks"
                value={clientTrustCheckDepositsSummary.total_entries.toString()}
                cardId="clientTrust-totalEntries"
              />
            </>
          );
        

        case 'caseReview':
          const caseReviewSummary = data as CaseReviewSummary;
          return (
            <>
              <SummaryCard
                title="Total Cases"
                value={caseReviewSummary.total_cases}
                cardId="caseReview-totalCases"
              />
              <SummaryCard
                title="Average Case Age"
                value={caseReviewSummary.average_case_age}
                cardId="caseReview-averageCaseAge"
              />
              <SummaryCard
                title="Cases Touched (Last 7 Days)"
                value={`${caseReviewSummary.touched_last_7_days}%`}
                cardId="caseReview-touchedLast7Days"
              />
              <SummaryCard
                title="Cases Over 1 Year"
                value={`${caseReviewSummary.aged_over_1_year}%`}
                cardId="caseReview-agedOver1Year"
              />
            </>
          );

        case 'caseCosts':
          const caseCostsSummary = data as CaseCostSummary;
          return (
            <>
              <SummaryCard
                title="Total Case Costs"
                value={`$${caseCostsSummary.total_costs.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="caseCosts-totalCosts"
              />
              <SummaryCard
                title="Total Entries"
                value={caseCostsSummary.total_entries}
                cardId="caseCosts-totalEntries"
              />
              <SummaryCard
                title="Case Count"
                value={caseCostsSummary.case_count}
                cardId="caseCosts-caseCount"
              />
            </>
          );

        case 'contacts':
          const contactsSummary = data as ContactsSummary;
          return (
            <>
              <SummaryCard
                title="Total Contacts"
                value={contactsSummary.total_contacts}
                cardId="contacts-totalContacts"
              />
              {contactsSummary.contacts_by_type &&
                Object.entries(contactsSummary.contacts_by_type)
                  .slice(0, 4)
                  .map(([type, count]) => (
                    <SummaryCard
                      key={type}
                      title={type.replace(/_/g, ' ')}
                      value={count}
                      cardId={`contacts-type-${type}`}
                      color="blue"
                    />
                  ))
              }
            </>
          );
        case 'caseEventsCalendar':
          const eventsSummary = data as CaseEventsCalendarSummary;
          return (
            <>
              <SummaryCard
                title="Today's Events"
                value={eventsSummary.today}
                cardId="caseEventsCalendar-today"
              />
              <SummaryCard
                title="Next 7 Days"
                value={eventsSummary.next_7_days}
                cardId="caseEventsCalendar-next7days"
              />
              <SummaryCard
                title="Total Events"
                value={eventsSummary.total_events}
                cardId="caseEventsCalendar-totalEvents"
              />
            </>
          );
        case 'estimatedValue':
          const estimatedValueSummary = data as EstimatedValueSummary;
          return (
            <>
              <SummaryCard
                title="Total Cases"
                value={estimatedValueSummary.total_cases}
                cardId="estimatedValue-totalCases"
              />
              <SummaryCard
                title="Total Calculated Estimated Value"
                value={`$${estimatedValueSummary.total_calculated_estimated_value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="estimatedValue-totalCalculatedEstimatedValue"
              />
              <SummaryCard
                title="Total Manual Estimated Value"
                value={`$${estimatedValueSummary.total_manual_estimated_value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                cardId="estimatedValue-totalManualEstimatedValue"
              />
              
              
            </>
          );
        default:
          return (
            <>
              {Object.entries(data).map(([key, value]) => (
                <SummaryCard
                  key={key}
                  title={key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  value={typeof value === 'number'
                    ? value.toLocaleString()
                    : (value as ReactNode)}
                  cardId={`default-${key}`}
                  expandable={true}
                  expandedContent={
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      {Object.entries(data).map(([k, v]) => (
                        <div key={k} className="text-sm text-gray-500 mb-2">
                          {k.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                          {typeof v === 'number'
                            ? ` ${v.toLocaleString()}`
                            : ` ${v}`}
                        </div>
                      ))}
                    </div>
                  }
                  isExpanded={expandedCards[`default-${key}`]}
                  onToggleExpand={toggleCardExpansion}
                />
              ))}
            </>
          );
      }
    };

    // Determine the number of columns based on the number of tiles
    const getGridConfig = () => {

      // if (childCount <= 2) return {
      //   cols: "grid-cols-1 sm:grid-cols-2",
      //   gap: "gap-3"
      // };
      // if (childCount <= 4) return {
      //   cols: "grid-cols-2 md:grid-cols-4",
      //   gap: "gap-3"
      // };
      // if (childCount <= 5) return {
      //   cols: "grid-cols-2 md:grid-cols-5",
      //   gap: "gap-3"
      // };
      return {
        cols: "grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5",
        gap: "gap-3"
      };
    };

    const { cols, gap } = getGridConfig();

    console.log(cols, gap, "cols and gap");
    return (
      <div className={`grid ${cols} ${gap}`}>
        {renderTiles()}
      </div>
    );
  };


  const getSummaryData = (): ReportSummary | null => {
    const data = getReportData();
    if (!data) return null;

    // Add debugging for summary data
    console.log(`Getting summary data for report type: ${selectedReport}`);
    console.log('Report data:', data);
    if (data && 'summary' in data) {
      console.log('Summary data:', data.summary);
    } else {
      console.log('No summary property found in data');
    }

    switch (selectedReport) {
      case 'caseList': return (data as CaseListResponse).summary as CaseListSummary;
      case 'caseListClosed': return (data as CaseListResponse).summary as CaseListSummary;
      case 'caseListRejected': return (data as CaseListResponse).summary as CaseListSummary;
      case 'caseListDropped': return (data as CaseListResponse).summary as CaseListSummary;
      case 'caseStatus': return (data as CaseStatusResponse).summary as CaseStatusSummary;
      case 'statistics': return (data as StatisticsResponse).summary as StatisticsSummary;
      case 'checkDeposits': return (data as CheckDepositsResponse).summary as CheckDepositsSummary;
      case 'clients': return (data as ClientsResponse).summary as ClientsSummary;
      case 'clientsOpen': return (data as ClientsResponse).summary as ClientsSummary;
      case 'clientsReferredIn': return (data as ClientsResponse).summary as ClientsSummary;
      case 'clientsSubbedOut': return (data as ClientsResponse).summary as ClientsSummary;
      case 'clientsIntakeReady': return (data as ClientsResponse).summary as ClientsSummary;
      case 'clientsRetained': return (data as ClientsResponse).summary as ClientsSummary;
      case 'defendantTypes': return (data as DefendantTypesResponse).summary as DefendantTypesSummary;
      case 'healthProviders': return (data as HealthProvidersResponse).summary as HealthProvidersSummary;
      case 'insuranceAdjusters': return (data as InsuranceAdjustersResponse).summary as InsuranceAdjustersSummary;
      case 'insuranceCompanies':
        const insuranceCompanySummary = (data as InsuranceCompaniesResponse).summary;
        return convertInsuranceCompanySummary(insuranceCompanySummary);
      case 'lienHolders':
        const lienHolderSummary = (data as LienHoldersResponse).summary;
        return (lienHolderSummary as LienHoldersSummary);
      case 'medpayRequests': return (data as MedpayRequestsResponse).summary as MedpayRequestsSummary;
      case 'medpayDeposits': return (data as MedpayDepositsResponse).summary as MedpayDepositsSummary;
      case 'sourcePerformance': return (data as SourcePerformanceResponse).summary as SourcePerformanceSummary;
      case 'medicalRequests': return (data as MedicalRequestsResponse).summary as MedicalRequestsSummary;
      case 'statute': return (data as StatuteResponse).summary as StatuteSummary;
      case 'demands': return (data as DemandsResponse).summary as DemandsSummary;
      case 'settlement': return (data as SettlementResponse).summary as SettlementSummary;
      case 'settlementManagement': return (data as SettlementManagementResponse).summary as SettlementManagementSummary;
      case 'litigationManagement': return (data as LitigationManagementResponse).summary as LitigationManagementSummary;
      case 'notes': return (data as NotesResponse).summary as NotesSummary;
      case 'clientTrust': return (data as ClientTrustResponse).summary as ClientTrustSummary;
      case 'caseReview': return (data as CaseReviewResponse).summary as CaseReviewSummary;
      case 'clientTrustAttorneyFees': return (data as ClientTrustResponse).summary as ClientTrustSummary;
      case 'clientTrustIssuedPayments': return (data as ClientTrustResponse).summary as ClientTrustSummary;
      case 'clientTrustCheckDeposits': return (data as ClientTrustResponse).summary as ClientTrustSummary;
      case 'clientTrustConsolidated': return (data as ClientTrustConsolidatedResponse).summary as ClientTrustConsolidatedSummary;
      case 'caseCosts': return (data as CaseCostResponse).summary as CaseCostSummary;
      case 'contacts':
        return (data as ContactsResponse).summary as ContactsSummary;
      case 'caseEventsCalendar':
        return (data as CaseEventsCalendarResponse).summary as CaseEventsCalendarSummary;
      case 'estimatedValue': return (data as EstimatedValueResponse).summary as EstimatedValueSummary;
      default:
        return null;
    }
  };

  // Update the getResultsData function to properly handle defendant types data
  const getResultsData = (): TableData<typeof selectedReport> => {
    const data = getReportData();
    if (!data) return [];

    // Handle special cases for responses without results
    if (selectedReport === 'caseStatus' && 'status_details' in data) {
      return data.status_details as unknown as TableData<typeof selectedReport>;
    }

    if (selectedReport === 'statistics') {
      return [] as TableData<typeof selectedReport>;
    }

    // For all other reports that have results property
    if ('results' in data && Array.isArray(data.results)) {
      // First cast the results array to unknown, then to the specific type
      const results = data.results as unknown as TableRowTypeMap[typeof selectedReport][];

      return results.map(row => {
        return row;
      });
    }

    return [];
  };

  // Fix the getTotalPages function to handle the new response structure
  const getTotalPages = () => {
    const data = getReportData();
    if (!data) {
      console.log("No data available for pagination");
      return 1;
    }

    console.log("Calculating total pages from data:", data);

    // Use total_pages if available
    if ('total_pages' in data && typeof data.total_pages === 'number') {
      console.log("Using total_pages from response:", data.total_pages);
      return data.total_pages;
    }

    // Fall back to calculating from count and page_size
    if ('count' in data && typeof data.count === 'number') {
      const totalPages = Math.ceil(data.count / Number(appliedFilters.page_size || 25));
      console.log("Calculated total pages:", totalPages);
      return totalPages;
    }

    console.log("No pagination info found, defaulting to 1 page");
    return 1;
  };
  // Update filter options handling
  useEffect(() => {
    const data = getReportData();
    if (data && 'filter_options' in data) {
      setFilterOptions(data.filter_options as FilterOptions);
    }
  }, [selectedReport]);

  console.log(isExporting, "isExporting");

  // Update filter handling
  const handleApplyFilters = () => {
    const cleanFilters = {
      ...defaultFilters,
      ...Object.entries(filters).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          // Don't include date_range in API request, we use from_date and to_date instead
          if (key !== 'date_range') {
            (acc)[key] = value;
          }
        }
        return acc;
      }, {} as CaseReportFilters)
    };
    setAppliedFilters(cleanFilters);
    setCurrentPage(1);
  };

  // Update handleSearch to properly merge with existing filters
  const handleSearch = () => {
    if (!searchTerm.trim()) {
      const newFilters = { ...appliedFilters };
      delete newFilters.search;
      setAppliedFilters(newFilters);
    } else {
      setAppliedFilters(prev => ({
        ...prev,
        search: searchTerm.trim(),
        page: 1
      }));
    }
    setCurrentPage(1);
  };

  // Update the EnhancedFilters function to not use showFilters
  const EnhancedFilters = ({ show_only_checkbox_filters = false }: { show_only_checkbox_filters?: boolean }) => {
    const data = getReportData();

    const filterOpts = data && 'filter_options' in data ? data.filter_options : null;
    if (!filterOpts) return null;

    // Helper function to render a select filter
    const renderSelectFilter = (
      label: string,
      filterKey: keyof CaseReportFilters,  // This ensures filterKey is valid
      options: (FilterOption | WorkerOption | ValueRangeFilter)[],
      isWorker = false,
    ) => {
      // Check if options are valid with more flexible validation
      const validOptions = options.filter(option =>
        isWorker ?
          ((option as WorkerOption).id !== undefined || (option as FilterOption).value !== undefined) :
          (option as FilterOption).value !== undefined && (option as FilterOption).value !== ''
      );

      if (validOptions.length === 0) return null;
      return (
        <div className="space-y-2">
          <label className="text-sm font-medium">{label}</label>
          <Select
            value={(filters[filterKey] as string) || "_all"}
            onValueChange={(value) => {
              if (value === "_all") {
                const newFilters = { ...filters };
                delete newFilters[filterKey];
                setFilters({ ...newFilters, page: 1 });
              } else {
                setFilters({ ...filters, [filterKey]: value, page: 1 });
              }
            }}
          >
            <SelectTrigger className="bg-white">
              <SelectValue placeholder={`Select ${label}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="_all">All</SelectItem>
              {validOptions.map((option) => {
                const displayValue = isWorker ?
                  ((option as WorkerOption).name || (option as FilterOption).label) :
                  (option as FilterOption).label;

                const actualValue = isWorker ?
                  (((option as WorkerOption).id as string) || (option as FilterOption).value) :
                  (option as FilterOption).value;

                // Format display value - remove underscores and capitalize
                const formattedDisplay = displayValue
                  .split('_')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');

                return (
                  <SelectItem
                    key={actualValue}
                    value={actualValue}
                  >
                    {formattedDisplay}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      );
    };

    // Render date field selector
    const renderDateFieldSelector = (dateFields: FilterOption[] | undefined, defaultValue = "created_at") => {
      if (!dateFields || dateFields.length === 0) return null;

      return (
        <div className="space-y-2">
          <label className="text-sm font-medium">Date Field</label>
          <Select
            value={filters.date_field || defaultValue}
            onValueChange={(value) => {
              setFilters({ ...filters, date_field: value, page: 1 });
            }}
          >
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="Select date field" />
            </SelectTrigger>
            <SelectContent>
              {dateFields.map((field) => (
                <SelectItem key={field.value} value={field.value}>
                  {field.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    };

    // Render date range picker
    const renderDateRangePicker = (useSeparateFields = false) => {
      if (useSeparateFields) {
        return (
          <div className="space-y-2 col-span-2">
            <label className="text-sm font-semibold text-gray-700">Select Dates</label>
            <div className="flex space-x-4">
              <DateSelect
                date={selectedFromDate}
                onSelect={(date) => handleDateSelect(date, 'from')}
                label="From Date"
                placeholder="Select From Date"
                key="from-date-select"
              />
              <DateSelect
                date={selectedToDate}
                onSelect={(date) => handleDateSelect(date, 'to')}
                label="To Date"
                placeholder="Select To Date"
                key="to-date-select"
              />
            </div>
          </div>
        );
      }

      // Regular date range picker with DateRangePicker component
      return (
        <>
          <label className="text-sm font-medium">Date Range</label>
          <DateRangePicker
            dateRange={filters.date_range ? parseDateRange(filters.date_range as string) : undefined}
            setDateRange={(newDateRange: DateRange | undefined) => {
              // Store the date range in filters.date_range for UI
              const newFilters = {
                ...filters,
                date_range: newDateRange ? JSON.stringify(newDateRange) : undefined,
                // Also set from_date and to_date for API
                from_date: newDateRange?.from ? format(newDateRange.from, "yyyy-MM-dd") : undefined,
                to_date: newDateRange?.to ? format(newDateRange.to, "yyyy-MM-dd") : undefined,
                // Ensure date_field is preserved
                date_field: filters.date_field,
                page: 1
              };
              setFilters(newFilters);
              // Don't automatically apply filters - let user click Apply button
            }}
          />
        </>
      );
    };

    // Render checkbox filter
    const renderCheckboxFilter = (label: string, filterKey: keyof CaseReportFilters, id: string) => {
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            id={id}
            checked={filters[filterKey] === "true"}
            onCheckedChange={(checked) => {
              const newFilters = {
                ...filters,
                [filterKey]: checked ? "true" : "false",
                page: 1
              };
              setFilters(newFilters);
              setAppliedFilters(newFilters); // Immediately apply the new filters
            }}
          />
          <label htmlFor={id} className="text-sm font-medium">
            {label}
          </label>
        </div>
      );
    };

    // Build UI components based on available filter options
    const generateFilterComponents = () => {
      const components: JSX.Element[] = [];
      const genericFilterOpts = filterOpts as FilterOptions;


      const dateComponentGroup = [];

      // Date field selector if available
      if (genericFilterOpts.date_fields && genericFilterOpts.date_fields.length > 0 && show_only_checkbox_filters === false) {
        dateComponentGroup.push(renderDateFieldSelector(genericFilterOpts.date_fields,
          selectedReport === 'clients' ? "intake_date" :
            selectedReport === 'defendantTypes' ? "statute" :
              selectedReport === 'litigationManagement' ? "date" : "created_at"
        ));
        dateComponentGroup.push(renderDateRangePicker());
      }


      components.push(
        <div key="date-components" className="space-y-2 col-span-2">
          {dateComponentGroup}
        </div>
      );

      // Map of key patterns to their display names and filter keys
      const filterMappings = [
        // Base filters
        { key: 'case_types', label: 'Case Type', filterKey: 'case_type', isWorker: false },
        { key: 'case_managers', label: 'Case Manager', filterKey: 'case_manager', isWorker: true },
        { key: 'lead_attorneys', label: 'Lead Attorney', filterKey: 'lead_attorney', isWorker: true },

        // Case list specific
        { key: 'supervising_attorneys', label: 'Supervising Attorney', filterKey: 'supervising_attorney', isWorker: true },
        { key: 'primary_workers', label: 'Primary Worker', filterKey:'primary', isWorker: true},
        { key: 'workers', label: 'Worker', filterKey: 'worker', isWorker: true },
        { key: 'teams', label: 'Team', filterKey: 'team', isWorker: true },
        { key: 'source_types', label: 'Source Type', filterKey: 'source_type', isWorker: false },
        { key: 'source_details', label: 'Source Detail', filterKey: 'source_detail', isWorker: false },
        { key: 'case_ages', label: 'Case Age', filterKey: 'case_age', isWorker: false },
        { key: 'open_cases_checkbox', label: 'Open Cases', filterKey: 'open_cases', isWorker: false, is_checkbox: true },
        // Check deposits specific
        { key: 'check_types', label: 'Check Type', filterKey: 'check_type', isWorker: false },

        // Clients specific
        { key: 'organization_statuses', label: 'Organization Case Status', filterKey: 'organization_status', isWorker: true },
        { key: "open/closed", label: "Open/Closed", filterKey: "case_kpi_status", isWorker: false },
        { key: 'languages', label: 'Language', filterKey: 'language', isWorker: false },
        { key: 'birthday_months', label: 'Birthday Month', filterKey: 'birthday_month', isWorker: false },
        { key: 'minor_client', label: 'Minor Client', filterKey: 'minor_client', isWorker: false},
        { key: 'has_email', label: 'Has Email', filterKey: 'has_email', isWorker: false},
        
        // Defendant types specific
        { key: 'defendant_types', label: 'Defendant Type', filterKey: 'defendant_type', isWorker: false },

        // Health providers specific
        { key: 'specialties', label: 'Specialty', filterKey: 'specialty', isWorker: false },
        { key: 'cities', label: 'City', filterKey: 'city', isWorker: false },

        // Insurance adjusters specific
        { key: 'insurance_companies', label: 'Insurance Company', filterKey: 'insurance_company', isWorker: true },

        // Insurance companies specific
        { key: 'insurance_types', label: 'Insurance Type', filterKey: 'insurance_type', isWorker: false },
        { key: 'company_types', label: 'Company Type', filterKey: 'company_type', isWorker: false },

        // Lien holders specific
        { key: 'lien_status', label: 'Lien Status', filterKey: 'lien_status', isWorker: false },

        // Notes specific
        { key: 'tags', label: 'Tags', filterKey: 'tags', isWorker: false },

        // Litigation management specific
        { key: 'assigned_workers', label: 'Assigned Worker', filterKey: 'assigned_worker', isWorker: true },
        { key: 'event_types', label: 'Event Type', filterKey: 'event_type', isWorker: false },
        { key: 'status_options', label: 'Status', filterKey: 'status', isWorker: false },

        // Role options for any report type
        { key: 'role_options', label: 'Role', filterKey: 'role', isWorker: true },
        // Case review specific
        { key: 'case_grades', label: 'Case Grade', filterKey: 'case_grade', isWorker: false },

        //Client trust specific deposit_types
        { key: 'deposit_types', label: 'Deposit Type', filterKey: 'is_deposit', isWorker: false, is_checkbox: true },
        { key: 'client_trust_entry_types', label: 'Client Trust Type', filterKey: 'client_trust_entry_type', isWorker: false },
        { key: 'show_attorney_fees', label: 'Show Attorney Fees', filterKey: 'show_attorney_fees', isWorker: false, is_checkbox: true },

        // Case costs specific
        { key: 'cost_status', label: 'Cost Status', filterKey: 'cost_status', isWorker: false },
        { key: 'payment_type_options', label: 'Payment Type', filterKey: 'payment_type', isWorker: false },
        { key: 'priority_options', label: 'Priority', filterKey: 'priority', isWorker: false },
        { key: 'section_options', label: 'Section', filterKey: 'section', isWorker: false },
        { key: "contact_types", label: "Contact Type", filterKey: "contact_type", isWorker: false },
        { 
          key: 'calculated_estimated_value_range', 
          label: 'Calculated Estimated Value Range', 
          filterKey: 'calculated_estimated_value_range', 
          isWorker: false,
          isRange: true,
          rangePrefix: 'calculated_value',
          prefixSign: '$'
        },
        { 
          key: 'manual_estimated_value_range', 
          label: 'Manual Estimated Value Range', 
          filterKey: 'manual_estimated_value_range', 
          isWorker: false,
          isRange: true,
          rangePrefix: 'estimated_value'
        },
        {
          key:'statue_of_limitation_incoming_end_in_days',
          label: 'Statue of Limitation Incoming End In Days',
          filterKey: 'statue_of_limitation_incoming_end_in_days',
          isWorker: false,
          isRange: true,
          rangePrefix: 'statue_of_limitation_incoming_end_in_days',
          prefixSign: 'Day '
        }

      ];

      if (show_only_checkbox_filters) {
        const componentsCheckbox: JSX.Element[] = [];
        for (const mapping of filterMappings) {
          const optionsArray = genericFilterOpts[mapping.key as keyof FilterOptions];
          if (optionsArray && optionsArray.length > 0 && mapping.is_checkbox) {
            componentsCheckbox.push(
              <div key={mapping.key}>
                {renderCheckboxFilter(mapping.label, mapping.filterKey as keyof CaseReportFilters, mapping.key)}
              </div>
            );
          }
        }
        return componentsCheckbox;
      }

      // Add select filters based on available filter options
      for (const mapping of filterMappings) {
        if (mapping.is_checkbox) {
          continue;
        }

        if (mapping.isRange) {
          const rangeData = genericFilterOpts[mapping.key as keyof FilterOptions];
          if (rangeData) {
            components.push(
              <div key={mapping.key} className="col-span-2 md:col-span-1 p-2 border rounded-md shadow-sm bg-white w-full lg:w-[400px]">
                {RangeSliderFilter({label: mapping.label, filterKeyPrefix: mapping.rangePrefix as string, rangeData: rangeData as ValueRangeFilter[], filters, setFilters, prefixSign: mapping.prefixSign || ''})}
              </div>
            );
          }
          continue;
        }

        const optionsArray = genericFilterOpts[mapping.key as keyof FilterOptions];
        if (optionsArray && optionsArray.length > 0) {
          components.push(
            <div key={mapping.key}>
              {renderSelectFilter(mapping.label, mapping.filterKey as keyof CaseReportFilters, optionsArray, mapping.isWorker)}
            </div>
          );
        }
      }

      return components;
    };

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-2">
        {generateFilterComponents()}
      </div>
    );
  };

  // Fix the reset filters function
  const handleResetFilters = () => {
    const resetFilters: CaseReportFilters = {
      page: 1,
      page_size: 25,
      view: "all_cases",
      date_field: "created_at", // Set a default date field
    };

    setFilters(resetFilters);
    setSearchTerm("");
    setSelectedFromDate(undefined);
    setSelectedToDate(undefined);
  };

  // Fix the useEffect for selectedReport changes
  useEffect(() => {
    if (!selectedReport) return;

    // Determine the appropriate default date field based on report type
    const defaultDateField =
      selectedReport === 'clients' ? "intake_date" :
        selectedReport === 'defendantTypes' ? "statute" :
          selectedReport === 'litigationManagement' ? "date" :
            "created_at";

    // Reset to default filters when report type changes
    const defaultFilters: CaseReportFilters = {
      page: 1,
      page_size: 25,
      view: "all_cases",
      date_field: defaultDateField,
    };

    setFilters(defaultFilters);
    setAppliedFilters(defaultFilters); // Also update applied filters
    setCurrentPage(1);
    setSearchTerm("");
  }, [selectedReport]);

  // Add the handlePageChange function
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Update the applied filters with the new page number
    setAppliedFilters(prev => ({ ...prev, page }));
  };



  // Update the handleExportAll function with the correct Accept header
  const handleExportAll = async () => {
    if (!selectedReport) return;

    const baseUrl = process.env.NEXT_PUBLIC_DJANGO_URL_V2;
    const endpoint = getReportEndpoint(selectedReport);

    const queryParams = new URLSearchParams();
    Object.entries(appliedFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.set(key, String(value));
      }
    });

    queryParams.set('export', 'csv');

    let exportUrl = '';


    if(["settlementManagement", "litigationManagement"].includes(selectedReport)) {
       exportUrl = `${baseUrl}/case-management/reports/management-reports/${endpoint}/?${queryParams.toString()}`;
    } else {
       exportUrl = `${baseUrl}/case-management/reports/${endpoint}/?${queryParams.toString()}`;
    }

    try {
      setIsExporting(true);
      console.log('Starting export with axios...');

      // Use axios to make the request with the exact headers from the working curl command
      const response = await axios.get(exportUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json, text/plain, */*',
          'Origin': window.location.origin,
          'Referer': window.location.href,
        },
        responseType: 'blob',
      });

      console.log('Response received:', response.status);
      console.log('Content type:', response.headers['content-type']);

      // Create a blob with the correct content type
      const contentType = response.headers['content-type'] || 'text/csv';
      const blob = new Blob([response.data], { type: contentType });

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a link element to trigger the download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${selectedReport}_all_data.csv`;
      document.body.appendChild(a);

      // Trigger the download
      a.click();

      // Clean up
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        console.log('Download complete');
      }, 100);

    } catch (error) {
      console.error('Export error:', error);
      // Log more details about the error
      // alert(`Failed to export data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExporting(false);
    }
  };

  // Update the date handling in filters
  const handleDateSelect = (date: Date | undefined, type: 'from' | 'to') => {
    if (type === 'from') {
      setSelectedFromDate(date);
      setFilters(prev => ({
        ...prev,
        from_date: date ? format(date, "yyyy-MM-dd") : undefined,
        // Ensure date_field is preserved
        date_field: prev.date_field,
        page: 1
      }));
    } else {
      setSelectedToDate(date);
      setFilters(prev => ({
        ...prev,
        to_date: date ? format(date, "yyyy-MM-dd") : undefined,
        // Ensure date_field is preserved
        date_field: prev.date_field,
        page: 1
      }));
    }
  };

  // Handle tile click for settlement/litigation management
  const handleTileClick = (filterValue: string) => {
    // Check if the tile is already active
    const isCurrentlyActive = filters.tile === filterValue;

    // Update filters and apply immediately
    const newFilters = {
      ...filters,
      // If the tile is already active, clear the filter, otherwise set it
      tile: isCurrentlyActive ? undefined : filterValue,
      page: 1
    };

    setFilters(newFilters);
    setAppliedFilters(newFilters);
  };

  // Update URL when report type changes
  useEffect(() => {
    if (!selectedReport || !searchParams) return;

    // Create new URL params that preserve existing ones
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set("report", selectedReport);

    // Update the URL without refreshing the page
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });

  }, [selectedReport, router, searchParams]);

  // Update the select change handler to update the selectedReport state
  const handleReportTypeChange = (value: string) => {
    setSelectedReport(value as ReportType);
  };

  const summaryData = getSummaryData();
  
  // State to control sidebar visibility
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [metricsOpen, setMetricsOpen] = useState(true);

  return (
    <div className="px-2 pt-3.5 w-[100%] relative flex">
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'mr-80' : 'mr-0'}`}>
        <div className="mb-4 flex justify-between items-center">
          <h1 className="text-3xl font-bold">Report {selectedReport ? reportOptions.find(option => option.value === selectedReport)?.label : ''}</h1>
          
          <div className="flex items-center gap-4">
            {/* Search component */}
            <div className="relative w-[300px]">
              <Input
                type="text"
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
                className="pr-20"
              />
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={handleSearch}
                className="absolute right-0 top-0 h-full px-3"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </Button>
            </div>

            <div className="flex items-center gap-2">
              {/* Show Matrix button */}
              <div className="flex items-center">
                <Button 
                  variant="outline" 
                  onClick={() => setMetricsOpen(!metricsOpen)}
                  className="flex items-center gap-1"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3 3v18h18"></path>
                    <path d="M18 17V9"></path>
                    <path d="M13 17v-5"></path>
                    <path d="M8 17v-3"></path>
                  </svg>
                  {metricsOpen ? 'Hide Matrix' : 'Show Matrix'}
                </Button>
              </div>

              {/* Filter toggle button */}
              <Button 
                variant="outline" 
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex items-center gap-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"></path>
                </svg>
                {sidebarOpen ? 'Hide Filters' : 'Show Filters'}
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 mb-4">
          {/* Summary Metrics Section */}
          {metricsOpen && (
            <div className="mt-6">
              <Collapsible defaultOpen={true}>
                <CollapsibleContent>
                  {isError() ? (
                    <div className="bg-red-50 text-red-700 p-4 rounded-md border border-red-200 mb-4">
                      <div className="flex items-start gap-3">
                        <div className="p-1 bg-red-100 rounded-full mt-0.5">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium">Error loading report data</h3>
                          <p className="text-sm mt-1">There was a problem fetching the report data. Please try again or contact support if the issue persists.</p>
                        </div>
                      </div>
                    </div>
                  ) : null}

                  {(() => {
                    const summaryData = getSummaryData();
                    return summaryData ? (
                      <div className="bg-white rounded-lg border border-gray-200 shadow-sm mb-4 overflow-hidden">
                        <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                          <h2 className="text-lg font-semibold flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-primary">
                              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                              <polyline points="3.29 7 12 12 20.71 7"></polyline>
                              <line x1="12" y1="22" x2="12" y2="12"></line>
                            </svg>
                            Summary Metrics
                          </h2>
                          <div className="text-sm text-gray-500">
                            {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                          </div>
                        </div>
                        <div className="p-3 sm:p-4">
                          <SummaryTiles data={summaryData} reportType={selectedReport} onTileClick={handleTileClick} />
                        </div>
                      </div>
                    ) : null;
                  })()}
                </CollapsibleContent>
              </Collapsible>
            </div>
          )}

          {/* Table Section */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 h-full">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Results</h2>
              <Button variant="outline" onClick={handleExportAll} disabled={isExporting}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Export All
                {isExporting && <span className="ml-2 animate-spin">⏳</span>}
              </Button>
            </div>

            {isLoading() ? (
              <div className="flex justify-center p-8">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-4"></div>
                  <p className="text-muted-foreground">Loading report data...</p>
                </div>
              </div>
            ) : (
              <>
                <DynamicTable data={getResultsData()} reportType={selectedReport} isLoading={false} />
                {getTotalPages() > 1 && (
                  <div>
                    <Pagination
                      currentPage={currentPage}
                      totalPages={getTotalPages()}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Right Sidebar for Filters */}
      <div className={`fixed top-[64px] bottom-0 right-0 w-80 bg-white border-l border-gray-200 shadow-lg overflow-y-auto transform transition-transform duration-300 ease-in-out ${sidebarOpen ? 'translate-x-0' : 'translate-x-full'} z-10`}>
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"></path>
              </svg>
              Report Controls
            </h2>
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)} className="h-8 w-8 p-0">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              <span className="sr-only">Close</span>
            </Button>
          </div>

          <div className="space-y-6">
            {/* Report Options */}
            <div>
              <h3 className="text-md font-medium mb-3">Report Type</h3>
              <Select
                value={selectedReport || ''}
                onValueChange={handleReportTypeChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  {reportOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Case View Controls */}
            <div className="border-t border-gray-100 pt-4">
              <h3 className="text-md font-medium mb-3">Case View</h3>
              <div className="flex gap-2">
                <Button
                  variant={filters.view === "all_cases" ? "default" : "outline"}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const newFilters = {
                      ...filters,
                      view: "all_cases" as "all_cases" | "my_cases" | undefined,
                      page: 1
                    };
                    setFilters(newFilters);
                    setAppliedFilters(newFilters);
                  }}
                >
                  All Cases
                </Button>
                <Button
                  variant={filters.view === "my_cases" ? "default" : "outline"}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const newFilters = {
                      ...filters,
                      view: "my_cases" as "all_cases" | "my_cases" | undefined,
                      page: 1
                    };
                    setFilters(newFilters);
                    setAppliedFilters(newFilters);
                  }}
                >
                  My Cases
                </Button>
              </div>
            </div>

            {/* Quick Filters */}
            <div className="border-t border-gray-100 pt-4">
              <h3 className="text-md font-medium mb-3">Quick Filters</h3>
              {/* <EnhancedFilters show_only_checkbox_filters={true} /> */}
            </div>

            {/* Advanced Filters */}
            <div className="border-t border-gray-100 pt-4">
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-1 w-full justify-between">
                    <div className="flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"></path>
                      </svg>
                      Advanced Filters
                    </div>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-4">
                  <EnhancedFilters />
                </CollapsibleContent>
              </Collapsible>
            </div>

            {/* Filter Actions */}
            <div className="flex flex-col gap-2 sticky bottom-0 pt-4 pb-2 bg-white border-t border-gray-100 mt-4">
              <Button onClick={handleApplyFilters} className="w-full flex items-center justify-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                Apply Filters
              </Button>
              <Button variant="outline" onClick={handleResetFilters} className="w-full flex items-center justify-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0z"></path>
                  <path d="m9 9 6 6"></path>
                  <path d="m15 9-6 6"></path>
                </svg>
                Reset Filters
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}