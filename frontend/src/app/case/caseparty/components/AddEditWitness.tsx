"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useExpertWitnessesQuery } from "@/services/case-management/expertWitnessService";
import { ExpertWitnessContact } from "@/type/case-management/orgTypes";
import {
  useCreateCaseExpertWitnessMutation,
  useUpdateCaseExpertWitnessMutation,
} from "@/services/case-management/caseExpertWitnessService";
import { CaseExpertWitness } from "@/type/case-management/partyTypes";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import RichTextEditor from '@/components/ui/RichTextEditor'
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { formatDateForApi, formatDateForInput } from "@/utils/dateUtils";
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";
import { useExpertWitnessSyncMutation } from "@/services/case-management/caseSyncService";
import { toast } from "@/hooks/use-toast";

const witnessFormSchema = z.object({
  expertWitness: z.string().min(1, "Expert witness is required"),
  type: z.string().optional(),
  description: z.string().optional(),
  retained_date: z.string().optional(),
  record_received_date: z.string().optional(),
});

export type ExpertWitnessData = z.infer<typeof witnessFormSchema>;

interface AddEditWitnessProps {
  trigger: React.ReactNode;
  selectedWitness?: ExpertWitnessContact;
  caseId: string;
  isEdit?: boolean;
  expertWitnessId?: number;
  selectedCaseWitness?: CaseExpertWitness;
}

// const formatSpecialties = (specialty: string) => {
//   return specialty
//     .split('_')
//     .map(word => word.charAt(0).toUpperCase() + word.slice(1))
//     .join(' ');
// };

const AddEditWitness = ({
  trigger,
  selectedWitness,
  selectedCaseWitness,
  isEdit,
  caseId,
}: AddEditWitnessProps) => {
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Fetch expert witnesses from API
  const { data: expertWitnesses, isLoading } = useExpertWitnessesQuery();

  const form = useForm<ExpertWitnessData>({
    resolver: zodResolver(witnessFormSchema),
    defaultValues: {
      expertWitness: selectedWitness?.id?.toString() || "",
      type: selectedCaseWitness?.type === "Defense" ? "DEFENSE" :
        selectedCaseWitness?.type === "Plaintiff" ? "PLAINTIFF" : "",
      description: selectedCaseWitness?.description || "",
      retained_date: selectedCaseWitness?.retained_date ? formatDateForApi(selectedCaseWitness.retained_date) : "",
      record_received_date: selectedCaseWitness?.record_received_date ? formatDateForApi(selectedCaseWitness.record_received_date) : "",
    },
  });

  // Update form values when selectedCaseWitness changes
  useEffect(() => {
    if (isEdit && selectedCaseWitness) {
      form.setValue("type", selectedCaseWitness.type === "Defense" ? "DEFENSE" : "PLAINTIFF");
      form.setValue("description", selectedCaseWitness.description || "");
    }
  }, [selectedCaseWitness, isEdit, form]);

  // Add mutation hooks
  const createMutation = useCreateCaseExpertWitnessMutation(caseId);
  const updateMutation = useUpdateCaseExpertWitnessMutation(
    caseId,
    selectedCaseWitness?.id?.toString() || ""
  );
  const witnessSync = useExpertWitnessSyncMutation(caseId);

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(newOpen);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset();
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleSync = async (selectedCaseIds: string[]) => {
    try {
      setIsSyncing(true);

      const values = form.getValues();
      const witnessData = {
        expert_witness: parseInt(values.expertWitness),
        type: (values.type === "DEFENSE" ? "Defense" : "Plaintiff") as "Defense" | "Plaintiff",
        description: values.description || "",
        retained_date: values.retained_date ? formatDateForApi(values.retained_date) : undefined,
        record_received_date: values.record_received_date ? formatDateForApi(values.record_received_date) : undefined,
      };

      let updatedWitness;
      if (isEdit && selectedCaseWitness) {
        updatedWitness = await updateMutation.mutateAsync(witnessData);
      } else {
        updatedWitness = await createMutation.mutateAsync(witnessData);
      }

      await witnessSync.mutateAsync({
        expert_witness_id: updatedWitness.id,
        target_case_ids: selectedCaseIds
      });

      setIsSyncDialogOpen(false);
      setOpen(false);
      form.reset(form.getValues());

    } catch (error) {
      console.error("Failed to sync expert witness:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to sync expert witness details",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  async function onSubmit(values: ExpertWitnessData) {
    try {
      const mutationData = {
        expert_witness: parseInt(values.expertWitness),
        type: (values.type === "DEFENSE" ? "Defense" : "Plaintiff") as
          | "Defense"
          | "Plaintiff",
        description: values.description || "",
        retained_date: values.retained_date ? formatDateForApi(values.retained_date) : undefined,
        record_received_date: values.record_received_date ? formatDateForApi(values.record_received_date) : undefined,
      };

      if (isEdit && selectedWitness) {
        await updateMutation.mutateAsync(mutationData);
      } else {
        await createMutation.mutateAsync(mutationData);
      }

      form.reset();
      setOpen(false);
    } catch (error) {
      console.error("Failed to save witness:", error);
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{trigger}</DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {isEdit ? "Edit Witness" : "Add Witness"}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                {/* Expert Witness Select Field */}
                <FormField
                  control={form.control}
                  name="expertWitness"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-600 font-normal">
                        Expert Witness
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || ""}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select expert witness" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {field.value && (
                            <Button
                              variant="ghost"
                              className="mb-2 w-full justify-center text-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                field.onChange("");
                              }}
                            >
                              Clear Selection
                            </Button>
                          )}
                          {expertWitnesses?.map((witness) => (
                            <SelectItem key={witness.id} value={witness.id.toString()}>
                              {witness.first_name} {witness.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                {/* Type Field */}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-600 font-normal">
                        Type
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {field.value && (
                            <Button
                              variant="ghost"
                              className="mb-2 w-full justify-center text-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                field.onChange("");
                              }}
                            >
                              Clear Selection
                            </Button>
                          )}
                          <SelectItem value="DEFENSE">Defense</SelectItem>
                          <SelectItem value="PLAINTIFF">Plaintiff</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>

              {/* Date Fields */}
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="retained_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-600 font-normal">Retained Date</FormLabel>
                      <FormControl>
                        <CustomDateInput
                          value={field.value ? formatDateForInput(field.value) : ""}
                          onChange={field.onChange}
                          error={!!form.formState.errors.retained_date}
                          className="bg-white"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="record_received_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-600 font-normal">Record Received Date</FormLabel>
                      <FormControl>
                        <CustomDateInput
                          value={field.value ? formatDateForInput(field.value) : ""}
                          onChange={field.onChange}
                          error={!!form.formState.errors.record_received_date}
                          className="bg-white"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Description Field */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-600 font-normal">
                      Description
                    </FormLabel>
                    <FormControl>
                      <div className="bg-white border rounded-md">
                        <RichTextEditor
                          value={field.value || ""}
                          onChange={(value) => field.onChange(value)}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Form Actions */}
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    const isDirty = form.formState.isDirty;
                    if (isDirty) {
                      setShowUnsavedAlert(true);
                    } else {
                      handleClose();
                    }
                  }}
                >
                  CANCEL
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsSyncDialogOpen(true)}
                  disabled={isSyncing}
                >
                  Sync Linked Cases
                </Button>
                <Button
                  type="button"
                  onClick={(e) => {
                    form.handleSubmit((values) => onSubmit(values))(e);
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  SAVE
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />

      <SyncCaseDialog
        isOpen={isSyncDialogOpen}
        onClose={() => setIsSyncDialogOpen(false)}
        onSync={handleSync}
        isSyncing={isSyncing}
        caseId={caseId}
        syncType="expert-witnesses"
      />
    </>
  );
};

export default AddEditWitness;
