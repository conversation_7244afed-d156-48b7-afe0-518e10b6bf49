"use client";

import React, { Fragment, useState } from 'react';
import { useCaseExpertWitnessesQuery, useDeleteCaseExpertWitnessMutation } from "@/services/case-management/partyService";
import { Edit, Trash2, User, Repeat } from 'lucide-react';
import { AddressLink } from "@/components/gMap/address-link";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { Separator } from "@/components/ui/separator";
import { CaseExpertWitness } from '@/type/case-management/partyTypes';
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { Button } from '@/components/ui/button';
import AddEditWitness from './AddEditWitness';
import { useExpertWitnessSyncMutation } from '@/services/case-management/caseSyncService';
import SyncCaseDialog from '@/components/CaseOverview/components/SyncCaseDialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";

interface ExpertWitnessListProps {
    caseId: string;
    handleExpertWitnessSyncComplete: () => void;
}

export default function ExpertWitnessList({ caseId, handleExpertWitnessSyncComplete }: ExpertWitnessListProps) {
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [selectedWitness, setSelectedWitness] = useState<CaseExpertWitness | null>(null);
    const [showSyncDialog, setShowSyncDialog] = useState(false);
    const [selectedWitnessForSync, setSelectedWitnessForSync] = useState<number | null>(null);
    const { data: expertWitnesses = [], isLoading } = useCaseExpertWitnessesQuery(caseId);
    const deleteWitnessMutation = useDeleteCaseExpertWitnessMutation(caseId, selectedWitness?.id || 0);
    const syncExpertWitness = useExpertWitnessSyncMutation(caseId);

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            if (!selectedCaseIds.length) {
                toast({
                    title: "Error",
                    description: "No cases selected for syncing",
                    variant: "destructive",
                });
                return;
            }

            if (!selectedWitnessForSync) {
                toast({
                    title: "Error",
                    description: "No expert witness selected for syncing",
                    variant: "destructive",
                });
                return;
            }

            await syncExpertWitness.mutateAsync({
                expert_witness_id: selectedWitnessForSync,
                target_case_ids: selectedCaseIds
            });

            setShowSyncDialog(false);
            handleExpertWitnessSyncComplete();
            toast({
                title: "Success",
                description: "Expert witness data synchronized successfully",
            });
        } catch (error) {
            console.error("Error syncing expert witness:", error);
            toast({
                title: "Error",
                description: "Failed to sync expert witness data",
                variant: "destructive",
            });
        }
    };

    if (isLoading) {
        return <div className="text-center py-8">Loading expert witnesses...</div>;
    }

    if (expertWitnesses.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500">
                No expert witnesses found.
            </div>
        );
    }
    const handleDeleteWitness = (witness: CaseExpertWitness) => {
        setSelectedWitness(witness);
        setDeleteDialogOpen(true);
    };

    const handleConfirmDelete = async () => {
        if (selectedWitness) {
            try {
                await deleteWitnessMutation.mutateAsync();
                setDeleteDialogOpen(false);
                setSelectedWitness(null);
            } catch (error) {
                console.error('Error deleting expert witness:', error);
            }
        }
    };

    return (
        <div className="space-y-6">
            {expertWitnesses.map((witness, index) => {
                const addressProps = {
                    street1: witness.expert_witness.street1,
                    street2: witness.expert_witness.street2,
                    city: witness.expert_witness.city,
                    state: witness.expert_witness.state,
                    zip_code: witness.expert_witness.zip_code
                };

                const fullAddress = [
                    addressProps.street1,
                    addressProps.street2,
                    addressProps.city,
                    addressProps.state,
                    addressProps.zip_code
                ].filter(Boolean).join(', ');

                return (
                    <Fragment key={witness.id}>
                        {index > 0 && (
                            <Separator className="my-6 bg-gray-200" />
                        )}
                        <div className="bg-white">
                            <div className="p-4">
                                {/* Header */}
                                <div className="flex items-center justify-between mb-5">
                                    <div className="flex items-center gap-1.5">
                                        <User className="w-4 h-4" />
                                        <h2 className="text-[14px] font-medium leading-5">Expert Witness - {index + 1}</h2>
                                    </div>
                                    <div className="flex items-center gap-1.5">
                                        {/* <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <span className={`${syncExpertWitness.isPending ? 'cursor-not-allowed' : ''}`}>
                                                        <Button
                                                            variant="outline"
                                                            className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
                                                            onClick={() => {
                                                                setSelectedWitnessForSync(witness.expert_witness.id);
                                                                setShowSyncDialog(true);
                                                            }}
                                                            disabled={syncExpertWitness.isPending}
                                                        >
                                                            {syncExpertWitness.isPending ? (
                                                                <>
                                                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <Repeat className="h-4 w-4 mr-2" />
                                                                </>
                                                            )}
                                                        </Button>
                                                    </span>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    Sync Expert Witness Data
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider> */}
                                        <AddEditWitness
                                            trigger={
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="text-green-600 hover:text-green-700 hover:bg-green-50 p-2 h-8 w-8"
                                                >
                                                    <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                                                </Button>
                                            }
                                            selectedWitness={witness.expert_witness}
                                            selectedCaseWitness={witness}
                                            caseId={caseId}
                                            isEdit={true}
                                            expertWitnessId={witness.id}
                                        />
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2 h-8 w-8"
                                            onClick={() => handleDeleteWitness(witness)}
                                        >
                                            <Trash2 className="h-4 w-4 text-red-600 cursor-pointer" />
                                        </Button>
                                        <ManageEmailTemplate
                                            caseId={caseId}
                                            contextType={"expert_witness" as TemplateContextType}
                                            expert_witness_id={witness.id.toString()}
                                        />
                                    </div>
                                </div>

                                {/* Basic Information */}
                                <InfoFieldGroup
                                    caseId={caseId}
                                    fields={[
                                        {
                                            label: "Name",
                                            value: `${witness.expert_witness.first_name} ${witness.expert_witness.last_name}`
                                        },
                                        {
                                            label: "Type",
                                            value: witness.type || 'Expert'
                                        },
                                        {
                                            label: "Specialties",
                                            value: witness.expert_witness.specialties || 'Biomechanics engineer'
                                        },
                                        {
                                            label: "Primary",
                                            value: witness.expert_witness.phone || '',
                                            isPhone: true
                                        },
                                        {
                                            label: "Secondary Phone",
                                            value: witness.expert_witness.cell || '',
                                            isPhone: true
                                        },
                                        {
                                            label: "Email",
                                            value: witness.expert_witness.email || '',
                                            isMail: true
                                        },
                                        {
                                            label: "Retained Date",
                                            value: witness.retained_date || '—',
                                            isDate: true
                                        },
                                        {
                                            label: "Record Received Date",
                                            value: witness.record_received_date || '—',
                                            isDate: true
                                        },
                                        {
                                            label: "Address",
                                            value: (
                                                <AddressLink
                                                    address={addressProps}
                                                    className="text-[#060216] text-sm leading-5 font-semibold"
                                                >
                                                    {fullAddress || '—'}
                                                </AddressLink>
                                            )
                                        }
                                    ]}
                                />

                                {/* Notes */}
                                <div className="bg-[#F7F7F7] border border-black/[0.08] rounded-lg p-4">
                                    <div className="space-y-0.5">
                                        <p className="text-[#060216] opacity-50 text-sm font-medium">Notes</p>
                                        {witness.description ? (
                                            <RichTextViewer
                                                data={witness.description}
                                                className="text-[#060216] text-sm leading-5 font-semibold"
                                            />
                                        ) : (
                                            <p className="text-[#060216] text-sm leading-5 font-semibold">—</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Fragment>
                );
            })}

            <DeleteConfirmationDialog
                open={deleteDialogOpen}
                onOpenChange={setDeleteDialogOpen}
                onConfirm={handleConfirmDelete}
                title="Delete Expert Witness"
                description={`Are you sure you want to delete ${selectedWitness?.expert_witness.first_name} ${selectedWitness?.expert_witness.last_name}? This action cannot be undone.`}
            />

            <SyncCaseDialog
                caseId={caseId}
                syncType="expert-witnesses"
                isOpen={showSyncDialog}
                onClose={() => setShowSyncDialog(false)}
                onSync={handleSync}
                isSyncing={syncExpertWitness.isPending}
            />
        </div>
    );
} 