'use client';

import React, { useState, useEffect, FC, ReactNode } from 'react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { PARTY_STATUS_CHOICES, PartyType, PartyStatus, CasePartyContact } from "@/type/case-management/partyTypes";
import { LawFirmResponse, AttorneyResponse } from '@/type/case-management/orgTypes';
import AddEditPartyContact from "./AddEditPartyContact";
import { useCasePartyContactsQuery, useCreateCasePartyMutation, useUpdateCasePartyMutation, CASE_PARTY_CONTACTS_KEY } from "@/services/case-management/partyService";
import { useAttorneysQuery, useLawFirmsQuery } from "@/services/orgAPIs";
import { AddEditLawFirm } from "@/app/client-details/components/AddEditLawFirm";
import { AddEditAttorneyContact } from "@/app/case/employment/components/AddEditAttorneyContact";
import { useQueryClient } from '@tanstack/react-query';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import RichTextEditor from '@/components/ui/RichTextEditor'
import { RequiredLabel } from "@/components/ui/required-label";
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";
import { usePartySyncMutation } from "@/services/case-management/caseSyncService";
import { toast } from "@/hooks/use-toast";

const formSchema = z.object({
    status: z.nativeEnum(PartyStatus),
    party_type: z.nativeEnum(PartyType).optional(),
    party_contact: z.string(),
    law_firm: z.string().optional(),
    attorney: z.string().optional(),
    description: z.string().optional().nullable(),
});

interface AddEditCasePartyProps {
    isEdit?: boolean;
    caseId: string;
    trigger?: ReactNode;
    defaultValues?: z.infer<typeof formSchema>;
    partyId?: string;
}

type ApiError = {
    [key: string]: string[];
};

type FormFields = keyof z.infer<typeof formSchema>;

interface ApiErrorResponse {
    response?: {
        data?: {
            [key: string]: string[];
        };
    };
    message: string;
}

const AddEditCaseParty: FC<AddEditCasePartyProps> = ({
    isEdit = false,
    caseId,
    trigger,
    defaultValues,
    partyId
}) => {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [selectedPartyType, setSelectedPartyType] = useState<PartyType>(defaultValues?.party_type || PartyType.PLAINTIFF);

    const { data: partyContacts } = useCasePartyContactsQuery(caseId, selectedPartyType);
    const { data: lawFirms } = useLawFirmsQuery({ enabled: open });
    const { data: attorneys } = useAttorneysQuery({ enabled: open });

    const [selectedContact, setSelectedContact] = useState<number | undefined>(
        defaultValues?.party_contact ? parseInt(defaultValues.party_contact, 10) : undefined
    );

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: defaultValues || {
            status: PartyStatus.PENDING_CONTACT,
            party_type: PartyType.PLAINTIFF,
            party_contact: "",
            law_firm: "",
            attorney: "",
            description: "",
        },
    });

    useEffect(() => {
        if (open) {
            if (isEdit && defaultValues) {
                // Only reset form if values have changed
                const currentValues = form.getValues();
                const hasChanged =
                    currentValues.status !== defaultValues.status ||
                    currentValues.party_type !== defaultValues.party_type ||
                    currentValues.party_contact !== defaultValues.party_contact ||
                    currentValues.law_firm !== defaultValues.law_firm ||
                    currentValues.attorney !== defaultValues.attorney ||
                    currentValues.description !== defaultValues.description;

                if (hasChanged) {
                    // Reset form with default values
                    form.reset({
                        status: defaultValues.status,
                        party_type: defaultValues.party_type,
                        party_contact: defaultValues.party_contact || "",
                        law_firm: defaultValues.law_firm || "",
                        attorney: defaultValues.attorney || "",
                        description: defaultValues.description || "",
                    });

                    // Reset selected states
                    setSelectedContact(
                        defaultValues.party_contact ? parseInt(defaultValues.party_contact, 10) : undefined
                    );
                    setSelectedPartyType(defaultValues.party_type || PartyType.PLAINTIFF);
                }
            } else {
                // Reset form for new party
                form.reset({
                    status: PartyStatus.PENDING_CONTACT,
                    party_type: selectedPartyType,
                    party_contact: "",
                    law_firm: "",
                    attorney: "",
                    description: "",
                });

                // Reset selected states
                setSelectedContact(undefined);
            }
        }
    }, [open, isEdit, defaultValues, form, selectedPartyType]);

    const createPartyMutation = useCreateCasePartyMutation(caseId);
    const updatePartyMutation = useUpdateCasePartyMutation(
        caseId,
        partyId ? parseInt(partyId, 10) : 0
    );
    const partySync = usePartySyncMutation(caseId);

    const [mutationErrors, setMutationErrors] = useState<ApiError>({});

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            setIsSubmitting(true);
            // Reset any previous mutation errors
            setMutationErrors({});

            const isValid = await form.trigger();
            if (!isValid) {
                return;
            }

            const partyData = {
                status: values.status,
                description: values.description || "",
                contact: parseInt(values.party_contact, 10),
                law_firm: values.law_firm ? parseInt(values.law_firm, 10) : undefined,
                attorney: values.attorney ? parseInt(values.attorney, 10) : undefined,
            };

            if (isEdit && partyId) {
                await updatePartyMutation.mutateAsync(partyData);
            } else {
                await createPartyMutation.mutateAsync(partyData);
            }

            // Reset form with current values and close dialog directly
            form.reset(form.getValues());
            setOpen(false);
        } catch (error: unknown) {
            // Better error handling
            const apiError = error as ApiErrorResponse;

            if (apiError.response?.data) {
                const errorData = apiError.response.data;
                setMutationErrors(errorData);

                // Set form errors for each field
                Object.entries(errorData).forEach(([key, messages]) => {
                    if (Array.isArray(messages) && messages.length > 0) {
                        form.setError(key as FormFields, {
                            type: 'manual',
                            message: messages[0]
                        });
                    }
                });
            } else {
                // Handle non-API errors
                const errorMessage = apiError.message || 'An unexpected error occurred';
                setMutationErrors({
                    form: [errorMessage]
                });
                console.error('Form submission error:', errorMessage);
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    const queryClient = useQueryClient();

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty && form.formState.isSubmitSuccessful === false) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(newOpen);
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        form.reset();
        setSelectedContact(undefined);
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            setIsSyncing(true);

            const values = form.getValues();
            const partyData = {
                status: values.status,
                description: values.description || "",
                contact: parseInt(values.party_contact, 10),
                law_firm: values.law_firm ? parseInt(values.law_firm, 10) : undefined,
                attorney: values.attorney ? parseInt(values.attorney, 10) : undefined,
            };
            let updatedParty;
            if (isEdit && partyId) {
                updatedParty = await updatePartyMutation.mutateAsync(partyData);
            } else {
                updatedParty = await createPartyMutation.mutateAsync(partyData);
            }

            await partySync.mutateAsync({
                party_id: updatedParty.id,
                target_case_ids: selectedCaseIds
            });

            setIsSyncDialogOpen(false);
            setOpen(false);
            form.reset(form.getValues());

        } catch (error) {
            console.error("Failed to sync party:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to sync party details",
            });
        } finally {
            setIsSyncing(false);
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {trigger || (
                        <Button variant="outline" className="border-teal-600 text-teal-600 hover:bg-teal-50">
                            {isEdit ? "EDIT" : "ADD"}
                        </Button>
                    )}
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? "Edit Party" : `Add ${selectedPartyType.charAt(0) + selectedPartyType.slice(1).toLowerCase()} Party`}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            {/* Status Field */}
                            <FormField
                                control={form.control}
                                name="status"
                                render={({ field }) => (
                                    <FormItem className="space-y-2">
                                        <RequiredLabel className="flex gap-1">Status</RequiredLabel>
                                        <Select onValueChange={field.onChange} value={field.value}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select status" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {PARTY_STATUS_CHOICES.map((status) => (
                                                    <SelectItem key={status.value} value={status.value}>
                                                        {status.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage className="text-red-500 text-sm mt-1">
                                            {mutationErrors.status?.[0]}
                                        </FormMessage>
                                    </FormItem>
                                )}
                            />

                            {/* Party Contact Field */}
                            <FormField
                                control={form.control}
                                name="party_contact"
                                render={({ field }) => (
                                    <FormItem className="space-y-2">
                                        <RequiredLabel className="flex gap-1">Contact</RequiredLabel>
                                        <div className="flex gap-2">
                                            <Select
                                                onValueChange={(value) => {
                                                    field.onChange(value);
                                                    const selectedContactData = partyContacts?.find(
                                                        contact => contact.id.toString() === value
                                                    );
                                                    setSelectedContact(selectedContactData ? selectedContactData.id : undefined);
                                                }}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="flex-1">
                                                        <SelectValue placeholder="Select contact" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent searchable>
                                                    {(partyContacts || []).map((contact: CasePartyContact) => (
                                                        <SelectItem
                                                            key={contact.id}
                                                            value={contact.id.toString()}
                                                        >
                                                            {`${contact.first_name} ${contact.last_name}`}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <div className="flex gap-2">
                                                <AddEditPartyContact
                                                    caseId={caseId}
                                                    partyType={selectedPartyType}
                                                    onSuccess={async (newContactId) => {
                                                        if (newContactId) {
                                                            try {
                                                                // First refetch the data
                                                                await queryClient.refetchQueries({
                                                                    queryKey: [CASE_PARTY_CONTACTS_KEY, caseId, selectedPartyType],
                                                                });

                                                                // Small delay to ensure data is available
                                                                await new Promise(resolve => setTimeout(resolve, 100));

                                                                // Then update the form and state
                                                                field.onChange(newContactId.toString());
                                                                setSelectedContact(newContactId);
                                                            } catch (error) {
                                                                console.error('Error updating contact selection:', error);
                                                            }
                                                        }
                                                    }}
                                                >
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        className="flex-1 text-green-600 border-green-600 hover:bg-green-100 hover:text-green-700"
                                                    >
                                                        ADD
                                                    </Button>
                                                </AddEditPartyContact>
                                                {selectedContact && (
                                                    <AddEditPartyContact
                                                        caseId={caseId}
                                                        partyType={selectedPartyType}
                                                        isEdit={true}
                                                        defaultValues={partyContacts?.find(c => c.id === selectedContact)}
                                                        onSuccess={() => {
                                                            // Only invalidate party contacts query
                                                            queryClient.invalidateQueries({
                                                                queryKey: [CASE_PARTY_CONTACTS_KEY, caseId, selectedPartyType],
                                                            });
                                                        }}
                                                    >
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            className="flex-1 text-[#060216]-600 border-blue-600 hover:bg-blue-100 hover:text-[#060216]-700"
                                                        >
                                                            EDIT
                                                        </Button>
                                                    </AddEditPartyContact>
                                                )}
                                            </div>
                                        </div>
                                        <FormMessage className="text-red-500 text-sm mt-1">
                                            {mutationErrors.party_contact?.[0]}
                                        </FormMessage>
                                    </FormItem>
                                )}
                            />

                            {/* Law Firm Field */}
                            <FormField
                                control={form.control}
                                name="law_firm"
                                render={({ field }) => (
                                    <FormItem className="space-y-2">
                                        <FormLabel>Law Firm</FormLabel>
                                        <div className="flex gap-2">
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value || ""}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="flex-1">
                                                        <SelectValue placeholder="Select law firm" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent searchable>
                                                    {field.value && (
                                                        <Button
                                                            variant="ghost"
                                                            className="mb-2 w-full justify-center text-sm"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                field.onChange("");
                                                            }}
                                                        >
                                                            Clear Selection
                                                        </Button>
                                                    )}
                                                    {lawFirms?.map((lawFirm: LawFirmResponse) => (
                                                        <SelectItem
                                                            key={lawFirm.id}
                                                            value={lawFirm.id.toString()}
                                                        >
                                                            {lawFirm.office_name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <div className="flex gap-2">
                                                <AddEditLawFirm
                                                    isEdit={false}
                                                    onSuccess={async (newLawFirmId) => {
                                                        if (newLawFirmId) {
                                                            try {
                                                                // First refetch the law firms data
                                                                await queryClient.refetchQueries({
                                                                    queryKey: ['lawFirms'],
                                                                });

                                                                // Small delay to ensure data is available
                                                                await new Promise(resolve => setTimeout(resolve, 100));
                                                                // Then update the form
                                                                field.onChange(newLawFirmId.toString());
                                                            } catch (error) {
                                                                console.error('Error updating law firm selection:', error);
                                                            }
                                                        }
                                                    }}
                                                >
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        className="flex-1 text-green-600 border-green-600 hover:bg-green-100 hover:text-green-700"
                                                    >
                                                        ADD
                                                    </Button>
                                                </AddEditLawFirm>
                                            </div>
                                        </div>
                                        <FormMessage className="text-red-500 text-sm mt-1">
                                            {mutationErrors.law_firm?.[0]}
                                        </FormMessage>
                                    </FormItem>
                                )}
                            />

                            {/* Attorney Field */}
                            <FormField
                                control={form.control}
                                name="attorney"
                                render={({ field }) => (
                                    <FormItem className="space-y-2">
                                        <FormLabel>Attorney</FormLabel>
                                        <div className="flex gap-2">
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value || ""}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="flex-1">
                                                        <SelectValue placeholder="Select attorney" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent searchable>
                                                    {field.value && (
                                                        <Button
                                                            variant="ghost"
                                                            className="mb-2 w-full justify-center text-sm"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                field.onChange("");
                                                            }}
                                                        >
                                                            Clear Selection
                                                        </Button>
                                                    )}
                                                    {attorneys?.map((attorney: AttorneyResponse) => (
                                                        <SelectItem
                                                            key={attorney.id}
                                                            value={attorney.id.toString()}
                                                        >
                                                            {`${attorney.first_name} ${attorney.last_name}`}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <div className="flex gap-2">
                                                <AddEditAttorneyContact
                                                    isEdit={false}
                                                    onSuccess={async (newAttorneyId) => {
                                                        if (newAttorneyId) {
                                                            try {
                                                                // First refetch the attorneys data
                                                                await queryClient.refetchQueries({
                                                                    queryKey: ['attorneys'],
                                                                });

                                                                // Small delay to ensure data is available
                                                                await new Promise(resolve => setTimeout(resolve, 100));

                                                                // Then update the form
                                                                field.onChange(newAttorneyId.toString());
                                                            } catch (error) {
                                                                console.error('Error updating attorney selection:', error);
                                                            }
                                                        }
                                                    }}
                                                >
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        className="flex-1 text-green-600 border-green-600 hover:bg-green-100 hover:text-green-700"
                                                    >
                                                        ADD
                                                    </Button>
                                                </AddEditAttorneyContact>
                                            </div>
                                        </div>
                                        <FormMessage className="text-red-500 text-sm mt-1">
                                            {mutationErrors.attorney?.[0]}
                                        </FormMessage>
                                    </FormItem>
                                )}
                            />

                            {/* Description Field */}
                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem className="space-y-2">
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <div className="bg-white border rounded-md">
                                                <RichTextEditor
                                                    value={field.value || ""}
                                                    onChange={(value) => field.onChange(value)}
                                                />
                                            </div>
                                        </FormControl>
                                        <FormMessage className="text-red-500 text-sm mt-1">
                                            {mutationErrors.description?.[0]}
                                        </FormMessage>
                                    </FormItem>
                                )}
                            />

                            {/* Action Buttons */}
                            <div className="flex justify-end gap-2 pt-4">
                                <Button
                                    variant="outline"
                                    type="button"
                                    onClick={handleCancelClick}
                                    className="uppercase"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="outline"
                                    type="button"
                                    onClick={() => setIsSyncDialogOpen(true)}
                                    disabled={isSyncing}
                                    className="uppercase"
                                >
                                    Sync Linked Cases
                                </Button>
                                <Button
                                    type="button"
                                    onClick={(e) => {
                                        form.handleSubmit((values) => onSubmit(values))(e);
                                    }}
                                    disabled={isSubmitting}
                                >
                                    Save
                                </Button>
                            </div>

                            {mutationErrors.form && (
                                <div className="text-red-500 text-sm">
                                    {mutationErrors.form[0]}
                                </div>
                            )}
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />

            <SyncCaseDialog
                isOpen={isSyncDialogOpen}
                onClose={() => setIsSyncDialogOpen(false)}
                onSync={handleSync}
                isSyncing={isSyncing}
                caseId={caseId}
                syncType="parties"
            />
        </>
    );
};

export default AddEditCaseParty; 