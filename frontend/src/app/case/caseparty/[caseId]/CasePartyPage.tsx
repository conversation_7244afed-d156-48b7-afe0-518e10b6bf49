"use client";

import { notFound } from "next/navigation";
import { Plus, ArrowLeft, Edit } from "lucide-react";
import PartyCard from "@/app/case/caseparty/components/PartyCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import AddEditCaseParty from "../components/AddEditCaseParty";
import { useCasePartiesQuery, useCaseExpertWitnessesQuery } from "@/services/case-management/partyService";
import {
  CaseParty,
  PartyType,
  PartyStatus,
} from "@/type/case-management/partyTypes";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import SelectWitness from "../components/SelectWitness";
import ExpertWitnessList from "../components/ExpertWitnessList";
import { useRouter } from 'next/navigation';
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";

interface CasePartyPageProps {
  caseId: string;
}

const getContextTypeAndId = (partyType: PartyType) => {
  switch (partyType) {
    case PartyType.OTHER:
      return { contextType: 'other_party' as TemplateContextType, other_party_id: '' };
    case PartyType.PLAINTIFF:
      return { contextType: 'other_plaintiff' as TemplateContextType, other_plaintiff_id: '' };
    case PartyType.WITNESS:
      return { contextType: 'witness' as TemplateContextType, witness_id: '' };
    default:
      return { contextType: 'other_party' as TemplateContextType, other_party_id: '' };
  }
};

export default function CasePartyPage({ caseId }: CasePartyPageProps) {
  const router = useRouter();

  if (!caseId) {
    notFound();
  }

  const { data: parties = [], isLoading: isLoadingParties } = useCasePartiesQuery(caseId);
  const { data: expertWitnesses = [], isLoading: isLoadingExpertWitnesses } = useCaseExpertWitnessesQuery(caseId);

  if (isLoadingParties || isLoadingExpertWitnesses) {
    return <div className="min-h-screen bg-gray-50 p-8">Loading...</div>;
  }

  const plaintiffParties = parties.filter(
    (party: CaseParty) => party.contact?.party_type === PartyType.PLAINTIFF
  );
  const witnessParties = parties.filter(
    (party: CaseParty) => party.contact?.party_type === PartyType.WITNESS
  );
  const otherParties = parties.filter(
    (party: CaseParty) => party.contact?.party_type === PartyType.OTHER
  );

  const renderPartyCards = (filteredParties: CaseParty[]) => {
    if (filteredParties.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">No parties found.</div>
      );
    }

    return filteredParties.map((party) => {
      const { contextType, ...idProps } = getContextTypeAndId(party.contact.party_type);
      const idKey = Object.keys(idProps)[0];

      return (
        <div key={party.id} className="relative group">
          <PartyCard
            title={`${party.contact?.first_name} ${party.contact?.last_name}`}
            // icon={<Users className="h-5 w-5 text-gray-500" />}
            info={{
              status: party.status,
              description: party.description || "",
              phone: party.contact?.phone || "",
              lawFirm: party.law_firm?.office_name || "",
              attorney: party.attorney
                ? `${party.attorney.first_name} ${party.attorney.last_name}`
                : "",
              attorneyPhone: party.attorney?.phone || "",
              attorneyEmail: party.attorney?.email || "",
              address: party.contact?.street1
                ? `${party.contact.street1}${party.contact.street2 ? `, ${party.contact.street2}` : ""
                }, ${party.contact.city || ""}, ${party.contact.state || ""} ${party.contact.zip_code || ""
                }`
                : "",
            }}
          />
          <div className="absolute top-4 right-4 transition-opacity flex items-center gap-2">
            <AddEditCaseParty
              caseId={caseId}
              isEdit={true}
              defaultValues={{
                status: party.status,
                description: party.description,
                party_contact: party.contact?.id?.toString(),
                law_firm: party.law_firm?.id?.toString(),
                attorney: party.attorney?.id?.toString(),
                party_type: party.contact.party_type,
              }}
              partyId={party.id.toString()}
              trigger={
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-green-600 hover:text-green-700 hover:bg-green-50 p-2 h-8 w-8"
                >
                  <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                </Button>
              }
            />
            <ManageEmailTemplate
              caseId={caseId}
              contextType={contextType as TemplateContextType}
              {...{ [idKey]: party.id.toString() }}
            />
          </div>
        </div>
      );
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8 space-y-6">
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="hover:bg-transparent p-0 justify-start w-fit"
      >
        <ArrowLeft className="w-5 h-5 mr-2" />
        Back
      </Button>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Case Parties</h1>
        <div className="flex gap-4">
          <SelectWitness
            caseId={caseId}
          />
          <AddEditCaseParty
            caseId={caseId}
            defaultValues={{
              party_type: PartyType.PLAINTIFF,
              status: PartyStatus.PENDING_CONTACT,
              party_contact: ""
            }}
            trigger={
              <Button
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 stroke-[1.5] mr-2" />
                Plaintiff
              </Button>
            }
          />
          <AddEditCaseParty
            caseId={caseId}
            defaultValues={{
              party_type: PartyType.WITNESS,
              status: PartyStatus.PENDING_CONTACT,
              party_contact: "",
            }}
            trigger={
              <Button
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 stroke-[1.5] mr-2" />
                Witness
              </Button>
            }
          />
          <AddEditCaseParty
            caseId={caseId}
            defaultValues={{
              party_type: PartyType.OTHER,
              status: PartyStatus.PENDING_CONTACT,
              party_contact: "",
            }}
            trigger={
              <Button
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 stroke-[1.5] mr-2" />
                Other Party
              </Button>
            }
          />
        </div>
      </div>

      <Tabs defaultValue="expert-witnesses" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="expert-witnesses" className="text-sm">
            Expert Witnesses ({expertWitnesses.length})
          </TabsTrigger>
          <TabsTrigger value="plaintiffs" className="text-sm">
            Plaintiffs ({plaintiffParties.length})
          </TabsTrigger>
          <TabsTrigger value="witnesses" className="text-sm">
            Witnesses ({witnessParties.length})
          </TabsTrigger>
          <TabsTrigger value="others" className="text-sm">
            Other Parties ({otherParties.length})
          </TabsTrigger>
        </TabsList>
        <TabsContent value="expert-witnesses" className="space-y-4 mt-4">
          {/* <ExpertWitnessList
            caseId={caseId}
            handleSyncError={handleSyncError}
            handleExpertWitnessSyncComplete={handleExpertWitnessSyncComplete}
          /> */}
        </TabsContent>
        <TabsContent value="plaintiffs" className="space-y-4 mt-4">
          {renderPartyCards(plaintiffParties)}
        </TabsContent>
        <TabsContent value="witnesses" className="space-y-4 mt-4">
          {renderPartyCards(witnessParties)}
        </TabsContent>
        <TabsContent value="others" className="space-y-4 mt-4">
          {renderPartyCards(otherParties)}
        </TabsContent>
      </Tabs>
    </div>
  );
}