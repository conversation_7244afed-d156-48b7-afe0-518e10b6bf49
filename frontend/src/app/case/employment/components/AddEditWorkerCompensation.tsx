'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    Di<PERSON><PERSON>eader,
    <PERSON><PERSON>Title,
    Di<PERSON>Footer,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Edit, Plus } from "lucide-react";
import { useState, useEffect } from "react";
import { AddEditAttorneyContact } from "./AddEditAttorneyContact";
import { AddEditLawFirm } from "@/app/client-details/components/AddEditLawFirm";
import { useLawFirmsQuery, useA<PERSON>rneysQuery, useInsuranceCompaniesQuery } from "@/services/orgAPIs";
import { useCreateWorkersCompensationMutation, useUpdateWorkersCompensationMutation } from "@/services/case-management/employerService";
import { EmployerWorkersCompensationResponse } from "@/type/case-management/employerTypes";
import { useQueryClient } from '@tanstack/react-query';
import AddEditInsurance from "./AddEditInsurance";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import CustomCKEditor from "@/components/ckeditor/CKEditor";


const STATUS_CHOICES = [
    { value: "PENDING", label: "Pending" },
    { value: "REQUESTED", label: "Requested" },
    { value: "RECEIVED", label: "Received" },
    { value: "NA", label: "N/A" },
];

const workerCompFormSchema = z.object({
    status: z.string(),
    lawFirm: z.string(),
    attorney: z.string(),
    insuranceCompany: z.string(),
    insurancePolicy: z.string(),
    insuranceClaim: z.string(),
    note: z.string().optional().nullable(),
});

type WorkerCompFormValues = z.infer<typeof workerCompFormSchema>;

interface AddEditWorkerCompProps {
    isEdit?: boolean;
    caseId: string;
    employerId: string;
    initialData?: EmployerWorkersCompensationResponse;
    children: React.ReactNode;
    onSuccess?: () => void;
}



export function AddEditWorkerCompensation({
    isEdit = false,
    caseId,
    employerId,
    initialData,
    children,
    onSuccess
}: AddEditWorkerCompProps) {
    console.log("children", children);
    const [open, setOpen] = useState(false);
    const [isAddingLawFirm, setIsAddingLawFirm] = useState(false);
    const [selectedLawFirm, setSelectedLawFirm] = useState<number | undefined>(
        initialData?.law_firm ? (typeof initialData.law_firm === 'object' ? initialData.law_firm.id : initialData.law_firm) : undefined
    );
    
    const selectedAttorney = initialData?.attorney ? (typeof initialData.attorney === 'object' ? initialData.attorney.id : initialData.attorney) : undefined
    
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    // Fetch data from APIs
    const { data: lawFirms, isLoading: isLoadingLawFirms, refetch: refetchLawFirms } = useLawFirmsQuery();
    const { data: attorneys, isLoading: isLoadingAttorneys } = useAttorneysQuery();
    const { data: insuranceCompanies, isLoading: isLoadingInsurance } = useInsuranceCompaniesQuery();

    // Create/Update mutations
    const createWorkerComp = useCreateWorkersCompensationMutation(caseId, employerId);
    const updateWorkerComp = useUpdateWorkersCompensationMutation(caseId, employerId);
    const queryClient = useQueryClient();


    const form = useForm<WorkerCompFormValues>({
        resolver: zodResolver(workerCompFormSchema),
        defaultValues: {
            status: initialData?.status || "",
            lawFirm: initialData?.law_firm ? initialData.law_firm.toString() : "",
            attorney: initialData?.attorney ? initialData.attorney.toString() : "",
            insuranceCompany: initialData?.insurance_company ? initialData.insurance_company.toString() : "",
            insurancePolicy: initialData?.insurance_policy_number || "",
            insuranceClaim: initialData?.insurance_claim_number || "",
            note: initialData?.note || "",
        }
    });

    useEffect(() => {
        if (initialData && isEdit) {
            form.reset({
                status: initialData.status || "",
                lawFirm: initialData.law_firm ? initialData.law_firm.toString() : "",
                attorney: initialData.attorney ? initialData.attorney.toString() : "",
                insuranceCompany: initialData.insurance_company ? initialData.insurance_company.toString() : "",
                insurancePolicy: initialData.insurance_policy_number || "",
                insuranceClaim: initialData.insurance_claim_number || "",
                note: initialData.note ||"",
            });
        }
    }, [initialData, isEdit, form]);

    // Handle law firm selection
    const handleLawFirmChange = (value: string) => {
        setSelectedLawFirm(parseInt(value));
        form.setValue('lawFirm', value);
    };

    // Handle law firm creation/update success
    const handleLawFirmSuccess = async () => {
        setIsAddingLawFirm(false);
        await refetchLawFirms();
    };

    async function onSubmit(values: WorkerCompFormValues) {
        if (isAddingLawFirm) return; // Prevent submission while adding law firm

        try {
            const workerCompData = {
                status: values.status,
                law_firm: values.lawFirm ? parseInt(values.lawFirm) : undefined,
                attorney: values.attorney ? parseInt(values.attorney) : undefined,
                insurance_company: values.insuranceCompany ? parseInt(values.insuranceCompany) : undefined,
                insurance_policy_number: values.insurancePolicy,
                insurance_claim_number: values.insuranceClaim,
                ...(values.note && { note: values.note }),
            };

            if (isEdit) {
                await updateWorkerComp.mutateAsync(workerCompData);
            } else {
                await createWorkerComp.mutateAsync(workerCompData);
            }

            // Refetch employers data to update the UI
            await queryClient.invalidateQueries({ queryKey: ['employers', caseId] });

            setOpen(false);
            form.reset();
            onSuccess?.();
        } catch (error) {
            console.error('Failed to submit worker compensation:', error);
        }
    }

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!isAddingLawFirm) { // Only allow closing if not adding law firm
                if (!newOpen) {
                    handleClose();
                } else {
                    setOpen(newOpen);
                }
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        form.reset();
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {isEdit ? (
                        <Button
                            variant="ghost"
                            className="flex items-center gap-2 hover:bg-green-50"
                            size="sm"
                        >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                        </Button>
                    ) : (
                        <Button
                            variant="default"
                            className="bg-transparent hover:bg-blue-50 text-[#060216]-600 hover:text-[#060216]-700 border border-blue-300 shadow-sm"
                        >
                            <Plus className="h-4 w-4 stroke-[1.5]" />
                            Worker Compensation
                        </Button>
                    )}
                </DialogTrigger>

                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? "Edit Worker Compensation" : "Add Worker Compensation"}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            {/* Status */}
                            <FormField
                                control={form.control}
                                name="status"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Status</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select status" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {STATUS_CHOICES.map((status) => (
                                                    <SelectItem key={status.value} value={status.value}>
                                                        {status.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Law Firm and Attorney Section */}
                            <div className="grid grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <FormField
                                        control={form.control}
                                        name="lawFirm"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Law Firm</FormLabel>
                                                <Select
                                                    onValueChange={handleLawFirmChange}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select law firm" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent searchable>
                                                        {isLoadingLawFirms ? (
                                                            <SelectItem value="loading">Loading...</SelectItem>
                                                        ) : lawFirms?.map((firm) => (
                                                            <SelectItem key={firm.id} value={firm.id.toString()}>
                                                                {firm.office_name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <div className="flex gap-2">
                                        <AddEditLawFirm
                                            isEdit={false}
                                            onSuccess={handleLawFirmSuccess}
                                            onOpenChange={(open) => setIsAddingLawFirm(open)}
                                        >
                                            <Button variant="outline" size="sm">
                                                <Plus className="h-4 w-4 mr-2" />
                                                Law Firm
                                            </Button>
                                        </AddEditLawFirm>
                                        {selectedLawFirm && (
                                            <AddEditLawFirm
                                                isEdit={true}
                                                selectedLawFirm={lawFirms?.find(firm => firm.id === selectedLawFirm)}
                                                onSuccess={handleLawFirmSuccess}
                                                onOpenChange={(open) => setIsAddingLawFirm(open)}
                                            >
                                                <Button variant="ghost" size="sm">
                                                    <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                                                </Button>
                                            </AddEditLawFirm>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <FormField
                                        control={form.control}
                                        name="attorney"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Attorney</FormLabel>
                                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select attorney" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent searchable>
                                                        {isLoadingAttorneys ? (
                                                            <SelectItem value="loading">Loading...</SelectItem>
                                                        ) : attorneys?.map((attorney) => (
                                                            <SelectItem key={attorney.id} value={attorney.id.toString()}>
                                                                {`${attorney.first_name} ${attorney.last_name}`}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <div className="flex gap-2">
                                        <AddEditAttorneyContact isEdit={false}>
                                            <Button variant="outline" size="sm">
                                                <Plus className="h-4 w-4 mr-2" />
                                                Attorney
                                            </Button>
                                        </AddEditAttorneyContact>
                                        {selectedAttorney && (
                                            <AddEditAttorneyContact isEdit={true}>
                                                <Button variant="ghost" size="sm">
                                                    <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                                                </Button>
                                            </AddEditAttorneyContact>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Insurance Section */}
                            <div className="grid grid-cols-3 gap-4">
                                <div className="space-y-2">
                                    <FormField
                                        control={form.control}
                                        name="insuranceCompany"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Insurance Company</FormLabel>
                                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select company" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent searchable>
                                                        {isLoadingInsurance ? (
                                                            <SelectItem value="loading">Loading...</SelectItem>
                                                        ) : insuranceCompanies?.map((company) => (
                                                            <SelectItem key={company.id} value={company.id.toString()}>
                                                                {company.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <div className="space-y-2">
                                        <div className="flex gap-2">
                                            <AddEditInsurance isEdit={false} caseId={caseId} employerId={employerId} />
                                        </div>
                                    </div>
                                </div>

                                <FormField
                                    control={form.control}
                                    name="insurancePolicy"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Insurance Policy #</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter policy number" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="insuranceClaim"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Insurance Claim #</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter claim number" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            {/* Note */}
                            <FormField
                                control={form.control}
                                name="note"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Note</FormLabel>
                                        <FormControl>
                                            <div className="bg-white border rounded-md">
                                                <CustomCKEditor
                                                    initialValue={field.value || ""}
                                                    onChange={(value) => field.onChange(value)}
                                                    minHeight="200px"
                                                />
                                            </div>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <DialogFooter>
                                <Button
                                    variant="outline"
                                    type="button"
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="button"
                                    onClick={(e) => {
                                        form.handleSubmit((values) => onSubmit(values))(e);
                                    }}
                                    disabled={createWorkerComp.isPending || updateWorkerComp.isPending}
                                >
                                    {isEdit ? "Update" : "Create"}
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    );
} 