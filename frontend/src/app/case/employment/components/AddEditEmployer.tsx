"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Edit, Plus } from "lucide-react";
import { useState, useEffect } from "react";
import {
  useCreateEmployerMutation,
  useUpdateEmployerMutation,
} from "@/services/case-management/employerService";
import { useToast } from "@/hooks/use-toast";
import { USStatesLabels } from "@/constants/commont";
import { PhoneNumberInput } from "@/components/ui/phone-number-input";
import { EmployerResponse } from "@/type/case-management/employerTypes";
import CustomCKEditor from "@/components/ckeditor/CKEditor";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

const employerFormSchema = z.object({
  lostWagesStatus: z.enum(["PENDING", "REQUESTED", "RECEIVED", "NA"]),
  incomeType: z.enum(["HOURLY", "SALARY"]),
  companyName: z.string().min(1, "Company name is required"),
  unemployed: z.enum(["yes", "no"]),
  position: z.string().optional().nullable(),
  totalLostWages: z.string().optional().nullable(),
  hoursMissed: z.string().optional().nullable(),
  weeksMissed: z.string().optional().nullable(),
  wage: z.string().optional().nullable(),
  contactFirstName: z.string().optional().nullable(),
  contactLastName: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  phoneExt: z.string().optional().nullable(),
  fax: z.string().optional().nullable(),
  street1: z.string().optional().nullable(),
  street2: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  zip: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
});

type EmployerFormValues = z.infer<typeof employerFormSchema>;

interface AddEditEmployerProps {
  isEdit?: boolean;
  caseId: string;
  onSubmit?: (data: EmployerFormValues) => void;
  triggerClassName?: string;
  initialData?: EmployerResponse;
}

export function AddEditEmployer({
  isEdit = false,
  caseId,
  onSubmit,
  initialData,
}: Required<Pick<AddEditEmployerProps, "caseId">> &
  Pick<AddEditEmployerProps, "isEdit" | "onSubmit" | "initialData">) {
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const { toast } = useToast();
  const createEmployer = useCreateEmployerMutation(caseId);
  const updateEmployer = useUpdateEmployerMutation(
    caseId,
    initialData?.id?.toString() || ""
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getDefaultValues = (): EmployerFormValues => {
    if (isEdit && initialData) {
      return {
        lostWagesStatus: (initialData.lost_wages_status?.toUpperCase() ||
          "PENDING") as "PENDING" | "REQUESTED" | "RECEIVED" | "NA",
        incomeType: (initialData.income_type?.toUpperCase() || "HOURLY") as
          | "HOURLY"
          | "SALARY",
        unemployed: initialData.unemployed ? "yes" : "no",
        position: initialData.position || null,
        totalLostWages: initialData.total_lost_wages || null,
        hoursMissed: initialData.hours_missed || null,
        weeksMissed: initialData.weeks_missed || null,
        wage: initialData.wage || null,
        companyName: initialData.company_name || "",
        contactFirstName: initialData.contact_first_name || null,
        contactLastName: initialData.contact_last_name || null,
        phone: initialData.phone || null,
        phoneExt: initialData.phone_ext || null,
        fax: initialData.fax || null,
        street1: initialData.street1 || null,
        street2: initialData.street2 || null,
        city: initialData.city || null,
        state: initialData.state || null,
        zip: initialData.zip_code || null,
        description: initialData.description || "",
      };
    }

    return {
      lostWagesStatus: "PENDING",
      incomeType: "HOURLY",
      unemployed: "no",
      companyName: "",
      position: null,
      totalLostWages: null,
      hoursMissed: null,
      weeksMissed: null,
      wage: null,
      contactFirstName: null,
      contactLastName: null,
      phone: null,
      phoneExt: null,
      fax: null,
      street1: null,
      street2: null,
      city: null,
      state: null,
      zip: null,
      description: "",
    };
  };

  const form = useForm<EmployerFormValues>({
    resolver: zodResolver(employerFormSchema),
    defaultValues: getDefaultValues(),
  });

  // Reset form when modal opens or when initialData changes
  useEffect(() => {
    if (open) {
      form.reset(getDefaultValues());
    }
  }, [open, initialData, isEdit]);

  const handleOpenChange = (newOpen: boolean) => {
    // If closing the modal and form has unsaved changes
    if (!newOpen && form.formState.isDirty) {
      setShowUnsavedAlert(true);
    } else {
      setOpen(newOpen);
      if (newOpen) {
        // Reset form when opening the modal
        form.reset(getDefaultValues());
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    setOpen(false);
    form.reset(getDefaultValues());
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const onSubmitForm = async (data: EmployerFormValues) => {
    try {
      setIsSubmitting(true);
      
      const cleanOptionalString = (
        value: string | null | undefined
      ): string | undefined => {
        return value?.trim() || undefined;
      };

      const cleanOptionalNumber = (
        value: string | null | undefined
      ): string | undefined => {
        return value && !isNaN(Number(value)) ? value : undefined;
      };

      const employerData = {
        case: caseId,
        lost_wages_status: data.lostWagesStatus,
        income_type: data.incomeType,
        company_name: data.companyName.trim(),
        unemployed: data.unemployed === "yes",
        ...(cleanOptionalString(data.position) && {
          position: cleanOptionalString(data.position),
        }),
        ...(cleanOptionalNumber(data.totalLostWages) && {
          total_lost_wages: cleanOptionalNumber(data.totalLostWages),
        }),
        ...(cleanOptionalNumber(data.hoursMissed) && {
          hours_missed: cleanOptionalNumber(data.hoursMissed),
        }),
        ...(cleanOptionalNumber(data.weeksMissed) && {
          weeks_missed: cleanOptionalNumber(data.weeksMissed),
        }),
        ...(cleanOptionalNumber(data.wage) && {
          wage: cleanOptionalNumber(data.wage),
        }),
        ...(cleanOptionalString(data.contactFirstName) && {
          contact_first_name: cleanOptionalString(data.contactFirstName),
        }),
        ...(cleanOptionalString(data.contactLastName) && {
          contact_last_name: cleanOptionalString(data.contactLastName),
        }),
        ...(cleanOptionalString(data.phone) && {
          phone: cleanOptionalString(data.phone),
        }),
        ...(cleanOptionalString(data.phoneExt) && {
          phone_ext: cleanOptionalString(data.phoneExt),
        }),
        ...(cleanOptionalString(data.fax) && {
          fax: cleanOptionalString(data.fax),
        }),
        ...(cleanOptionalString(data.street1) && {
          street1: cleanOptionalString(data.street1),
        }),
        ...(cleanOptionalString(data.street2) && {
          street2: cleanOptionalString(data.street2),
        }),
        ...(cleanOptionalString(data.city) && {
          city: cleanOptionalString(data.city),
        }),
        ...(cleanOptionalString(data.state) && {
          state: cleanOptionalString(data.state),
        }),
        ...(cleanOptionalString(data.zip) && {
          zip_code: cleanOptionalString(data.zip),
        }),
        ...(cleanOptionalString(data.description) && {
          description: cleanOptionalString(data.description),
        }),
      };

      if (isEdit) {
        await updateEmployer.mutateAsync(employerData);
        toast({
          title: "Success",
          description: "Employer updated successfully",
        });
      } else {
        await createEmployer.mutateAsync(employerData);
        toast({
          title: "Success",
          description: "Employer created successfully",
        });
      }
      
      onSubmit?.(data);
      setOpen(false);
      // Reset form after successful submission
      form.reset(getDefaultValues());
    } catch (error) {
      console.error("Error submitting employer data:", error);
      toast({
        title: "Error",
        description: "Failed to save employer data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEdit ? (
            <Button
              variant="link"
            >
              <Edit className="h-4 w-4 text-green-600" />
            </Button>
          ) : (
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4" />
              Employer
            </Button>
          )}
        </DialogTrigger>

        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>
              {isEdit ? "Edit Employer" : "Add Employer"}
            </DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmitForm)}
              className="space-y-6"
            >
              <div className="space-y-6 max-h-[80vh] overflow-y-auto pr-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {/* Lost Wages Status */}
                <FormField
                  control={form.control}
                  name="lostWagesStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lost wages status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="PENDING" defaultChecked>
                            Pending
                          </SelectItem>
                          <SelectItem value="REQUESTED">Requested</SelectItem>
                          <SelectItem value="RECEIVED">Received</SelectItem>
                          <SelectItem value="NA">N/A</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Work Details Group */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="position"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Position</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value || ""}
                            placeholder="Position"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="totalLostWages"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Total lost wages</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value ?? ""}
                            onChange={(e) => field.onChange(e.target.value)}
                            placeholder="0.00"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="hoursMissed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Hours missed</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value ?? ""}
                            onChange={(e) => field.onChange(e.target.value)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="weeksMissed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Weeks missed</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value ?? ""}
                            onChange={(e) => field.onChange(e.target.value)}
                            placeholder="5"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Income Details */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="incomeType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Income based on</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex gap-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="HOURLY" id="hourly" />
                              <Label htmlFor="hourly">Hourly</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="SALARY" id="salary" />
                              <Label htmlFor="salary">Salary</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="wage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Wage</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value ?? ""}
                            onChange={(e) => field.onChange(e.target.value)}
                            placeholder="5000"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Company Information */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="companyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="after:content-['*'] after:text-red-500 after:ml-0.5">
                          Company name
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value}
                            placeholder="Company name"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="contactFirstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact first name</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ""}
                              placeholder="First name"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="contactLastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact last name</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ""}
                              placeholder="Last name"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <FormLabel>Phone</FormLabel>
                    <div className="flex gap-2">
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <PhoneNumberInput
                                value={field.value || ""}
                                onChange={(value) => field.onChange(value)}
                                placeholder="************"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phoneExt"
                        render={({ field }) => (
                          <FormItem className="w-20">
                            <FormControl>
                              <Input
                                {...field}
                                value={field.value || ""}
                                placeholder="Ext"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name="fax"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fax</FormLabel>
                        <FormControl>
                          <PhoneNumberInput
                            value={field.value || ""}
                            onChange={(value) => field.onChange(value)}
                            placeholder="************"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Address */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="street1"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street 1</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value ?? ""}
                            placeholder="1256 street number"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="street2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street 2</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value ?? ""}
                            placeholder="9875 house number"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value ?? ""}
                              placeholder="City"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="state"
                      render={({ field: { value, ...rest } }) => (
                        <FormItem>
                          <FormLabel>State</FormLabel>
                          <FormControl>
                            <Select {...rest} value={value || ""}>
                              <SelectTrigger>
                                <SelectValue placeholder="State" />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.entries(USStatesLabels).map(
                                  ([value, label]) => (
                                    <SelectItem key={value} value={value}>
                                      {label}
                                    </SelectItem>
                                  )
                                )}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="zip"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ZIP</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value ?? ""}
                              placeholder="ZIP code"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <div className="bg-white border rounded-md">
                          <CustomCKEditor
                            initialValue={field.value || ""}
                            onChange={(value) => field.onChange(value)}
                            minHeight="200px"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Unemployed Status */}
                {/* {isEdit ? (
                                    <FormField
                                        control={form.control}
                                        name="unemployed"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Unemployed</FormLabel>
                                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select status" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    <SelectItem value="no">No</SelectItem>
                                                    <SelectItem value="yes">Yes</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                    />
                                ) : null} */}
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    const isDirty = form.formState.isDirty;
                    if (isDirty) {
                      setShowUnsavedAlert(true);
                    } else {
                      handleClose();
                    }
                  }}
                  type="button"
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {isSubmitting ? "Saving..." : isEdit ? "Update" : "Create"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
}
