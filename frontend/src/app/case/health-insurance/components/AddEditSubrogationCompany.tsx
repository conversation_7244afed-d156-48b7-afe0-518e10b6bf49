"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  useSubrogationCompanyQuery,
  useCreateSubrogationCompanyMutation,
  useUpdateSubrogationCompanyMutation,
} from "@/services/orgAPIs";
import { SubrogationCompanyCreateRequest } from "@/type/case-management/orgTypes";
import { Loader2 } from "lucide-react";
import { USStatesLabels } from "@/constants/commont";
import { PhoneNumberInput } from "@/components/ui/phone-number-input";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import CustomCKEditor from "@/components/ckeditor/CKEditor";

const phoneRegex = /^\d{3}-\d{3}-\d{4}$/;
const zipRegex = /^\d{5}(-\d{4})?$/;
const taxIdRegex = /^\d{2}-\d{7}$|^\d{9}$/;

const formSchema = z.object({
  company: z.string().min(1, "Company name is required"),
  payee: z.string().optional(),
  phone: z
    .string()
    .regex(phoneRegex, "Please enter a valid phone number (e.g., ************)")
    .optional()
    .or(z.literal("")),
  cell: z
    .string()
    .regex(phoneRegex, "Please enter a valid phone number (e.g., ************)")
    .optional()
    .or(z.literal("")),
  street1: z.string().optional(),
  street2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z
    .string()
    .regex(
      zipRegex,
      "Please enter a valid ZIP code (e.g., 12345 or 12345-6789)"
    )
    .optional()
    .or(z.literal("")),
  email: z
    .string()
    .email("Please enter a valid email address")
    .optional()
    .or(z.literal("")),
  fax: z
    .string()
    .regex(phoneRegex, "Please enter a valid fax number (e.g., ************)")
    .optional()
    .or(z.literal("")),
  taxId: z
    .string()
    .regex(
      taxIdRegex,
      "Please enter a valid Tax ID (e.g., 12-3456789 or *********)"
    )
    .optional()
    .or(z.literal("")),
  website: z
    .string()
    .refine((val) => !val || /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/.test(val), {
      message: "Please enter a valid domain (e.g., example.com)"
    })
    .optional()
    .or(z.literal("")),
  note: z.string().optional(),
});

type AddEditSubrogationCompanyProps = {
  isEdit: boolean;
  id?: string;
  disabled?: boolean;
  onSuccess?: (newCompanyId?: number) => void;
};

export function AddEditSubrogationCompany({
  isEdit,
  id,
  disabled,
  onSuccess,
}: AddEditSubrogationCompanyProps) {
  const { data: subrogationCompany, isLoading } = useSubrogationCompanyQuery(
    id || ""
  );
  const createMutation = useCreateSubrogationCompanyMutation();
  const updateMutation = useUpdateSubrogationCompanyMutation(id || "");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      company: "",
      payee: "",
      phone: "",
      cell: "",
      street1: "",
      street2: "",
      city: "",
      state: "",
      zip: "",
      email: "",
      fax: "",
      taxId: "",
      website: "",
      note: "",
    },
  });

  // Update form values when subrogation company data is loaded
  useEffect(() => {
    if (isEdit && subrogationCompany) {
      form.reset({
        company: subrogationCompany.company || "",
        payee: subrogationCompany.payee || "",
        phone: subrogationCompany.phone || "",
        cell: subrogationCompany.cell || "",
        street1: subrogationCompany.street1 || "",
        street2: subrogationCompany.street2 || "",
        city: subrogationCompany.city || "",
        state: subrogationCompany.state || "",
        zip: subrogationCompany.zip_code || "",
        email: subrogationCompany.email || "",
        fax: subrogationCompany.fax || "",
        taxId: subrogationCompany.tax_id || "",
        website: subrogationCompany.website || "",
        note: subrogationCompany.note || "",
      });
    }
  }, [form, isEdit, subrogationCompany]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const data: SubrogationCompanyCreateRequest = {
      company: values.company,
      payee: values.payee,
      phone: values.phone || "",
      cell: values.cell || "",
      street1: values.street1 || "",
      street2: values.street2 || "",
      city: values.city || "",
      state: values.state || "",
      zip_code: values.zip || "",
      email: values.email || "",
      fax: values.fax || "",
      tax_id: values.taxId || "",
      website: values.website || "",
      note: values.note || "",
    };

    try {
      let response;
      if (isEdit) {
        response = await updateMutation.mutateAsync(data);
        // For edit operations, call onSuccess without ID and close immediately
        onSuccess?.();
        handleClose();
      } else {
        response = await createMutation.mutateAsync(data);
        
        // For create operations, call onSuccess with ID and delay closing
        if (response && response.id) {
          // Call onSuccess callback with the new company ID
          onSuccess?.(response.id);
          
          // Close the dialog after a delay to ensure parent component has time
          // to process the selection and refresh the companies list
          setTimeout(() => {
            form.reset();
            setOpen(false);
          }, 300);
          
          // Return early to prevent immediate form reset and dialog close
          return;
        }
      }
      handleClose();
    } catch (error) {
      console.error("Failed to save subrogation company:", error);
    }
  };

  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

  const handleClose = () => {
    form.reset();
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(newOpen);
      }
    }
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="border-teal-600 text-teal-600 hover:bg-teal-50"
            disabled={disabled}
          >
            {isEdit ? "EDIT" : "ADD"}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {isEdit ? "Edit Subrogation Company" : "Add Subrogation Company"}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid gap-4 overflow-y-auto max-h-[60vh] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <Card>
                  <CardHeader>
                    <CardTitle>Company Information</CardTitle>
                  </CardHeader>
                  <CardContent className="grid gap-6">
                    <FormField
                      control={form.control}
                      name="company"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">
                            Company Name
                          </FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="payee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payee</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <PhoneNumberInput
                                {...field}
                                value={field.value || ""}
                                placeholder="************"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="cell"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cell</FormLabel>
                            <FormControl>
                              <PhoneNumberInput
                                {...field}
                                value={field.value || ""}
                                placeholder="************"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Address Information</CardTitle>
                  </CardHeader>
                  <CardContent className="grid gap-6">
                    <FormField
                      control={form.control}
                      name="street1"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Street 1</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="street2"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Street 2</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>State</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select state" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.entries(USStatesLabels).map(
                                    ([value, label]) => (
                                      <SelectItem key={value} value={value}>
                                        {label}
                                      </SelectItem>
                                    )
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="zip"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>ZIP</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="12345 or 12345-6789"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Contact & Additional Information</CardTitle>
                  </CardHeader>
                  <CardContent className="grid gap-6">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="email"
                                placeholder="<EMAIL>"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="fax"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Fax</FormLabel>
                            <FormControl>
                              <PhoneNumberInput
                                {...field}
                                value={field.value || ""}
                                placeholder="************"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="taxId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tax ID / SSN</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="12-3456789"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Website</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="www.example.com"
                                className="bg-white"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                      control={form.control}
                      name="note"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Note</FormLabel>
                          <FormControl>
                            <div className="bg-white border rounded-md">
                              <CustomCKEditor
                                initialValue={field.value || ""}
                                onChange={(value) => field.onChange(value)}
                                minHeight="200px"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={(e) => {
                    form.handleSubmit((values) => onSubmit(values))(e);
                  }}
                  disabled={
                    createMutation.isPending || updateMutation.isPending
                  }
                >
                  {createMutation.isPending || updateMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : isEdit ? (
                    "Save Changes"
                  ) : (
                    "Add Company"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
} 