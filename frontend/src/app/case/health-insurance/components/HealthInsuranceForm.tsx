"use client";

import React, { FC, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { default as AddEditInsuranceCompany } from "@/app/client-details/components/AddEditInsurance";
import { AddEditSubrogationcontact } from "./AddEditSubrogationcontact";
import { AddEditSubrogationCompany } from "./AddEditSubrogationCompany";
import {
  useSubrogationContactsQuery,
  useInsuranceCompaniesQuery,
  useSubrogationCompaniesQuery,
} from "@/services/orgAPIs";
import { formSchema, type HealthInsuranceFormValues } from "../types";
import { cn } from "@/lib/utils";
import RichTextEditor from '@/components/ui/RichTextEditor'
import { LinkedCasesResponse } from "@/type/linkCaseType";
import { useQueryClient } from "@tanstack/react-query";
import { formatDateForInput } from "@/utils/dateUtils";
import { formatDateForApi } from "@/utils/dateUtils";
import { RequiredLabel } from "@/components/ui/required-label";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";

const PLAN_TYPES = [
  { value: "private", label: "Private" },
  { value: "federal_va", label: "Federal VA" },
  { value: "federal_tricare", label: "Federal TriCare" },
  { value: "federal_febha", label: "Federal FEBHA" },
  { value: "medicaid", label: "Medicaid" },
  { value: "medicare", label: "Medicare" },
  { value: "medicare_advantage", label: "Medicare Advantage" },
  { value: "medicare_supplement", label: "Medicare Supplement" },
  { value: "indian_health_services", label: "Indian Health Services" },
  { value: "other", label: "Other" }
] as const;

export interface HealthInsuranceFormProps {
  defaultValues?: HealthInsuranceFormValues;
  isEdit: boolean;
  onSubmit: (values: HealthInsuranceFormValues) => Promise<void>;
  isSubmitting: boolean;
  onCancel: () => void;
  onFormStateChange: (state: HealthInsuranceFormValues | null) => void;
  linkedCases?: LinkedCasesResponse;
  isHeightNeeded?: boolean;
  onSync?: (values: HealthInsuranceFormValues) => Promise<void>;
  caseId: string;
}

export const HealthInsuranceForm: FC<HealthInsuranceFormProps> = ({
  defaultValues,
  isEdit,
  onSubmit,
  isSubmitting,
  onCancel,
  onFormStateChange,
  linkedCases,
  isHeightNeeded = true,
  onSync,
  caseId
}) => {
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  const form = useForm<HealthInsuranceFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      noInsurance: defaultValues?.noInsurance || false,
      insuranceCompany: defaultValues?.insuranceCompany,
      subrogationCompany: defaultValues?.subrogationCompany,
      insured: defaultValues?.insured || "",
      groupNumber: defaultValues?.groupNumber || "",
      memberNumber: defaultValues?.memberNumber || "",
      policyNumber: defaultValues?.policyNumber || "",
      representative: defaultValues?.representative,
      totalLien: defaultValues?.totalLien || "",
      adjustedLien: defaultValues?.adjustedLien || "",
      fileNumber: defaultValues?.fileNumber || "",
      planType: defaultValues?.planType || "",
      erisa: defaultValues?.erisa || "",
      medicare: defaultValues?.medicare || "",
      note: defaultValues?.note || "",
      target_cases: defaultValues?.target_cases || [],
      final_lien: defaultValues?.final_lien || false,
      final_lien_date: defaultValues?.final_lien_date || "",
      final_amount: defaultValues?.final_amount || "",
    }
  });

  // Add state variables to track the last updated timestamps
  const [subrogationCompaniesLastUpdated, setSubrogationCompaniesLastUpdated] = useState<number>(Date.now());
  const [subrogationContactsLastUpdated, setSubrogationContactsLastUpdated] = useState<number>(Date.now());

  const { data: subrogationContacts, isLoading: isLoadingContacts } =
    useSubrogationContactsQuery();
  const { data: insuranceCompanies, isLoading: isLoadingInsuranceCompanies } =
    useInsuranceCompaniesQuery();
  const { data: subrogationCompanies, isLoading: isLoadingSubrogationCompanies } =
    useSubrogationCompaniesQuery();

  const queryClient = useQueryClient();

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  useEffect(() => {
    const subscription = form.watch((value) => {
      if (onFormStateChange) {
        const hasChanges = JSON.stringify(value) !== JSON.stringify(defaultValues);
        onFormStateChange(hasChanges ? value as HealthInsuranceFormValues : null);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, defaultValues, onFormStateChange]);

  useEffect(() => {
    if (linkedCases?.direct_cases?.length || linkedCases?.indirect_cases?.length) {
      const currentValues = form.getValues();
      form.setValue('target_cases', currentValues.target_cases || []);
    }
  }, [linkedCases, form]);

  const noInsurance = form.watch("noInsurance");
  const finalLien = form.watch("final_lien");

  useEffect(() => {
    if (noInsurance) {
      form.setValue("insuranceCompany", "");
      form.setValue("insured", "");
      form.setValue("groupNumber", "");
      form.setValue("memberNumber", "");
      form.setValue("policyNumber", "");
      form.setValue("subrogationCompany", "");
      form.setValue("representative", "");
      form.setValue("totalLien", "");
      form.setValue("adjustedLien", "");
      form.setValue("fileNumber", "");
      form.setValue("planType", "");
      form.setValue("erisa", "False");
      form.setValue("medicare", "False");
      form.setValue("note", "");
      form.setValue("final_lien", false);
      form.setValue("final_lien_date", "");
      form.setValue("final_amount", "");
    }
  }, [noInsurance, form]);

  useEffect(() => {
    console.log('Form Values:', form.getValues());
    console.log('Linked Cases:', linkedCases);
  }, [form, linkedCases]);

  const handleCancel = () => {
    if (form.formState.isDirty) {
      onFormStateChange?.(form.getValues());
    }
    onCancel();
  };

  const handleSync = async (selectedCaseIds: string[]) => {
    try {
      setIsSyncing(true);
      const values = form.getValues();
      values.target_cases = selectedCaseIds;
      
      if (onSync) {
        await onSync(values);
      }
      
      setIsSyncDialogOpen(false);
    } catch (error) {
      console.error("Failed to sync insurance:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className={cn("grid gap-6 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", isHeightNeeded ? "max-h-[70vh]" : "")}>
            <FormField
              control={form.control}
              name="noInsurance"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={(checked) => {
                        field.onChange(checked);
                        if (checked) {
                          form.setValue("insuranceCompany", "");
                          form.setValue("insured", "");
                          form.setValue("groupNumber", "");
                          form.setValue("memberNumber", "");
                          form.setValue("policyNumber", "");
                          form.setValue("subrogationCompany", "");
                          form.setValue("representative", "");
                          form.setValue("totalLien", "");
                          form.setValue("adjustedLien", "");
                          form.setValue("fileNumber", "");
                          form.setValue("planType", "");
                          form.setValue("erisa", "False");
                          form.setValue("medicare", "False");
                          form.setValue("note", "");
                          form.clearErrors();
                        }
                      }}
                    />
                  </FormControl>
                  <FormLabel>No Insurance</FormLabel>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="insuranceCompany"
              render={({ field }) => (
                <div>
                  <FormItem>
                    <FormLabel>
                      Insurance Company
                      {!form.getValues("noInsurance") && (
                        <span className="text-red-700 ml-1">*</span>
                      )}
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                      disabled={noInsurance}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingInsuranceCompanies ? (
                          <SelectItem value="loading">Loading...</SelectItem>
                        ) : (
                          insuranceCompanies?.map((company) => (
                            <SelectItem
                              key={company.id}
                              value={company.id.toString()}
                            >
                              {company.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                      <FormMessage />
                    </Select>
                  </FormItem>
                  <div className="flex space-x-4 mt-2">
                    <AddEditInsuranceCompany
                      isEdit={false}
                      trigger={
                        <Button
                          variant="outline"
                          className="border-teal-600 text-teal-600 hover:bg-teal-50"
                          disabled={noInsurance}
                        >
                          ADD
                        </Button>
                      }
                      onSuccess={async (newInsuranceId) => {
                        if (newInsuranceId) {
                          await queryClient.invalidateQueries({
                            queryKey: ['insuranceCompanies']
                          });
                          
                          form.setValue("insuranceCompany", newInsuranceId.toString(), { 
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          setTimeout(() => {
                            form.trigger("insuranceCompany");
                          }, 100);
                        }
                      }}
                    />
                    {field.value && (
                      <AddEditInsuranceCompany
                        isEdit={true}
                        company={field.value}
                        trigger={
                          <Button
                            variant="outline"
                            className="border-teal-600 text-teal-600 hover:bg-teal-50"
                            disabled={noInsurance}
                          >
                            EDIT
                          </Button>
                        }
                      />
                    )}
                  </div>
                </div>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="insured"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Insured</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={noInsurance} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="groupNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Number</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={noInsurance} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="memberNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Member Number</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={noInsurance} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="subrogationCompany"
                render={({ field }) => (
                  <div>
                    <FormItem>
                      <FormLabel>Subrogation Company</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || ""}
                        disabled={noInsurance}
                        onOpenChange={(open) => {
                          if (open) {
                            queryClient.invalidateQueries({ queryKey: ['subrogationCompanies'] });
                            setSubrogationCompaniesLastUpdated(Date.now());
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select company" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {field.value && (
                            <Button
                              variant="ghost"
                              className="mb-2 w-full justify-center text-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                field.onChange("");
                              }}
                            >
                              Clear Selection
                            </Button>
                          )}
                          {isLoadingSubrogationCompanies ? (
                            <SelectItem value="loading">Loading...</SelectItem>
                          ) : (
                            subrogationCompanies?.map((company) => (
                              <SelectItem
                                key={`${company.id}-${subrogationCompaniesLastUpdated}`}
                                value={company.id.toString()}
                              >
                                {company.company}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormItem>
                    <div className="flex space-x-4 mt-2">
                      <AddEditSubrogationCompany
                        isEdit={false}
                        disabled={noInsurance}
                        onSuccess={async (newCompanyId) => {
                          if (newCompanyId) {
                            await queryClient.invalidateQueries({
                              queryKey: ['subrogationCompanies']
                            });
                            
                            // Update timestamp to force re-render of select items
                            setSubrogationCompaniesLastUpdated(Date.now());
                            
                            // Set the form value after a short delay to ensure the data is loaded
                            setTimeout(() => {
                              form.setValue("subrogationCompany", newCompanyId.toString(), { 
                                shouldValidate: true,
                                shouldDirty: true,
                                shouldTouch: true
                              });
                              
                              form.trigger("subrogationCompany");
                            }, 300);
                          }
                        }}
                      />
                      {field.value && (
                        <AddEditSubrogationCompany
                          isEdit={true}
                          id={field.value}
                          disabled={noInsurance}
                        />
                      )}
                    </div>
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="representative"
                render={({ field }) => (
                  <div>
                    <FormItem>
                      <FormLabel>Representative</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || ""}
                        disabled={noInsurance}
                        onOpenChange={(open) => {
                          if (open) {
                            queryClient.invalidateQueries({ queryKey: ['subrogationContacts'] });
                            setSubrogationContactsLastUpdated(Date.now());
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select representative" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {field.value && (
                            <Button
                              variant="ghost"
                              className="mb-2 w-full justify-center text-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                field.onChange("");
                              }}
                            >
                              Clear Selection
                            </Button>
                          )}
                          {isLoadingContacts ? (
                            <SelectItem value="loading">Loading...</SelectItem>
                          ) : (
                            subrogationContacts?.map((contact) => (
                              <SelectItem
                                key={`${contact.id}-${subrogationContactsLastUpdated}`}
                                value={contact.id.toString()}
                              >
                                {contact.first_name} {contact.last_name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormItem>
                    <div className="flex space-x-4 mt-2">
                      <AddEditSubrogationcontact 
                        isEdit={false} 
                        disabled={noInsurance}
                        onSuccess={async (newContactId) => {
                          if (newContactId) {
                            await queryClient.invalidateQueries({
                              queryKey: ['subrogationContacts']
                            });
                            
                            // Update timestamp to force re-render of select items
                            setSubrogationContactsLastUpdated(Date.now());
                            
                            // Set the form value after a short delay to ensure the data is loaded
                            setTimeout(() => {
                              form.setValue("representative", newContactId.toString(), { 
                                shouldValidate: true,
                                shouldDirty: true,
                                shouldTouch: true
                              });
                              
                              form.trigger("representative");
                            }, 300);
                          }
                        }}
                      />
                      {field.value && (
                        <AddEditSubrogationcontact
                          isEdit={true}
                          id={field.value}
                          disabled={noInsurance}
                        />
                      )}
                    </div>
                  </div>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="totalLien"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Lien</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        placeholder="Enter total lien amount"
                        className="bg-white"
                        disabled={noInsurance}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="adjustedLien"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Adjusted Lien</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        placeholder="Enter adjusted lien amount"
                        className="bg-white"
                        disabled={noInsurance}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="final_lien"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          if (checked) {
                            // Set default values when checked
                            form.setValue("final_lien_date", formatDateForApi(new Date().toLocaleDateString()));
                            form.setValue("final_amount", form.getValues("totalLien"));
                          } else {
                            // Clear values when unchecked
                            form.setValue("final_lien_date", "");
                            form.setValue("final_amount", "");
                          }
                        }}
                        disabled={noInsurance}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Final Lien</FormLabel>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {finalLien && (
                <>
                  <FormField
                    control={form.control}
                    name="final_amount"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <RequiredLabel className="text-sm text-gray-500">Final Amount</RequiredLabel>
                        <FormControl>
                          <Input type="number" placeholder="Enter final amount" {...field} disabled={noInsurance} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="final_lien_date"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Final Lien Date</RequiredLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value ? formatDateForInput(field.value) : ""}
                            onChange={(value: string) => {
                              field.onChange(formatDateForApi(value));
                            }}
                            error={!!form.formState.errors.final_lien_date}
                            onError={(message: string) => {
                              form.setError("final_lien_date", {
                                type: "manual",
                                message: message,
                              });
                            }}
                            disabled={noInsurance}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
              
              <FormField
                control={form.control}
                name="fileNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>File Number</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={noInsurance} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="planType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Plan Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                      disabled={noInsurance}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select plan type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {field.value && (
                          <Button
                            variant="ghost"
                            className="mb-2 w-full justify-center text-sm"
                            onClick={(e) => {
                              e.preventDefault();
                              field.onChange("");
                            }}
                          >
                            Clear Selection
                          </Button>
                        )}
                        {PLAN_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="erisa"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ERISA</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={noInsurance} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="medicare"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Medicare</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={noInsurance} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="note"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Note</FormLabel>
                  <FormControl>
                    <div className={cn("bg-white border rounded-md", {
                      "opacity-50 pointer-events-none": noInsurance
                    })}>
                      <RichTextEditor
                        value={field.value || ""}
                        onChange={(value) => {
                          field.onChange(value);
                        }}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

          </div>

          <div className="flex justify-between space-x-2 pt-4 border-t">
            <div></div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                type="button"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                variant="outline"
                type="button"
                onClick={() => setIsSyncDialogOpen(true)}
                disabled={!linkedCases || (linkedCases.direct_cases.length === 0 && linkedCases.indirect_cases.length === 0)}
              >
                Sync Linked Cases
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isEdit ? "Update" : "Create"}
              </Button>
            </div>
          </div>
        </form>
      </Form>

      <SyncCaseDialog
        isOpen={isSyncDialogOpen}
        onClose={() => setIsSyncDialogOpen(false)}
        onSync={handleSync}
        isSyncing={isSyncing}
        caseId={caseId}
        syncType="client-insurance"
      />
    </>
  );
};

HealthInsuranceForm.displayName = "HealthInsuranceForm";

export { formSchema }; 