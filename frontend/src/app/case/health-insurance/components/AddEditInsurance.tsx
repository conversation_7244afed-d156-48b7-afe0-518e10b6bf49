"use client";

import { useState, useMemo, FC, useEffect } from "react";
import {
  Di<PERSON>,
  Di<PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Edit, Plus } from "lucide-react";
import {
  useCreateHealthInsuranceMutation,
  useUpdateHealthInsuranceMutation,
  useHealthInsurancesQuery,
} from "@/services/case-management/healthInsuranceService";
import { useHealthInsuranceSyncMutation } from "@/services/case-management/caseSyncService";
import { HealthInsuranceCreateRequest } from "@/type/case-management/healthInsuranceTypes";
import { HealthInsuranceForm } from "./HealthInsuranceForm";
import { type HealthInsuranceFormValues } from "../types";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";

interface AddEditInsuranceProps {
  isEdit?: boolean;
  caseId: string;
  insuranceId?: string;
  defaultValues?: HealthInsuranceFormValues;
  isHeightNeeded?: boolean;
}

const AddEditInsurance: FC<AddEditInsuranceProps> = ({
  isEdit = false,
  caseId,
  insuranceId,
  defaultValues,
  isHeightNeeded = true,
}) => {
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [formState, setFormState] = useState<HealthInsuranceFormValues | null>(null);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  const { data: insuranceData } = useHealthInsurancesQuery(caseId);
  const { data: linkedCases, isLoading: isLinkedCasesLoading } = useLinkedCasesQuery(caseId);

  // Debug logs for linked cases
  useEffect(() => {
    console.log('Linked Cases in Parent:', linkedCases);
    console.log('Case ID:', caseId);
  }, [linkedCases, caseId]);

  // Get the specific insurance from the list
  const currentInsurance = useMemo(() => {
    if (insuranceData && insuranceId) {
      return insuranceData.find(insurance => insurance.id?.toString() === insuranceId);
    }
    return null;
  }, [insuranceData, insuranceId]);

  const createMutation = useCreateHealthInsuranceMutation(caseId);
  const updateMutation = useUpdateHealthInsuranceMutation(
    caseId,
    insuranceId || ""
  );
  const insuranceSync = useHealthInsuranceSyncMutation(caseId);

  // Form values
  const formValues = useMemo(() => {
    if (isEdit && currentInsurance) {
      return {
        noInsurance: currentInsurance.no_insurance || false,
        insuranceCompany: currentInsurance.insurance_company?.id?.toString(),
        subrogationCompany: currentInsurance.subrogation_company?.id?.toString(),
        insured: currentInsurance.insured || "",
        groupNumber: currentInsurance.group_number || "",
        memberNumber: currentInsurance.member_number || "",
        policyNumber: currentInsurance.policy_number || "",
        representative: currentInsurance.representative?.id?.toString(),
        totalLien: currentInsurance.total_lien || "",
        adjustedLien: currentInsurance.adjusted_lien || "",
        fileNumber: currentInsurance.file_number || "",
        planType: currentInsurance.plan_type || "",
        erisa: currentInsurance.erisa || "",
        medicare: currentInsurance.medicare || "",
        note: currentInsurance.note || "",
        target_cases: currentInsurance.target_cases?.map((id: string) => id.toString()) || [],
        final_lien: currentInsurance.final_lien || false,
        final_lien_date: currentInsurance.final_lien_date || "",
        final_amount: currentInsurance.final_amount || "",
      };
    }
    return defaultValues ? {
      ...defaultValues,
      noInsurance: defaultValues.noInsurance || false,
      target_cases: defaultValues.target_cases?.map((id: string) => id.toString()) || [],
      final_lien: defaultValues.final_lien || false,
      final_lien_date: defaultValues.final_lien_date || "",
      final_amount: defaultValues.final_amount || "",
    } : {
      noInsurance: false,
      insuranceCompany: "",
      subrogationCompany: "",
      insured: "",
      groupNumber: "",
      memberNumber: "",
      policyNumber: "",
      representative: "",
      totalLien: "",
      adjustedLien: "",
      fileNumber: "",
      planType: "",
      erisa: "",
      medicare: "",
      note: "",
      target_cases: [],
      final_lien: false,
      final_lien_date: "",
      final_amount: "",
    };
  }, [isEdit, currentInsurance, defaultValues]);

  const handleSubmit = async (values: HealthInsuranceFormValues) => {
    try {
      if (isLinkedCasesLoading) {
        console.log('Waiting for linked cases to load...');
        return;
      }

      console.log('Linked Cases available:', linkedCases);
      console.log('Submitting form with values:', values);
      const data: HealthInsuranceCreateRequest = {
        no_insurance: values.noInsurance,
        target_cases: values.target_cases || [],
        final_lien: values.final_lien,
        ...(values.final_lien ? {
          final_lien_date: values.final_lien_date,
          final_amount: values.final_amount,
        } : {}),
        ...(values.noInsurance ? {} : {
          insurance_company: values.insuranceCompany
            ? parseInt(values.insuranceCompany)
            : undefined,
          subrogation_company: values.subrogationCompany
            ? parseInt(values.subrogationCompany)
            : undefined,
          insured: values.insured || undefined,
          group_number: values.groupNumber || "",
          member_number: values.memberNumber || "",
          policy_number: values.policyNumber || undefined,
          representative: values.representative
            ? parseInt(values.representative)
            : undefined,
          total_lien: values.totalLien || undefined,
          adjusted_lien: values.adjustedLien || undefined,
          file_number: values.fileNumber || undefined,
          plan_type: values.planType || undefined,
          erisa: values.erisa || undefined,
          medicare: values.medicare || undefined,
          note: values.note || "",
        })
      };

      console.log('Submitting data to API:', data);

      let updatedInsurance;
      if (isEdit && insuranceId) {
        updatedInsurance = await updateMutation.mutateAsync(data);
      } else {
        updatedInsurance = await createMutation.mutateAsync(data);
      }

      handleClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handleSync = async (values: HealthInsuranceFormValues) => {
    try {
      setIsSyncing(true);

      // First create/update the insurance
      const data: HealthInsuranceCreateRequest = {
        no_insurance: values.noInsurance,
        target_cases: values.target_cases || [],
        final_lien: values.final_lien,
        ...(values.final_lien ? {
          final_lien_date: values.final_lien_date,
          final_amount: values.final_amount,
        } : {}),
        ...(values.noInsurance ? {} : {
          insurance_company: values.insuranceCompany
            ? parseInt(values.insuranceCompany)
            : undefined,
          subrogation_company: values.subrogationCompany
            ? parseInt(values.subrogationCompany)
            : undefined,
          insured: values.insured || undefined,
          group_number: values.groupNumber || "",
          member_number: values.memberNumber || "",
          policy_number: values.policyNumber || undefined,
          representative: values.representative
            ? parseInt(values.representative)
            : undefined,
          total_lien: values.totalLien || undefined,
          adjusted_lien: values.adjustedLien || undefined,
          file_number: values.fileNumber || undefined,
          plan_type: values.planType || undefined,
          erisa: values.erisa || undefined,
          medicare: values.medicare || undefined,
          note: values.note || "",
        })
      };

      let updatedInsurance;
      if (isEdit && insuranceId) {
        updatedInsurance = await updateMutation.mutateAsync(data);
      } else {
        updatedInsurance = await createMutation.mutateAsync(data);
      }

      // Then sync with target cases
      if (updatedInsurance?.id && values.target_cases && values.target_cases.length > 0) {
        await insuranceSync.mutateAsync({
          source_insurance_id: parseInt(updatedInsurance.id.toString()),
          target_case_ids: values.target_cases
        });
      }

      handleClose();
      
    } catch (error) {
      console.error("Error syncing insurance:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && formState !== null) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    setFormState(null);
    setIsSyncDialogOpen(false);
  };

  const handleDiscardChanges = () => {
    console.log("Discarding changes");
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleFormStateChange = (state: HealthInsuranceFormValues | null) => {
    if (state && JSON.stringify(state) !== JSON.stringify(formValues)) {
      setFormState(state);
    } else {
      setFormState(null);
    }
  };

  const handleCancelClick = () => {
    const isDirty = formState !== null;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEdit ? (
            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
          ) : (
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4 stroke-[1.5]" />
              Insurance
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {isEdit ? "Edit Insurance" : "Add Insurance"}
            </DialogTitle>
          </DialogHeader>
          <HealthInsuranceForm
            defaultValues={formValues}
            isEdit={isEdit}
            onSubmit={handleSubmit}
            isSubmitting={createMutation.isPending || updateMutation.isPending}
            onCancel={handleCancelClick}
            onFormStateChange={handleFormStateChange}
            linkedCases={linkedCases}
            isHeightNeeded={isHeightNeeded}
            onSync={handleSync}
            caseId={caseId}
          />
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
};

export default AddEditInsurance;
