"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Dialog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON>,
  <PERSON>bs<PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Separator } from "@/components/ui/separator"
import { USStatesLabels } from "@/constants/commont"
import { MultiSelect } from "@/components/ui/multi-select"
import { cn } from "@/lib/utils"
import { useCreateMedicalProviderWithContactMutation, useUpdateMedicalProviderWithContactMutation } from '@/services/case-management/medicalTreatmentService';
import { MedicalProvider } from '@/type/case-management/orgTypes';
import { MedicalProviderWithContactCreateRequest, MedicalProviderUpdateRequest } from "@/type/case-management/medicalTreatmentTypes"
import { useMedicalProviderContactsQuery } from '@/services/orgAPIs';
import { useCreateMedicalProviderContactMutation, useUpdateMedicalProviderContactMutation } from '@/services/orgAPIs';
import { PhoneNumberInput } from "@/components/ui/phone-number-input"
import { SPECIALTIES_OPTIONS } from "@/constants/commont"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import CustomCKEditor from "@/components/ckeditor/CKEditor";


const websiteRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9][-a-zA-Z0-9@:%._+~#=]{0,256}\.[a-z]{2,}([-a-zA-Z0-9@:%_+.~#?&//=]*)?$/i;

// Base schema for billing and records (all fields optional)
const billingAndRecordsSchema = z.object({
  company: z.string().optional(),
  payee: z.string().optional(),
  street1: z.string().optional(),
  street2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional()
    .refine(val => !val || val.length >= 5, "ZIP code must be at least 5 characters if provided"),
  phone: z.string().optional()
    .refine(val => !val || val.length >= 10, "Phone number must be at least 10 digits if provided"),
  ext: z.string().optional(),
  cell: z.string().optional()
    .refine(val => !val || val.length >= 10, "Cell number must be at least 10 digits if provided"),
  email: z.string().optional()
    .refine(val => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), "Invalid email format if provided"),
  fax: z.string().optional()
    .refine(val => !val || val.length >= 10, "Fax number must be at least 10 digits if provided"),
  taxId: z.string().optional(),
  website: z.string()
    .refine((val) => !val || websiteRegex.test(val), {
        message: "Please enter a valid website URL (e.g., example.com or https://example.com)"
    })
    .optional()
    .or(z.literal("")),
})

// Office schema (company and city required)
const officeSchema = billingAndRecordsSchema.extend({
  company: z.string().min(1, "Company is required"),
  city: z.string().min(1, "City is required"),
})


const providerFormSchema = z.object({
  specialties: z.array(z.string()).min(1, "At least one specialty is required"),
  acceptsLiens: z.string().optional().default('no'),
  noRecords: z.boolean().default(false),
  doNotUse: z.boolean().default(false),
  note: z.string().optional(),
  office: officeSchema,
  billing: billingAndRecordsSchema.optional(),
  records: billingAndRecordsSchema.optional(),
})

type ProviderFormValues = z.infer<typeof providerFormSchema>

type AddEditProviderProps = {
  isEdit?: boolean;
  provider?: MedicalProvider;
  onClose?: () => void;
}

export default function AddEditProvider({ 
  isEdit = false, 
  provider,
  onClose,
}: AddEditProviderProps) {
  const [open, setOpen] = useState(false)
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false)
  const [formState, setFormState] = useState<ProviderFormValues | null>(null)
  const createProviderMutation = useCreateMedicalProviderWithContactMutation()
  const updateProviderMutation = useUpdateMedicalProviderWithContactMutation(provider?.id?.toString() ?? '')
  const createContactMutation = useCreateMedicalProviderContactMutation(provider?.id?.toString() ?? '');
  const updateContactMutation = useUpdateMedicalProviderContactMutation(provider?.id?.toString() ?? '');

  useEffect(() => {
    if (!open) {
      onClose?.();
    }
  }, [open, onClose]);
  // Fetch provider contacts when in edit mode
  const { data: providerContacts } = useMedicalProviderContactsQuery(
    provider?.id?.toString() ?? '', 
    { enabled: isEdit && !!provider?.id }
  );

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && formState !== null) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  }

  const handleClose = () => {
    setOpen(false);
    setFormState(null);
    form.reset();
  }

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  }

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  }

  const handleTriggerClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setOpen(true)
  }

  // Modify the form schema based on isEdit
  const form = useForm<ProviderFormValues>({
    resolver: zodResolver(providerFormSchema),
    defaultValues: {
      specialties: [],
      acceptsLiens: "no",
      noRecords: false,
      doNotUse: false,
      note: "",
      office: {
        company: "",
        payee: "",
        street1: "",
        street2: "",
        city: "",
        state: "",
        zip: "",
        phone: "",
        ext: "",
        cell: "",
        email: "",
        fax: "",
        taxId: "",
        website: "",
      },
      // Only include billing and records in edit mode
      ...(isEdit && {
        billing: {
          company: "",
          payee: "",
          street1: "",
          street2: "",
          city: "",
          state: "",
          zip: "",
          phone: "",
          ext: "",
          cell: "",
          email: "",
          fax: "",
          taxId: "",
          website: "",
        },
        records: {
          company: "",
          payee: "",
          street1: "",
          street2: "",
          city: "",
          state: "",
          zip: "",
          phone: "",
          ext: "",
          cell: "",
          email: "",
          fax: "",
          taxId: "",
          website: "",
        },
      }),
    },
  })

  // Function to render the office form content
  const renderOfficeContent = (officeId: 'office' | 'billing' | 'records') => {
    // Helper function to get the label based on officeId
    const getCompanyLabel = (id: 'office' | 'billing' | 'records') => {
      switch(id) {
        case 'office':
          return 'Company *'
        case 'billing':
          return 'Billing Office'
        case 'records':
          return 'Records Office'
        default:
          return 'Company'
      }
    }

    // Helper function to get city label based on officeId
    const getCityLabel = (id: 'office' | 'billing' | 'records') => {
      return id === 'office' ? 'City *' : 'City'
    }

    // console.log("form values ::::  ", form.getValues())

    return (
      <div className="space-y-4">
        <FormField
          control={form.control}
          name={`${officeId}.company`}
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel className="text-sm text-[#060216]/50">{getCompanyLabel(officeId)}</FormLabel>
              <FormControl>
                <Input 
                  {...field}
                  value={(field.value ?? '') as string}
                  className={cn(
                    "text-[#060216]",
                    fieldState.error && "border-red-500"
                  )}
                />
              </FormControl>
              {fieldState.error && (
                <div className="text-sm text-red-500 mt-1">
                  {fieldState.error.message}
                </div>
              )}
            </FormItem>
          )}
        />

        {/* Two Column Layout */}
        <div className="grid grid-cols-2 gap-4">
          {/* Left Column */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name={`${officeId}.payee`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">payee</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.street1`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">street 1</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.street2`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">street 2</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.city`}
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">{getCityLabel(officeId)}</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      value={(field.value ?? '') as string}
                      className={cn(
                        "text-[#060216]",
                        fieldState.error && "border-red-500"
                      )}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <div className="text-sm text-red-500 mt-1">
                      {fieldState.error.message}
                    </div>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.state`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">state</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger className="text-[#060216]">
                        <SelectValue placeholder="Select a state" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {field.value && (
                        <Button
                          variant="ghost"
                          className="mb-2 w-full justify-center text-sm"
                          onClick={(e) => {
                            e.preventDefault();
                            field.onChange("");
                          }}
                        >
                          Clear Selection
                        </Button>
                      )}
                      {Object.entries(USStatesLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.zip`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">zip code</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name={`${officeId}.phone`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">phone</FormLabel>
                  <FormControl>
                    <PhoneNumberInput {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.ext`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">ext</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.cell`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">cell</FormLabel>
                  <FormControl>
                    <PhoneNumberInput {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.email`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">email</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.fax`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">fax</FormLabel>
                  <FormControl>
                    <PhoneNumberInput {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.taxId`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">tax id</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`${officeId}.website`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-[#060216]/50">website</FormLabel>
                  <FormControl>
                    <Input {...field} value={(field.value ?? '') as string} className="text-[#060216]" />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    )
  }

  async function handleSubmit(data: ProviderFormValues) {
    try {
      if (isEdit && provider?.id) {
        // Handle update for provider
        const updateData: MedicalProviderUpdateRequest = {
          company: data.office.company,
          specialties: data.specialties.join(', '),
          accepts_liens: data.acceptsLiens === 'yes',
          is_active: true,
          no_records_service: data.noRecords,
          do_not_use: data.doNotUse,
          note: data.note || ""
        };

        // Update provider first
        await updateProviderMutation.mutateAsync(updateData);

        // Handle contacts
        if (providerContacts) {
          // Process each contact type
          const contactTypes = ['OFFICE', 'BILLING', 'RECORDS'] as const;
          const formDataMap = {
            OFFICE: data.office,
            BILLING: data.billing,
            RECORDS: data.records
          };

          for (const contactType of contactTypes) {
            const formData = formDataMap[contactType];
            if (!formData) continue;

            const existingContact = providerContacts.find(c => c.contact_type === contactType);
            const contactData = {
              name: formData.company || null,
              medical_provider: provider.id,
              contact_type: contactType,
              payee: formData.payee || null,
              phone: formData.phone || null,
              phone_ext: formData.ext || null,
              cell: formData.cell || null,
              fax: formData.fax || null,
              email: formData.email || null,
              website: formData.website || null,
              street1: formData.street1 || null,
              street2: formData.street2 || null,
              city: formData.city || null,
              state: formData.state || null,
              zip_code: formData.zip || null,
              tax_id: formData.taxId || null,
              note: data.note || null
            };

            if (existingContact) {
              // Update existing contact using the component-level hook
              await updateContactMutation.mutateAsync({
                id: existingContact.id,
                ...contactData
              });
            } else {
              // Create new contact using the component-level hook
              await createContactMutation.mutateAsync(contactData);
            }
          }
        }
      } else {
        // Handle create (existing code)
        const requestData: MedicalProviderWithContactCreateRequest = {
          provider: {
            company: data.office.company,
            specialties: data.specialties.join(', '),
            accepts_liens: data.acceptsLiens === 'yes',
            is_active: true,
            no_records_service: data.noRecords,
            do_not_use: data.doNotUse,
            note: data.note || ""
          },
          contact: {
            name: data.office.company,
            contact_type: 'OFFICE',
            payee: data.office.payee || null,
            phone: data.office.phone || null,
            phone_ext: data.office.ext || null,
            cell: data.office.cell || null,
            fax: data.office.fax || null,
            email: data.office.email || null,
            website: data.office.website || null,
            street1: data.office.street1 || null,
            street2: data.office.street2 || null,
            city: data.office.city || null,
            state: data.office.state || null,
            zip_code: data.office.zip || null,
            tax_id: data.office.taxId || null,
            note: data.note || null
          }
        };

        await createProviderMutation.mutateAsync(requestData);
      }
      setOpen(false); // Close dialog on success
      form.reset(); // Reset form
    } catch (error) {
      console.error('Failed to save provider:', error);
    } finally {
      setOpen(false); // Close dialog on success
      form.reset(); // Reset form
    }
  }

  useEffect(() => {
    if (isEdit && provider) {
      // Base provider data
      const formData: ProviderFormValues = {
        specialties: provider.specialties?.split(', ') || [],
        acceptsLiens: provider.accepts_liens ? 'yes' : 'no',
        noRecords: provider.no_records_service || false,
        doNotUse: provider.do_not_use || false,
        note: provider.note ||"",
        office: {
          company: '',
          payee: '',  // Will be updated from contacts
          street1: '',
          street2: '',
          city: '',
          state: '',
          zip: '',
          phone: '',
          ext: '',
          cell: '',
          email: '',
          fax: '',
          taxId: '',
          website: '',
        }
      };

      // If we have contacts, populate the billing and records data
      if (providerContacts) {
        // Find contacts by type
        const office = providerContacts.find(c => c.contact_type === "OFFICE");
        const billingContact = providerContacts.find(c => c.contact_type === "BILLING");
        const recordsContact = providerContacts.find(c => c.contact_type === "RECORDS");

        // Update office payee from OFFICE contact
        if (office) {
          formData.office = {
            company: office.name || provider.company || '',
            payee: office.payee || '',
            street1: office.street1 || '',
            street2: office.street2 || '',
            city: office.city || '',
            state: office.state || '',
            zip: office.zip_code || '',
            phone: office.phone || '',
            ext: office.phone_ext || '',
            cell: office.cell || '',
            email: office.email || '',
            fax: office.fax || '',
            taxId: office.tax_id || '',
            website: office.website || '',
          };
        }

        // Add billing contact data if exists
        if (billingContact) {
          formData.billing = {
            company: billingContact.name || '',
            payee: billingContact.payee || '',
            street1: billingContact.street1 || '',
            street2: billingContact.street2 || '',
            city: billingContact.city || '',
            state: billingContact.state || '',
            zip: billingContact.zip_code || '',
            phone: billingContact.phone || '',
            ext: billingContact.phone_ext || '',
            cell: billingContact.cell || '',
            email: billingContact.email || '',
            fax: billingContact.fax || '',
            taxId: billingContact.tax_id || '',
            website: billingContact.website || '',
          };
        }

        // Add records contact data if exists
        if (recordsContact) {
          formData.records = {
            company: recordsContact.name || '',
            payee: recordsContact.payee || '',
            street1: recordsContact.street1 || '',
            street2: recordsContact.street2 || '',
            city: recordsContact.city || '',
            state: recordsContact.state || '',
            zip: recordsContact.zip_code || '',
            phone: recordsContact.phone || '',
            ext: recordsContact.phone_ext || '',
            cell: recordsContact.cell || '',
            email: recordsContact.email || '',
            fax: recordsContact.fax || '',
            taxId: recordsContact.tax_id || '',
            website: recordsContact.website || '',
          };
        }
      }

      // Reset form with the complete data
      form.reset(formData);
    }
  }, [isEdit, provider, providerContacts, form]);

  // Add form state watcher
  useEffect(() => {
    const subscription = form.watch((value) => {
      const hasChanges = JSON.stringify(value) !== JSON.stringify({
        specialties: provider?.specialties?.split(', ') || [],
        acceptsLiens: provider?.accepts_liens ? 'yes' : 'no',
        noRecords: provider?.no_records_service || false,
        doNotUse: provider?.do_not_use || false,
        note: provider?.note || '',
        office: provider?.contacts?.[0] || {},
        billing: provider?.contacts?.[1] || {},
        records: provider?.contacts?.[2] || {},
      });
      
      if (hasChanges) {
        setFormState(value as ProviderFormValues);
      } else {
        setFormState(null);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, provider]);

  // Add the handler function
  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
        setShowUnsavedAlert(true);
    } else {
        handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <Button 
            variant={isEdit ? "link" : "default"} 
            className={isEdit ? "text-green-600" : ""}
            onClick={handleTriggerClick}
          >
            {isEdit ? "Edit" : "Add New Provider"}
          </Button>
        </DialogTrigger>
        <DialogContent 
          className="max-w-6xl"
          onPointerDownOutside={(e) => {
            e.preventDefault()
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <DialogHeader>
            <DialogTitle>
              {isEdit ? "Edit Provider Contact" : "Add Provider Contact"}
            </DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <div className="max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {/* Office Tabs */}
                <Tabs defaultValue="office" className="w-full">
                  <TabsList className={`grid w-full ${isEdit ? 'grid-cols-3' : 'grid-cols-1'}`}>
                    <TabsTrigger value="office">OFFICE</TabsTrigger>
                    {isEdit && (
                      <>
                        <TabsTrigger value="billing">BILLING</TabsTrigger>
                        <TabsTrigger value="records">RECORDS</TabsTrigger>
                      </>
                    )}
                  </TabsList>

                  <div className="mt-4">
                    <TabsContent value="office">
                      {renderOfficeContent('office')}
                    </TabsContent>

                    {isEdit && (
                      <>
                        <TabsContent value="billing">
                          {renderOfficeContent('billing')}
                        </TabsContent>

                        <TabsContent value="records">
                          {renderOfficeContent('records')}
                        </TabsContent>
                      </>
                    )}
                  </div>
                </Tabs>

                <Separator className="my-4" />

                {/* Common Fields */}
                <div className="mt-4 mb-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="specialties"
                        render={({ field }) => (
                          <FormItem onClick={(e) => e.stopPropagation()}>
                            <FormLabel>Specialties *</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={SPECIALTIES_OPTIONS}
                                defaultValue={field.value}
                                onValueChange={field.onChange}
                                placeholder="Select specialties"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="acceptsLiens"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Accepts liens / LOPs</FormLabel>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="--" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="yes">Yes</SelectItem>
                                <SelectItem value="no">No</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex items-center gap-4">
                      <FormField
                        control={form.control}
                        name="noRecords"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel>No records service</FormLabel>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="doNotUse"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel>Do not use</FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="note"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Treatment Notes</FormLabel>
                          <FormControl>
                            <CustomCKEditor
                              initialValue={field.value ||""}
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter treatment notes..."
                              className="min-h-[100px] mb-4 resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelClick}
                >
                    Cancel
                </Button>
                <Button type="button" onClick={() => form.handleSubmit(handleSubmit)()}>
                    {isEdit ? "Update" : "Create"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  )
}
