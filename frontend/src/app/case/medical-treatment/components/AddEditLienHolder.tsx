"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Edit, Plus, Building2, MapPin, Phone, Mail } from "lucide-react"
import { useState, useEffect } from "react"
import { useCreateLienHolderMutation, useUpdateLienHolderMutation, useLienHolderQuery } from "@/services/orgAPIs"
import { LienHolderCreateRequest } from "@/type/case-management/orgTypes"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { PhoneNumberInput } from "@/components/ui/phone-number-input"
import { USStatesLabels } from "@/constants/commont"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import CustomCKEditor from "@/components/ckeditor/CKEditor"

// Define the form validation schema
const formSchema = z.object({
    company: z.string().min(1, "Company name is required"),
    first_name: z.string().min(1, "First name is required"),
    last_name: z.string().min(1, "Last name is required"),
    payee: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email("Invalid email address").optional().or(z.literal("")),
    street1: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip_code: z.string().optional(),
    note: z.string().optional(),
})

interface AddEditLienHolderProps {
    isEdit?: boolean;
    lienHolderId?: string;
    initialData?: LienHolderCreateRequest;
    onSuccess?: (newLienHolderId?: number) => void;
}

export function AddEditLienHolder({ isEdit = false, lienHolderId, initialData, onSuccess }: AddEditLienHolderProps) {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            company: initialData?.company || "",
            payee: initialData?.payee || "",
            first_name: initialData?.first_name || "",
            last_name: initialData?.last_name || "",
            phone: initialData?.phone || "",
            email: initialData?.email || "",
            street1: initialData?.street1 || "",
            city: initialData?.city || "",
            state: initialData?.state || "",
            zip_code: initialData?.zip_code || "",
            note: initialData?.note || "",
        },
    })

    const createMutation = useCreateLienHolderMutation();
    const updateMutation = useUpdateLienHolderMutation(lienHolderId || "");
    const { data: lienHolder } = useLienHolderQuery(isEdit && lienHolderId ? lienHolderId : "");

    const handleClose = () => {
        form.reset();
        setOpen(false);
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(true);
            }
        }
    };

    // Update form data when lien holder data is fetched
    useEffect(() => {
        if (lienHolder) {
            form.reset({
                company: lienHolder.company || "",
                payee: lienHolder.payee || "",
                first_name: lienHolder.first_name || "",
                last_name: lienHolder.last_name || "",
                phone: lienHolder.phone || "",
                email: lienHolder.email || "",
                street1: lienHolder.street1 || "",
                city: lienHolder.city || "",
                state: lienHolder.state || "",
                zip_code: lienHolder.zip_code || "",
                note: lienHolder.note || ""
            });
        }
    }, [lienHolder, form]);

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            let response;
            if (isEdit && lienHolderId) {
                response = await updateMutation.mutateAsync(values);
                // For edit operations, call onSuccess and close immediately
                onSuccess?.();
                handleClose();
            } else {
                response = await createMutation.mutateAsync(values);
                
                // For create operations, call onSuccess with ID and delay closing
                if (response && response.id) {
                    // Call onSuccess callback but don't reset form or close dialog immediately
                    // This allows the parent to update the select value before the dialog closes
                    onSuccess?.(response.id);
                    
                    // Close the dialog after a longer delay to ensure parent component has time 
                    // to process the selection and refresh the lien holders list
                    setTimeout(() => {
                        form.reset();
                        setOpen(false);
                    }, 300);
                } else {
                    // If no ID, close immediately
                    handleClose();
                }
            }
        } catch (error) {
            console.error("Failed to save lien holder:", error);
        }
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setOpen(true);
                }}>
                    {isEdit ? (
                        <Button
                            variant="ghost"
                            size="icon"
                            type="button"
                        >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                        </Button>
                    ) : (
                        <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="border-[#060216] text-[#060216] hover:bg-[#060216]/5"
                        >
                            <Plus className="w-4 h-4 mr-2" />
                            Lien Holder
                        </Button>
                    )}
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
                            {isEdit ? "Edit Lien Holder" : "Add Lien Holder"}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <div className="overflow-y-auto max-h-[70vh] pr-6 space-y-4 scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                                <FormField
                                    control={form.control}
                                    name="company"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-sm text-[#060216]/50">
                                                <Building2 className="w-4 h-4 inline-block mr-2" />
                                                company name <span className="text-red-500">*</span>
                                            </FormLabel>
                                            <FormControl>
                                                <Input {...field} className="text-[#060216]" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="grid grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="first_name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">first name <span className="text-red-500">*</span></FormLabel>
                                                <FormControl>
                                                    <Input {...field} className="text-[#060216]" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="last_name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">last name <span className="text-red-500">*</span></FormLabel>
                                                <FormControl>
                                                    <Input {...field} className="text-[#060216]" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="payee"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-sm text-[#060216]/50">payee</FormLabel>
                                            <FormControl>
                                                <Input {...field} className="text-[#060216]" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="grid grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="phone"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">
                                                    <Phone className="w-4 h-4 inline-block mr-2" />
                                                    phone
                                                </FormLabel>
                                                <FormControl>
                                                    <PhoneNumberInput
                                                        {...field}
                                                        value={field.value || ''}
                                                        placeholder="************"
                                                        className="text-[#060216]"
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">
                                                    <Mail className="w-4 h-4 inline-block mr-2" />
                                                    email
                                                </FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="email" className="text-[#060216]" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="street1"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-sm text-[#060216]/50">
                                                <MapPin className="w-4 h-4 inline-block mr-2" />
                                                street address
                                            </FormLabel>
                                            <FormControl>
                                                <Input {...field} className="text-[#060216]" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="grid grid-cols-3 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="city"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">city</FormLabel>
                                                <FormControl>
                                                    <Input {...field} className="text-[#060216]" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="state"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">state</FormLabel>
                                                <FormControl>
                                                    <Select 
                                                        value={field.value || ""} 
                                                        onValueChange={field.onChange}
                                                    >
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="State" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {field.value && (
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    className="mb-2 w-full justify-center text-sm"
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        field.onChange("");
                                                                    }}
                                                                >
                                                                    Clear Selection
                                                                </Button>
                                                            )}
                                                            {Object.entries(USStatesLabels).map(([value, label]) => (
                                                                <SelectItem key={value} value={value}>
                                                                    {label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="zip_code"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm text-[#060216]/50">zip code</FormLabel>
                                                <FormControl>
                                                    <Input {...field} className="text-[#060216]" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="note"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Note</FormLabel>
                                            <FormControl>
                                                <div className="bg-white border rounded-md">
                                                    <CustomCKEditor
                                                        initialValue={field.value || ""}
                                                        onChange={(value) => field.onChange(value)}
                                                        minHeight="100px"
                                                        placeholder="Enter note"
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="flex justify-end gap-2 pt-4 border-t mt-4">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancelClick}
                                    className="border-[#060216] text-[#060216] hover:bg-[#060216]/5"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="button"
                                    onClick={(e) => {
                                        form.handleSubmit((values) => onSubmit(values))(e);
                                    }}
                                    className="bg-green-600 hover:bg-green-700 text-white"
                                    disabled={!form.formState.isValid || form.formState.isSubmitting}
                                >
                                    Save
                                </Button>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    );
} 