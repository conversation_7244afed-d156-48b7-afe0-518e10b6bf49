import React, { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Edit,
  Stethoscope,
  FileText,
  DollarSign,
  ClipboardList,
  Building2,
  Phone,
  Globe,
  MapPin,
  Calculator,
} from "lucide-react";
import { useLienHoldersQuery } from "@/services/orgAPIs";
import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import {
  useCreateTreatmentProviderMutation,
  useUpdateTreatmentProviderMutation,
} from "@/services/case-management/medicalTreatmentService";
import {
  TreatmentStatus,
  TreatmentProviderCreateRequest,
  TreatmentProvider,
} from "@/type/case-management/medicalTreatmentTypes";
import { AddEditLienHolder } from "./AddEditLienHolder";
import { MedicalProvider } from "@/type/case-management/orgTypes";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import AddEditProvider from "./AddEditProvider";
import { useQueryClient } from "@tanstack/react-query";
import {
  formatDateForInput,
  formatDateForApi,
  validateDate,
} from "@/utils/dateUtils";
import CustomCKEditor from "@/components/ckeditor/CKEditor";
import RichTextEditor from "@/components/ui/RichTextEditor";
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog";
import { useTreatmentProviderSyncMutation } from "@/services/case-management/caseSyncService";

const treatmentFormSchema = z.object({
  medical_provider: z.number().optional(),
  lien_holder: z.string().optional(),
  treatment_status: z.nativeEnum(TreatmentStatus, {
    required_error: "Treatment status is required",
  }),
  treatment_description: z.string().optional(),
  first_visit: z.string().optional().nullable(),
  last_visit: z.string().optional().nullable(),
  number_of_visits: z.string().optional(),
  original_bill: z.string().optional(),
  adjusted_bill: z.string().optional(),
  insurance_paid: z.string().optional(),
  medpay_pip_paid: z.string().optional(),
  client_paid: z.string().optional(),
  still_owed: z.string().optional(),
  paid_plus_owed: z.string().optional(),
  account_number: z.string().optional(),
  signed_lien: z.boolean().optional(),
  filed_lien: z.boolean().optional(),
  target_cases: z.array(z.string()).optional(),
  // records_status: z.nativeEnum(RecordStatus, {
  //   required_error: "Records status is required",
  // }),
  // records_date: z.string().optional().nullable(),
  // billing_status: z.nativeEnum(RecordStatus, {
  //   required_error: "Billing status is required",
  // }),
  // billing_date: z.string().optional().nullable(),
}).refine((data) => {
  if (!data.first_visit || !data.last_visit) return true;
  return new Date(data.last_visit) >= new Date(data.first_visit);
}, {
  message: "Last visit cannot be before first visit",
  path: ["last_visit"], // This will show the error on the last_visit field
});

interface AddEditTreatmentProps {
  caseId: string;
  treatmentId?: string;
  isEdit: boolean;
  provider: MedicalProvider;
  treatment?: TreatmentProvider;
  onClose?: () => void;
}

// Helper function to create treatment data from form values
const createTreatmentData = (values: z.infer<typeof treatmentFormSchema>, providerId?: number, targetCases?: string[]): TreatmentProviderCreateRequest => {
  return {
    medical_provider: providerId,
    lien_holder: values.lien_holder ? parseInt(values.lien_holder) : undefined,
    treatment_status: values.treatment_status,
    treatment_description: values.treatment_description,
    first_visit: values.first_visit || null,
    last_visit: values.last_visit || null,
    number_of_visits: values.number_of_visits
      ? parseInt(values.number_of_visits)
      : undefined,
    original_bill: values.original_bill,
    adjusted_bill: values.adjusted_bill,
    insurance_paid: values.insurance_paid,
    medpay_pip_paid: values.medpay_pip_paid,
    client_paid: values.client_paid,
    still_owed: values.still_owed,
    paid_plus_owed: values.paid_plus_owed,
    account_number: values.account_number,
    signed_lien: values.signed_lien,
    filed_lien: values.filed_lien,
    target_cases: targetCases || values.target_cases || [],
  };
};

// Helper function to get form default values from treatment data
const getFormDefaultValues = (treatment: TreatmentProvider | undefined, provider: MedicalProvider) => {
  return {
    medical_provider: provider?.id,
    lien_holder: treatment?.lien_holder
      ? (typeof treatment.lien_holder === 'object'
        ? treatment.lien_holder.id.toString()
        : treatment.lien_holder.toString())
      : undefined,
    treatment_status: treatment?.treatment_status || TreatmentStatus.TREATING,
    treatment_description: treatment?.treatment_description || "",
    first_visit: treatment?.first_visit || null,
    last_visit: treatment?.last_visit || null,
    number_of_visits: treatment?.number_of_visits?.toString() || "",
    original_bill: treatment?.original_bill?.toString() || "",
    adjusted_bill: treatment?.adjusted_bill?.toString() || "",
    insurance_paid: treatment?.insurance_paid?.toString() || "",
    medpay_pip_paid: treatment?.medpay_pip_paid?.toString() || "",
    client_paid: treatment?.client_paid?.toString() || "",
    still_owed: treatment?.still_owed?.toString() || "",
    paid_plus_owed: treatment?.paid_plus_owed?.toString() || "",
    account_number: treatment?.account_number || "",
    signed_lien: treatment?.signed_lien || false,
    filed_lien: treatment?.filed_lien || false,
    target_cases: treatment?.target_cases || []
  };
};

// Helper function to get treatment ID safely
const getTreatmentId = (treatment: TreatmentProvider) => {
  return typeof treatment.id === 'object' ? treatment.id.id : treatment.id;
};

export function AddEditTreatment({
  caseId,
  treatmentId,
  isEdit,
  treatment,
  provider,
}: AddEditTreatmentProps) {
  const [open, setOpen] = useState(false);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [providerDetails, setProviderDetails] = useState(provider);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [isStillOwedManuallyEdited, setIsStillOwedManuallyEdited] = useState(false);
  const [lienHoldersLastUpdated, setLienHoldersLastUpdated] = useState(Date.now());
  const [pendingLienHolderId, setPendingLienHolderId] = useState<string | null>(null);

  const { data: lienHolders = [] } = useLienHoldersQuery();
  const { data: linkedCases } = useLinkedCasesQuery(caseId);
  const createTreatment = useCreateTreatmentProviderMutation(caseId);
  const updateTreatment = useUpdateTreatmentProviderMutation(
    caseId,
    treatmentId || ""
  );
  const queryClient = useQueryClient();
  const treatmentSync = useTreatmentProviderSyncMutation(caseId);
  const form = useForm<z.infer<typeof treatmentFormSchema>>({
    resolver: zodResolver(treatmentFormSchema),
    defaultValues: getFormDefaultValues(treatment, provider),
  });

  useEffect(() => {
    if (provider) {
      form.setValue("medical_provider", provider.id);
      setProviderDetails(provider);
    }
  }, [provider, form]);

  // Function to calculate still owed amount
  const calculateStillOwed = useCallback(() => {
    if (isStillOwedManuallyEdited) return;

    const originalBill = parseFloat(form.getValues("original_bill") || "0");
    const adjustedBill = parseFloat(form.getValues("adjusted_bill") || "0");
    const writeOff = parseFloat(form.getValues("account_number") || "0");
    const insurancePaid = parseFloat(form.getValues("insurance_paid") || "0");
    const medpayPipPaid = parseFloat(form.getValues("medpay_pip_paid") || "0");
    const clientPaid = parseFloat(form.getValues("client_paid") || "0");

    const stillOwed = Math.max(0, originalBill - adjustedBill - writeOff - insurancePaid - medpayPipPaid - clientPaid).toFixed(2);
    form.setValue("still_owed", stillOwed);
  }, [form, isStillOwedManuallyEdited]);

  // Watch for changes in fields that affect still_owed calculation
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (["original_bill", "adjusted_bill", "account_number", "insurance_paid", "medpay_pip_paid", "client_paid"].includes(name || "")) {
        calculateStillOwed();
      }
    });
    return () => subscription.unsubscribe();
  }, [form, calculateStillOwed]);

  // Calculate still owed on initial render
  useEffect(() => {
    calculateStillOwed();
  }, [calculateStillOwed]);

  // Watch the lienHolders data and apply pending lien holder when it changes
  useEffect(() => {
    if (pendingLienHolderId && lienHolders.length > 0) {
      // Find if the pending lien holder exists in the updated list
      const exists = lienHolders.some(holder => holder.id.toString() === pendingLienHolderId);

      if (exists) {
        // Apply the pending lien holder
        form.setValue("lien_holder", pendingLienHolderId, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true
        });

        // Clear the pending ID
        setPendingLienHolderId(null);

        // Trigger validation to update UI
        form.trigger("lien_holder");
      }
    }
  }, [lienHolders, pendingLienHolderId, form]);

  const handleClose = () => {
    form.reset();
    setIsStillOwedManuallyEdited(false);
    setOpen(false);
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      const isDirty = form.formState.isDirty;
      if (isDirty) {
        setShowUnsavedAlert(true);
      } else {
        handleClose();
      }
    } else {
      setOpen(true);
    }
  };

  const refreshLienHolders = useCallback(() => {
    // Invalidate the query cache to force a fresh fetch
    queryClient.invalidateQueries({ queryKey: ["lienHolders"] });
    // Update timestamp to trigger re-render of select items
    setLienHoldersLastUpdated(Date.now());
  }, [queryClient]);

  async function onSubmit(values: z.infer<typeof treatmentFormSchema>) {
    try {
      const treatmentData = createTreatmentData(values, provider?.id);

      let updatedTreatment;
      if (isEdit) {
        updatedTreatment = await updateTreatment.mutateAsync(treatmentData);
      } else {
        updatedTreatment = await createTreatment.mutateAsync(treatmentData);
      }

      form.reset(getFormDefaultValues(updatedTreatment, provider));
      setIsStillOwedManuallyEdited(false);
      setOpen(false);
    } catch (error) {
      console.error("Failed to save treatment:", error);
    }
  }

  const handleSync = async (selectedCaseIds: string[]) => {
    try {
      setIsSyncing(true);

      const values = form.getValues();
      const treatmentData = createTreatmentData(values, provider?.id, selectedCaseIds);

      let updatedTreatment;
      if (isEdit && treatment?.id) {
        updatedTreatment = await updateTreatment.mutateAsync(treatmentData);
      } else {
        updatedTreatment = await createTreatment.mutateAsync(treatmentData);
      }

      const treatmentId = getTreatmentId(updatedTreatment);
      await treatmentSync.mutateAsync({
        treatment_provider_id: treatmentId,
        target_case_ids: selectedCaseIds
      });

      setIsSyncDialogOpen(false);
      setOpen(false);
      form.reset(getFormDefaultValues(updatedTreatment, provider));

    } catch (error) {
      console.error("Failed to sync treatment:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEdit ? (
            <Button
              variant="link"
              onClick={(e) => {
                e.stopPropagation();
                setOpen(true);
              }}
              className="p-0"
            >
              <Edit className="h-4 w-4 text-green-600" />
            </Button>
          ) : (
            <Button
              variant="link"
              onClick={(e) => {
                e.stopPropagation();
                setOpen(true);
              }}
              className="p-0 text-green-600"
            >
              {/* <Plus className="h-4 w-4 text-green-600" /> */}
              {/* Add */}
              Select
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
              {isEdit ? "Edit Treatment" : "Add Treatment"}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="overflow-y-auto max-h-[70vh] pr-6 scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                <div className="space-y-2 mb-5">
                  <div className="flex items-center justify-between">
                    {providerDetails?.company && (
                      <h3 className="text-[22px] font-bold font-Manrope text-[#060216]">
                        {providerDetails.company}
                      </h3>
                    )}
                    <AddEditProvider
                      isEdit={true}
                      provider={providerDetails}
                      onClose={() =>
                        queryClient.invalidateQueries({
                          queryKey: ["treatmentProviders", caseId],
                        })
                      }
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-sm text-[#060216]/50 space-y-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-[#060216]">
                          <Building2 className="w-4 h-4 inline-block mr-2" />
                          provider details
                        </h4>
                        <AddEditProvider
                          isEdit={true}
                          provider={providerDetails}
                          onClose={() =>
                            queryClient.invalidateQueries({
                              queryKey: ["treatmentProviders", caseId],
                            })
                          }
                        />
                      </div>
                      {providerDetails?.street1 && (
                        <p>
                          <MapPin className="w-4 h-4 inline-block mr-2" />
                          {providerDetails.street1}
                        </p>
                      )}
                      {(providerDetails?.city ||
                        providerDetails?.state ||
                        providerDetails?.zip_code) && (
                          <p>
                            {[
                              providerDetails.city,
                              providerDetails.state,
                              providerDetails.zip_code,
                            ]
                              .filter(Boolean)
                              .join(", ")}
                          </p>
                        )}
                      {providerDetails?.phone && (
                        <p>
                          <Phone className="w-4 h-4 inline-block mr-2" />
                          {providerDetails.phone}
                        </p>
                      )}
                      {providerDetails?.website && (
                        <a
                          href={providerDetails.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-2"
                        >
                          <Globe className="w-4 h-4" />
                          {providerDetails.website}
                        </a>
                      )}
                      {providerDetails?.contacts &&
                        providerDetails.contacts.length > 0 && (
                          <div className="text-sm text-[#060216]/50 space-y-1">
                            <h4 className="font-medium text-[#060216]">
                              contact details
                            </h4>
                            {providerDetails.contacts[0]?.street1 && (
                              <p>{providerDetails.contacts[0].street1}</p>
                            )}
                            {(providerDetails.contacts[0]?.city ||
                              providerDetails.contacts[0]?.state ||
                              providerDetails.contacts[0]?.zip_code) && (
                                <p>
                                  {[
                                    providerDetails.contacts[0].city,
                                    providerDetails.contacts[0].state,
                                    providerDetails.contacts[0].zip_code,
                                  ]
                                    .filter(Boolean)
                                    .join(", ")}
                                </p>
                              )}
                            {providerDetails.contacts[0]?.phone && (
                              <p>{providerDetails.contacts[0].phone}</p>
                            )}
                            {providerDetails.contacts[0]?.website && (
                              <a
                                href={providerDetails.contacts[0].website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-[#060216]-600 hover:underline"
                              >
                                website
                              </a>
                            )}
                          </div>
                        )}
                    </div>

                    <FormField
                      control={form.control}
                      name="lien_holder"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm text-[#060216]/50">
                            <FileText className="w-4 h-4 inline-block mr-2" />
                            lien holder
                          </FormLabel>
                          <div className="space-y-4">
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                              onOpenChange={(open) => {
                                if (open) {
                                  // Refresh lien holders list when select is opened
                                  refreshLienHolders();
                                }
                              }}
                            >
                              <FormControl>
                                <SelectTrigger className="text-[#060216]">
                                  <SelectValue placeholder="Select lien holder" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    className="mb-2 w-full justify-center text-sm"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      field.onChange("");
                                    }}
                                  >
                                    Clear Selection
                                  </Button>
                                )}
                                {lienHolders.map((holder) => (
                                  <SelectItem
                                    key={`${holder.id}-${lienHoldersLastUpdated}`}
                                    value={holder.id.toString()}
                                    className="text-[#060216]"
                                  >
                                    {holder.company}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <AddEditLienHolder
                              onSuccess={(newLienHolderId) => {
                                if (newLienHolderId) {
                                  // Store the new lien holder ID as pending
                                  setPendingLienHolderId(newLienHolderId.toString());
                                  // Refresh lien holders to get updated data
                                  refreshLienHolders();
                                }
                              }}
                            />
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="treatment_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm text-[#060216]/50">
                        <Stethoscope className="w-4 h-4 inline-block mr-2" />
                        treatment status <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger
                            className={
                              form.formState.errors.treatment_status
                                ? "border-red-500"
                                : ""
                            }
                          >
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={TreatmentStatus.TREATING}>
                            Ongoing
                          </SelectItem>
                          <SelectItem value={TreatmentStatus.COMPLETE}>
                            Completed
                          </SelectItem>
                          <SelectItem value={TreatmentStatus.UNKNOWN}>
                            Unknown
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      {form.formState.errors.treatment_status && (
                        <p className="text-sm text-red-500 mt-1">
                          {form.formState.errors.treatment_status.message}
                        </p>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="treatment_description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm text-[#060216]/50">
                        <ClipboardList className="w-4 h-4 inline-block mr-2" />
                        treatment description
                      </FormLabel>
                      <FormControl>
                        <CustomCKEditor
                          initialValue={field.value || ""}
                          onChange={(value) => field.onChange(value)}
                          placeholder="Enter treatment description..."
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="first_visit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Visit</FormLabel>
                        <FormControl>
                          <CustomDateInput
                            value={formatDateForInput(field.value)}
                            onChange={(value) => {
                              const { isValid, error } = validateDate(
                                value,
                                false
                              ); // false to disallow future dates
                              if (isValid) {
                                field.onChange(formatDateForApi(value));
                              } else if (error) {
                                form.setError("first_visit", {
                                  type: "manual",
                                  message: error,
                                });
                              }
                            }}
                            error={!!form.formState.errors.first_visit}
                            maxDate={new Date()}
                            onError={(message) => {
                              form.setError("first_visit", {
                                type: "manual",
                                message,
                              });
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="last_visit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Visit</FormLabel>
                        <FormControl>
                          <CustomDateInput
                            value={formatDateForInput(field.value)}
                            onChange={(value) => {
                              const { isValid, error } = validateDate(
                                value,
                                false
                              ); // false to disallow future dates
                              if (isValid) {
                                field.onChange(formatDateForApi(value));
                              } else if (error) {
                                form.setError("last_visit", {
                                  type: "manual",
                                  message: error,
                                });
                              }
                            }}
                            error={!!form.formState.errors.last_visit}
                            maxDate={new Date()}
                            onError={(message) => {
                              form.setError("last_visit", {
                                type: "manual",
                                message,
                              });
                            }}
                          />
                        </FormControl>
                        {form.formState.errors.last_visit && (
                          <p className="text-sm text-red-500 mt-1">
                            {form.formState.errors.last_visit.message}
                          </p>
                        )}
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="number_of_visits"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel># of Visits</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="original_bill"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm text-[#060216]/50">
                          <DollarSign className="w-4 h-4 inline-block mr-2" />
                          original bill
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="adjusted_bill"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Adjustment</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="account_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Write off</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="insurance_paid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Insurance Paid</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medpay_pip_paid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Medpay/PIP Paid</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="client_paid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Client Paid</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="still_owed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Still Owed</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              {...field}
                              onChange={(e) => {
                                setIsStillOwedManuallyEdited(true);
                                field.onChange(e);
                              }}
                              className="pr-10"
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full"
                            onClick={() => {
                              setIsStillOwedManuallyEdited(false);
                              calculateStillOwed();
                            }}
                            title="Recalculate (Original bill - adjustment - write off - insurance paid - Medpay/PIP paid - client paid)"
                          >
                            <Calculator className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Calculated as: Original bill - adjustment - write off - insurance paid - Medpay/PIP paid - client paid
                        </p>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="paid_plus_owed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Paid Plus Owed</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {/* <div className="flex space-x-4">
                  <FormField
                    control={form.control}
                    name="signed_lien"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel>Signed Lien/LOP</FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="filed_lien"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel>Filed Lien</FormLabel>
                      </FormItem>
                    )}
                  />
                </div> */}
              </div>

              <div className="flex justify-between space-x-2 pt-4 border-t">
                <div className="flex items-center gap-2">
                  {/* Additional buttons or content can go here */}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={handleCancelClick}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setIsSyncDialogOpen(true)}
                  >
                    Sync Linked Cases
                  </Button>
                  <Button
                    type="button"
                    onClick={(e) => {
                      form.handleSubmit((values) => onSubmit(values))(e);
                    }}
                  >
                    {isEdit ? "Update" : "Create"}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
      <SyncCaseDialog
        isOpen={isSyncDialogOpen}
        onClose={() => setIsSyncDialogOpen(false)}
        onSync={handleSync}
        isSyncing={isSyncing}
        caseId={caseId}
        syncType="providers"
      />
    </>
  );
}
