"use client"

import React from 'react';
import { useParams } from "next/navigation"
import { Label } from "@/components/ui/label"
import { MapPin, Phone, Mail } from "lucide-react"
import { AddEditMiscLiens } from "./AddEditMiscLiens"
import { useMiscLiensQuery, useUpdateMiscLienFinalStatusMutation } from "@/services/case-management/lienService"
import { CaseMiscellaneousLienResponse } from "@/type/case-management/lienTypes"
import { Skeleton } from "@/components/ui/skeleton"
import RichTextViewer from "@/components/ui/RichTextViewer"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { SyncCase } from '@/components/syncCase';

interface MiscLiensProps {
  caseId: string;
}

export function MiscLiens({ caseId }: MiscLiensProps) {
    const { data: liens, isLoading, error, refetch: refetchLiens } = useMiscLiensQuery(caseId);

    // Move the mutation hook outside the callback
    const updateMutation = useUpdateMiscLienFinalStatusMutation(caseId, '');

    // Handle checkbox change
    const handleFinalStatusChange = (lienId: string, checked: boolean) => {
        updateMutation.mutate({ is_final: checked });
    };

    const handleSyncComplete = () => {
        refetchLiens();
    };

    const handleSyncError = (error: Error) => {
        console.error('Sync error:', error);
    };

    if (isLoading) {
        return (
            <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center text-red-600 py-8">
                Failed to load miscellaneous liens. Please try again later.
            </div>
        );
    }

    if (!liens?.length) {
        return (
            <div className="text-center text-gray-500 py-8">
                No miscellaneous liens found. Add one to get started.
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Miscellaneous Liens</h2>
            </div>

            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Lien Holder</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Lien Details</TableHead>
                        <TableHead></TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {liens.map((lien: CaseMiscellaneousLienResponse) => (
                        <TableRow key={lien.id}>
                            <TableCell>
                                <div className="space-y-1">
                                    <p className="font-medium">{lien.lien_holder.company}</p>
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <MapPin className="h-3.5 w-3.5 mr-1" />
                                        <span>
                                            {[
                                                lien.lien_holder.street1,
                                                lien.lien_holder.city,
                                                lien.lien_holder.state,
                                                lien.lien_holder.zip_code
                                            ].filter(Boolean).join(", ")}
                                        </span>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div className="space-y-1">
                                    {lien.lien_holder.phone && (
                                        <div className="flex items-center text-sm">
                                            <Phone className="h-3.5 w-3.5 mr-1" />
                                            <span>{lien.lien_holder.phone}</span>
                                        </div>
                                    )}
                                    {lien.lien_holder.email && (
                                        <div className="flex items-center text-sm">
                                            <Mail className="h-3.5 w-3.5 mr-1" />
                                            <span>{lien.lien_holder.email}</span>
                                        </div>
                                    )}
                                </div>
                            </TableCell>
                            <TableCell>
                                <div className="space-y-2">
                                    <div>
                                        <Label className="text-sm text-muted-foreground">Lien Name</Label>
                                        <p className="font-medium">{lien.lien_name}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm text-muted-foreground">Lien Amount</Label>
                                        <p className="font-medium">${lien.lien_amount}</p>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id={`final-lien-${lien.id}`}
                                            checked={lien.final_lien}
                                            onCheckedChange={(checked) => {
                                                if (lien.id) {
                                                    handleFinalStatusChange(lien.id.toString(), checked as boolean);
                                                }
                                            }}
                                        />
                                        <Label htmlFor={`final-lien-${lien.id}`}>Final Lien</Label>
                                    </div>
                                    {lien.final_lien && lien.created_at && (
                                        <div>
                                            <Label className="text-sm text-muted-foreground">Final Date</Label>
                                            <p>{format(new Date(lien.created_at), "PPP")}</p>
                                        </div>
                                    )}
                                    {lien.description && (
                                        <div>
                                            <Label className="text-sm text-muted-foreground">Description</Label>
                                            <div className="text-sm"><RichTextViewer data={lien.description} /></div>
                                        </div>
                                    )}
                                </div>
                            </TableCell>
                            <TableCell>
                                {/* <SyncCase
                                    caseId={caseId}
                                    sourceId={lien.lien_holder.id}
                                    onSyncComplete={handleSyncComplete}
                                    onSyncError={handleSyncError}
                                    syncType="misc-liens"
                                /> */}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
} 