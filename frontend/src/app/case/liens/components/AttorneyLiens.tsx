"use client"

import React from 'react';
import { useParams } from "next/navigation"
import { Label } from "@/components/ui/label"
import { MapPin, Phone, Mail } from "lucide-react"
import { AddEditAtorneyLiens } from "./AddEditAtorneyLiens"
import { useAttorneyLiensQuery, useUpdateAttorneyLienFinalStatusMutation } from "@/services/case-management/lienService"
import { CaseAttorneyLienResponse } from "@/type/case-management/lienTypes"
import { Skeleton } from "@/components/ui/skeleton"
import RichTextViewer from "@/components/ui/RichTextViewer"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { SyncCase } from '@/components/syncCase';

interface AttorneyLiensProps {
    caseId: string;
}

export function AttorneyLiens({ caseId }: AttorneyLiensProps) {
    const { data: liens, isLoading, error, refetch: refetchLiens } = useAttorneyLiensQuery(caseId);

    // Move the mutation hook outside the callback
    const updateMutation = useUpdateAttorneyLienFinalStatusMutation(caseId, '');

    // Handle checkbox change
    const handleFinalStatusChange = (lienId: string, checked: boolean) => {
        updateMutation.mutate({ is_final: checked });
    };

    const handleSyncComplete = () => {
        refetchLiens();
    };

    const handleSyncError = (error: Error) => {
        console.error('Sync error:', error);
    };

    if (isLoading) {
        return (
            <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center text-red-600 py-8">
                Failed to load attorney liens. Please try again later.
            </div>
        );
    }

    if (!liens?.length) {
        return (
            <div className="text-center text-gray-500 py-8">
                No attorney liens found. Add one to get started.
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Attorney Liens</h2>
            </div>

            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Law Firm Details</TableHead>
                        <TableHead>Attorney Details</TableHead>
                        <TableHead>Lien Information</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {liens.map((lien: CaseAttorneyLienResponse) => (
                        <TableRow key={lien.id}>
                            <TableCell>
                                <div className="space-y-2">
                                    <h3 className="font-semibold">{lien.law_firm.office_name}</h3>
                                    <div className="space-y-1 text-sm">
                                        <div className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span>{lien.law_firm.phone || '—'}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Mail className="h-4 w-4 text-muted-foreground" />
                                            <span>{lien.law_firm.email || '—'}</span>
                                        </div>
                                        <div className="flex items-start gap-2">
                                            <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                                            <div>
                                                <p>{lien.law_firm.street1}</p>
                                                {lien.law_firm.street2 && <p>{lien.law_firm.street2}</p>}
                                                <p>{lien.law_firm.city}, {lien.law_firm.state} {lien.law_firm.zip_code}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div className="space-y-2">
                                    <p className="font-medium">
                                        {lien.attorney ?
                                            `${lien.attorney.first_name || ''} ${lien.attorney.middle_name || ''} ${lien.attorney.last_name || ''}`.trim() || '—'
                                            : '—'}
                                    </p>
                                    <div className="space-y-1 text-sm">
                                        <div className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span>{lien.attorney.phone || '—'}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Mail className="h-4 w-4 text-muted-foreground" />
                                            <span>{lien.attorney.email || '—'}</span>
                                        </div>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div className="space-y-2">
                                    <div>
                                        <Label className="text-sm text-muted-foreground">Lien Amount</Label>
                                        <p className="font-medium">${lien.fee_amount}</p>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id={`final-lien-${lien.id}`}
                                            checked={lien.final_lien}
                                            onCheckedChange={(checked) => {
                                                if (lien.id) {
                                                    handleFinalStatusChange(lien.id.toString(), checked as boolean);
                                                }
                                            }}
                                        />
                                        <Label htmlFor={`final-lien-${lien.id}`}>Final Lien</Label>
                                    </div>
                                    {lien.final_lien && lien.created_at && (
                                        <div>
                                            <Label className="text-sm text-muted-foreground">Date Created</Label>
                                            <p>{format(new Date(lien.created_at), "PPP")}</p>
                                        </div>
                                    )}
                                    {lien.note && (
                                        <div>
                                            <Label className="text-sm text-muted-foreground">Note</Label>
                                            <div className="text-sm"><RichTextViewer data={lien.note} /></div>
                                        </div>
                                    )}
                                </div>
                            </TableCell>
                            <TableCell>
                                {/* <SyncCase
                                    caseId={caseId}
                                    sourceId={lien.attorney.id}
                                    onSyncComplete={handleSyncComplete}
                                    onSyncError={handleSyncError}
                                    syncType="attorney-liens"
                                /> */}
                                <AddEditAtorneyLiens
                                    isEdit={true}
                                    type="attorney"
                                    selectedLien={lien}
                                />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    )
} 