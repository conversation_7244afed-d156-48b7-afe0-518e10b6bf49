"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ger,
    DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Edit, Plus } from "lucide-react"
import { useState } from "react"
import { useCreateMiscLienMutation, useUpdateMiscLienMutation } from "@/services/case-management/lienService"
import { CaseMiscellaneousLienResponse } from "@/type/case-management/lienTypes"
import { useLienHoldersQuery } from "@/services/orgAPIs"
import { AddEditLienHolder } from "../../medical-treatment/components/AddEditLienHolder"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import CustomCKEditor from "@/components/ckeditor/CKEditor"
import { useQueryClient } from "@tanstack/react-query"
import { Checkbox } from "@/components/ui/checkbox"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import { formatDateForApi, formatDateForInput } from "@/utils/dateUtils"
import { RequiredLabel } from "@/components/ui/required-label"
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog"
import { useMiscellaneousLienSyncMutation } from "@/services/case-management/caseSyncService"
import { useToast } from "@/hooks/use-toast"

const formSchema = z.object({
    lien_holder: z.number().min(1, "Please select a lien holder"),
    lien_name: z.string().min(1, "Lien name is required"),
    lien_amount: z.string().min(1, "Lien amount is required"),
    description: z.string().optional(),
    final_lien: z.boolean().default(false),
    final_lien_date: z.string().optional(),
    final_amount: z.string().optional(),
}).refine((data) => {
    if (data.final_lien) {
        return data.final_amount && data.final_amount.length > 0;
    }
    return true;
}, {
    message: "Final amount is required for final lien",
    path: ["final_amount"]
}).refine((data) => {
    if (data.final_lien) {
        return data.final_lien_date && data.final_lien_date.length > 0;
    }
    return true;
}, {
    message: "Final lien date is required for final lien",
    path: ["final_lien_date"]
});

interface AddEditMiscLiensProps {
    isEdit?: boolean;
    selectedLien?: CaseMiscellaneousLienResponse;
}

export function AddEditMiscLiens({ isEdit = false, selectedLien }: AddEditMiscLiensProps) {
    const params = useParams<{ caseId: string }>();

    if (!params?.caseId) {
        throw new Error('Case ID is required for this component');
    }

    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [lienHoldersLastUpdated, setLienHoldersLastUpdated] = useState<number>(Date.now());
    const { data: lienHolders = [], isLoading: isLoadingLienHolders } = useLienHoldersQuery();
    const createMutation = useCreateMiscLienMutation(params.caseId);
    const updateMutation = useUpdateMiscLienMutation(params.caseId, selectedLien?.id?.toString() || "");
    const miscLienSync = useMiscellaneousLienSyncMutation(params.caseId);
    const queryClient = useQueryClient();
    const { toast } = useToast();

    // Form
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            lien_holder: selectedLien?.lien_holder.id || 0,
            lien_name: selectedLien?.lien_name || "",
            lien_amount: selectedLien?.lien_amount || "",
            description: selectedLien?.description || "",
            final_lien: selectedLien?.final_lien || false,
            final_lien_date: selectedLien?.final_lien_date || "",
            final_amount: selectedLien?.final_amount || "",
        },
    });

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(newOpen);
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        form.reset();
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const selectedLienHolder = lienHolders.find(holder =>
        holder.id === form.getValues().lien_holder
    );

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    async function onSubmit(values: z.infer<typeof formSchema>) {
        try {
            // Pick only the required fields and add optional ones if they have values
            const formData = {
                lien_holder: values.lien_holder,
                lien_name: values.lien_name,
                lien_amount: values.lien_amount,
                final_lien: values.final_lien,
                ...(values.description ? { description: values.description } : {}),
                ...(values.final_lien_date ? { final_lien_date: values.final_lien_date } : {}),
                ...(values.final_amount ? { final_amount: values.final_amount } : {})
            };

            if (isEdit && selectedLien) {
                await updateMutation.mutateAsync(formData);
            } else {
                await createMutation.mutateAsync(formData);
            }
            form.reset();
            setOpen(false);
        } catch (error) {
            console.error("Failed to save lien:", error);
        }
    }

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            setIsSyncing(true);

            // First update/create the lien with current form values
            const values = form.getValues();
            const formData = {
                lien_holder: values.lien_holder,
                lien_name: values.lien_name,
                lien_amount: values.lien_amount,
                final_lien: values.final_lien,
                ...(values.description ? { description: values.description } : {}),
                ...(values.final_lien_date ? { final_lien_date: values.final_lien_date } : {}),
                ...(values.final_amount ? { final_amount: values.final_amount } : {})
            };

            let updatedLien;
            if (isEdit && selectedLien?.id) {
                updatedLien = await updateMutation.mutateAsync(formData);
            } else {
                updatedLien = await createMutation.mutateAsync(formData);
            }

            if (!updatedLien?.id) {
                throw new Error("Failed to get updated lien ID");
            }

            // Then sync with selected cases
            await miscLienSync.mutateAsync({
                miscellaneous_lien_id: updatedLien.id,
                target_case_ids: selectedCaseIds
            });

            setIsSyncDialogOpen(false);
            setOpen(false);
            form.reset();

        } catch (error) {
            console.error("Failed to sync miscellaneous lien:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to sync miscellaneous lien details",
            });
        } finally {
            setIsSyncing(false);
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {isEdit ? (
                        <Button
                            variant="ghost"
                            size="icon"
                            className="hover:bg-green-100"
                        >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                        </Button>
                    ) : (
                        <Button
                            variant="link"
                            className="text-green-600"
                        >
                            <Plus className="w-4 h-4 mr-2" />
                            Misc Lien
                        </Button>
                    )}
                </DialogTrigger>
                <DialogContent className="max-w-3xl flex flex-col overflow-hidden">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? "Edit Miscellaneous Lien" : "Add Miscellaneous Lien"}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
                            <div className="flex-1 max-h-[75vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <div className="space-y-4">
                                    <FormField
                                        control={form.control}
                                        name="lien_name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Lien Name <span className="text-red-500">*</span></FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="lien_amount"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Lien Amount <span className="text-red-500">*</span></FormLabel>
                                                <FormControl>
                                                    <Input type="number" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="lien_holder"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Lien Holder</FormLabel>
                                                <Select
                                                    disabled={isLoadingLienHolders}
                                                    onValueChange={(value) => field.onChange(parseInt(value))}
                                                    value={field.value.toString()}
                                                    onOpenChange={(open) => {
                                                        if (open) {
                                                            queryClient.invalidateQueries({ queryKey: ['lienHolders'] });
                                                            setLienHoldersLastUpdated(Date.now());
                                                        }
                                                    }}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select a lien holder" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {field.value && (
                                                            <Button
                                                                variant="ghost"
                                                                className="mt-2 w-full justify-center text-sm"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    field.onChange(0);
                                                                }}
                                                            >
                                                                Clear Selection
                                                            </Button>
                                                        )}
                                                        {lienHolders.map((holder) => (
                                                            <SelectItem
                                                                key={`${holder.id}-${lienHoldersLastUpdated}`}
                                                                value={holder.id.toString()}
                                                            >
                                                                {holder.company}
                                                                {(holder.first_name || holder.last_name) &&
                                                                    ` - ${[holder.first_name, holder.last_name].filter(Boolean).join(" ")}`}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <div className="flex items-center gap-2">
                                        {selectedLienHolder && (
                                            <AddEditLienHolder
                                                isEdit={true}
                                                lienHolderId={selectedLienHolder.id.toString()}
                                                initialData={selectedLienHolder}
                                            />
                                        )}
                                        <AddEditLienHolder
                                            onSuccess={async (newLienHolderId) => {
                                                if (newLienHolderId) {
                                                    await queryClient.invalidateQueries({
                                                        queryKey: ['lienHolders']
                                                    });

                                                    setLienHoldersLastUpdated(Date.now());

                                                    setTimeout(() => {
                                                        form.setValue("lien_holder", newLienHolderId, {
                                                            shouldValidate: true,
                                                            shouldDirty: true,
                                                            shouldTouch: true
                                                        });

                                                        form.trigger("lien_holder");
                                                    }, 300);
                                                }
                                            }}
                                        />
                                    </div>

                                    <FormField
                                        control={form.control}
                                        name="description"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Description</FormLabel>
                                                <FormControl>
                                                    <div className="bg-white border rounded-md">
                                                        <CustomCKEditor
                                                            initialValue={field.value || ""}
                                                            onChange={(value) => field.onChange(value)}
                                                            minHeight="200px"
                                                        />
                                                    </div>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="final_lien"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={(checked) => {
                                                            field.onChange(checked);
                                                            if (checked) {
                                                                // Set default values when checked
                                                                form.setValue("final_lien_date", formatDateForApi(new Date().toLocaleDateString()));
                                                                form.setValue("final_amount", form.getValues("lien_amount"));
                                                            } else {
                                                                // Clear values when unchecked
                                                                form.setValue("final_lien_date", "");
                                                                form.setValue("final_amount", "");
                                                            }
                                                        }}
                                                    />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                    <FormLabel>Final Lien</FormLabel>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {form.watch("final_lien") && (
                                        <>
                                            <FormField
                                                control={form.control}
                                                name="final_amount"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <RequiredLabel>Final Amount</RequiredLabel>
                                                        <FormControl>
                                                            <Input type="number" {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="final_lien_date"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <RequiredLabel>Final Lien Date</RequiredLabel>
                                                        <FormControl>
                                                            <CustomDateInput
                                                                value={field.value ? formatDateForInput(field.value) : ""}
                                                                onChange={(value: string) => {
                                                                    field.onChange(formatDateForApi(value));
                                                                }}
                                                                error={!!form.formState.errors.final_lien_date}
                                                                onError={(message: string) => {
                                                                    form.setError("final_lien_date", {
                                                                        type: "manual",
                                                                        message: message,
                                                                    });
                                                                }}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </>
                                    )}
                                </div>
                            </div>
                            <div className="flex-none border-t mt-4">
                                <div className="flex justify-end gap-2 py-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleCancelClick}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsSyncDialogOpen(true)}
                                        disabled={isSyncing}
                                    >
                                        Sync Linked Cases
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={(e) => {
                                            form.handleSubmit((values) => onSubmit(values))(e);
                                        }}
                                        className="bg-green-600 hover:bg-green-700 text-white"
                                    >
                                        {isEdit ? "Update" : "Create"}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />

            <SyncCaseDialog
                isOpen={isSyncDialogOpen}
                onClose={() => setIsSyncDialogOpen(false)}
                onSync={handleSync}
                isSyncing={isSyncing}
                caseId={params.caseId}
                syncType="misc-liens"
            />
        </>
    );
} 