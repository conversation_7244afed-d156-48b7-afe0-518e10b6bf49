"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormMessage,
    FormLabel,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Edit, Plus } from "lucide-react"
import { AddEditAttorneyContact } from "@/app/case/employment/components/AddEditAttorneyContact"
import { AddEditLawFirm } from "@/app/client-details/components/AddEditLawFirm"
import { useLawFirmsQuery, useAttorneysQuery } from "@/services/orgAPIs"
import { useCreateAttorneyLienMutation, useUpdateAttorneyLienMutation } from "@/services/case-management/lienService"
import { CaseAttorneyLienResponse } from "@/type/case-management/lienTypes"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import CustomCKEditor from "@/components/ckeditor/CKEditor"
import { useQueryClient } from "@tanstack/react-query"
import { RequiredLabel } from "@/components/ui/required-label";
import { Checkbox } from "@/components/ui/checkbox"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import { formatDateForApi, formatDateForInput } from "@/utils/dateUtils"
import SyncCaseDialog from "@/components/CaseOverview/components/SyncCaseDialog"
import { useAttorneyLienSyncMutation, useMiscellaneousLienSyncMutation } from "@/services/case-management/caseSyncService"
import { toast } from "@/hooks/use-toast"

const formSchema = z.object({
    law_firm: z.number(),
    attorney: z.number(),
    fee_amount: z.string().min(1, "Fee amount is required"),
    note: z.string().optional(),
    final_lien: z.boolean().default(false),
    final_lien_date: z.string().optional(),
    final_amount: z.string().optional(),
}).refine((data) => {
    if (data.final_lien) {
        return data.final_amount && data.final_amount.length > 0;
    }
    return true;
}, {
    message: "Final amount is required for final lien",
    path: ["final_amount"]
}).refine((data) => {
    if (data.final_lien) {
        return data.final_lien_date && data.final_lien_date.length > 0;
    }
    return true;
}, {
    message: "Final lien date is required for final lien",
    path: ["final_lien_date"]
});

interface AddEditAtorneyLiensProps {
    isEdit?: boolean
    type: 'attorney' | 'misc'
    selectedLien?: CaseAttorneyLienResponse // We'll type this properly once we have the data
}

export function AddEditAtorneyLiens({ isEdit = false, type, selectedLien }: AddEditAtorneyLiensProps) {
    const params = useParams<{ caseId: string }>();

    if (!params?.caseId) {
        throw new Error('Case ID is required for this component');
    }

    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [lawFirmsLastUpdated, setLawFirmsLastUpdated] = useState<number>(Date.now());
    const [attorneysLastUpdated, setAttorneysLastUpdated] = useState<number>(Date.now());
    const title = type === 'attorney' ? 'Attorney Lien' : 'Misc Lien';
    const queryClient = useQueryClient();

    // Fetch law firms and attorneys
    const { data: lawFirms, } = useLawFirmsQuery();
    const { data: attorneys, } = useAttorneysQuery();

    // Mutations
    const createMutation = useCreateAttorneyLienMutation(params.caseId);
    const updateMutation = useUpdateAttorneyLienMutation(params.caseId, selectedLien?.id?.toString() || '');
    const attorneyLienSync = useAttorneyLienSyncMutation(params.caseId);
    const miscLienSync = useMiscellaneousLienSyncMutation(params.caseId);

    // Form
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            law_firm: selectedLien?.law_firm?.id,
            attorney: selectedLien?.attorney?.id,
            fee_amount: selectedLien?.fee_amount || "",
            note: selectedLien?.note || "",
            final_lien: selectedLien?.final_lien || false,
            final_lien_date: selectedLien?.final_lien_date || "",
            final_amount: selectedLien?.final_amount || "",
        },
    });

    // Reset form to initial values based on selectedLien
    const resetFormToInitialValues = () => {
        form.reset({
            law_firm: selectedLien?.law_firm?.id,
            attorney: selectedLien?.attorney?.id,
            fee_amount: selectedLien?.fee_amount || "",
            note: selectedLien?.note || "",
            final_lien: selectedLien?.final_lien || false,
            final_lien_date: selectedLien?.final_lien_date || "",
            final_amount: selectedLien?.final_amount || "",
        });
    };

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            // Format date for API
            const formData = {
                law_firm: values.law_firm,
                attorney: values.attorney,
                fee_amount: values.fee_amount,
                final_lien: values.final_lien,
                ...(values.note ? { note: values.note } : {}),
                ...(values.final_lien_date ? { final_lien_date: values.final_lien_date } : {}),
                ...(values.final_amount ? { final_amount: values.final_amount } : {})
            };

            if (isEdit && selectedLien) {
                await updateMutation.mutateAsync(formData);
            } else {
                await createMutation.mutateAsync(formData);
            }
            setOpen(false);
            resetFormToInitialValues();
        } catch (error) {
            console.error('Failed to submit:', error);
        }
    };

    const handleOpenChange = (newOpen: boolean) => {
        // If opening the dialog, reset form to initial values
        if (newOpen) {
            resetFormToInitialValues();
            setOpen(newOpen);
            return;
        }

        // If closing the dialog
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleClose = () => {
        setOpen(false);
        resetFormToInitialValues();
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleSync = async (selectedCaseIds: string[]) => {
        try {
            setIsSyncing(true);

            // First update the lien with current form values
            const values = form.getValues();
            if (!values.law_firm || !values.attorney) {
                throw new Error("Law firm and attorney are required");
            }

            const formData = {
                law_firm: values.law_firm,
                attorney: values.attorney,
                fee_amount: values.fee_amount,
                final_lien: values.final_lien,
                ...(values.note ? { note: values.note } : {}),
                ...(values.final_lien_date ? { final_lien_date: values.final_lien_date } : {}),
                ...(values.final_amount ? { final_amount: values.final_amount } : {})
            };

            let updatedLien;
            if (isEdit && selectedLien) {
                updatedLien = await updateMutation.mutateAsync(formData);
            } else {
                updatedLien = await createMutation.mutateAsync(formData);
            }

            if (!updatedLien?.id) {
                throw new Error("Failed to get updated lien ID");
            }

            // Then call sync service based on type
            if (type === 'attorney') {
                await attorneyLienSync.mutateAsync({
                    attorney_lien_id: updatedLien.id,
                    target_case_ids: selectedCaseIds
                });
            } else {
                await miscLienSync.mutateAsync({
                    miscellaneous_lien_id: updatedLien.id,
                    target_case_ids: selectedCaseIds
                });
            }

            setIsSyncDialogOpen(false);
            setOpen(false);
            resetFormToInitialValues();

        } catch (error) {
            console.error("Failed to sync lien:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: `Failed to sync ${type === 'attorney' ? 'attorney' : 'miscellaneous'} lien details`,
            });
        } finally {
            setIsSyncing(false);
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {isEdit ? (
                        <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                    ) : (
                        <Button
                            type="button"
                            variant="link"
                            className="text-green-600"
                        >
                            <Plus className="w-4 h-4 mr-2" />
                            {isEdit ? `Edit ${title}` : `Add ${title}`}
                        </Button>
                    )}
                </DialogTrigger>
                <DialogContent className="max-w-3xl flex flex-col overflow-hidden">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? `Edit ${title}` : `Add ${title}`}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
                            <div className="flex-1 max-h-[75vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <div className="space-y-6">
                                    <FormField
                                        control={form.control}
                                        name="law_firm"
                                        render={({ field }) => (
                                            <FormItem className="space-y-2">
                                                <RequiredLabel className="text-sm text-gray-500">law firm</RequiredLabel>
                                                <div className="space-y-4">
                                                    <Select
                                                        onValueChange={(value) => field.onChange(parseInt(value))}
                                                        value={field.value?.toString()}
                                                        onOpenChange={(open) => {
                                                            if (open) {
                                                                queryClient.invalidateQueries({ queryKey: ['lawFirms'] });
                                                                setLawFirmsLastUpdated(Date.now());
                                                            }
                                                        }}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select a law firm" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent searchable>
                                                            {lawFirms?.map((firm) => (
                                                                <SelectItem
                                                                    key={`${firm.id}-${lawFirmsLastUpdated}`}
                                                                    value={firm.id.toString()}
                                                                >
                                                                    {firm.office_name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    <div className="flex gap-2">
                                                        <AddEditLawFirm
                                                            onSuccess={async (newLawFirmId) => {
                                                                if (newLawFirmId) {
                                                                    await queryClient.invalidateQueries({
                                                                        queryKey: ['lawFirms']
                                                                    });

                                                                    setLawFirmsLastUpdated(Date.now());

                                                                    setTimeout(() => {
                                                                        form.setValue("law_firm", newLawFirmId, {
                                                                            shouldValidate: true,
                                                                            shouldDirty: true,
                                                                            shouldTouch: true
                                                                        });

                                                                        form.trigger("law_firm");
                                                                    }, 300);
                                                                }
                                                            }}
                                                        >
                                                            <Button variant="outline" size="sm">Add</Button>
                                                        </AddEditLawFirm>
                                                        {field.value && lawFirms?.find(f => f.id === field.value) && (
                                                            <AddEditLawFirm isEdit={true} selectedLawFirm={lawFirms?.find(f => f.id === field.value)}>
                                                                <Button variant="outline" size="sm">Edit</Button>
                                                            </AddEditLawFirm>
                                                        )}
                                                    </div>
                                                    <FormMessage />
                                                </div>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="attorney"
                                        render={({ field }) => (
                                            <FormItem className="space-y-2">
                                                <RequiredLabel className="text-sm text-gray-500">attorney</RequiredLabel>
                                                <div className="space-y-4">
                                                    <Select
                                                        onValueChange={(value) => field.onChange(parseInt(value))}
                                                        value={field.value?.toString()}
                                                        onOpenChange={(open) => {
                                                            if (open) {
                                                                queryClient.invalidateQueries({ queryKey: ['attorneys'] });
                                                                setAttorneysLastUpdated(Date.now());
                                                            }
                                                        }}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select an attorney" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent searchable>
                                                            {attorneys?.map((attorney) => (
                                                                <SelectItem
                                                                    key={`${attorney.id}-${attorneysLastUpdated}`}
                                                                    value={attorney.id.toString()}
                                                                >
                                                                    {`${attorney.first_name} ${attorney.last_name}`}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    <div className="flex gap-2">
                                                        <AddEditAttorneyContact
                                                            onSuccess={async (newAttorneyId) => {
                                                                if (newAttorneyId) {
                                                                    await queryClient.invalidateQueries({
                                                                        queryKey: ['attorneys']
                                                                    });

                                                                    setAttorneysLastUpdated(Date.now());

                                                                    setTimeout(() => {
                                                                        form.setValue("attorney", newAttorneyId, {
                                                                            shouldValidate: true,
                                                                            shouldDirty: true,
                                                                            shouldTouch: true
                                                                        });

                                                                        form.trigger("attorney");
                                                                    }, 300);
                                                                }
                                                            }}
                                                        >
                                                            <Button variant="outline" size="sm">Add</Button>
                                                        </AddEditAttorneyContact>
                                                        {field.value && attorneys?.find(a => a.id === field.value) && (
                                                            <AddEditAttorneyContact isEdit={true} selectedAttorney={attorneys?.find(a => a.id === field.value)}>
                                                                <Button variant="outline" size="sm">Edit</Button>
                                                            </AddEditAttorneyContact>
                                                        )}
                                                    </div>
                                                    <FormMessage />
                                                </div>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="fee_amount"
                                        render={({ field }) => (
                                            <FormItem className="space-y-2">
                                                <RequiredLabel className="text-sm text-gray-500">fee amount</RequiredLabel>
                                                <FormControl>
                                                    <Input type="number" placeholder="Enter fee amount" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="note"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Note</FormLabel>
                                                <FormControl>
                                                    <div className="bg-white border rounded-md">
                                                        <CustomCKEditor
                                                            initialValue={field.value || ""}
                                                            onChange={(value) => field.onChange(value)}
                                                            minHeight="200px"
                                                        />
                                                    </div>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="final_lien"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={(checked) => {
                                                            field.onChange(checked);
                                                            if (checked) {
                                                                // Set default values when checked
                                                                form.setValue("final_lien_date", formatDateForApi(new Date().toLocaleDateString()));
                                                                form.setValue("final_amount", form.getValues("fee_amount"));
                                                            } else {
                                                                // Clear values when unchecked
                                                                form.setValue("final_lien_date", "");
                                                                form.setValue("final_amount", "");
                                                            }
                                                        }}
                                                    />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                    <FormLabel>Final Lien</FormLabel>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {form.watch("final_lien") && (
                                        <>
                                            <FormField
                                                control={form.control}
                                                name="final_amount"
                                                render={({ field }) => (
                                                    <FormItem className="space-y-2">
                                                        <RequiredLabel className="text-sm text-gray-500">Final Amount</RequiredLabel>
                                                        <FormControl>
                                                            <Input type="number" placeholder="Enter final amount" {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="final_lien_date"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <RequiredLabel>Final Lien Date</RequiredLabel>
                                                        <FormControl>
                                                            <CustomDateInput
                                                                value={field.value ? formatDateForInput(field.value) : ""}
                                                                onChange={(value: string) => {
                                                                    field.onChange(formatDateForApi(value));
                                                                }}
                                                                error={!!form.formState.errors.final_lien_date}
                                                                onError={(message: string) => {
                                                                    form.setError("final_lien_date", {
                                                                        type: "manual",
                                                                        message: message,
                                                                    });
                                                                }}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </>
                                    )}
                                </div>
                            </div>
                            <div className="flex-none border-t mt-4">
                                <div className="flex gap-2 py-4">
                                    <Button
                                        variant="outline"
                                        type="button"
                                        className="flex-1"
                                        onClick={handleCancelClick}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        variant="outline"
                                        type="button"
                                        onClick={() => setIsSyncDialogOpen(true)}
                                        disabled={isSyncing}
                                        className="flex-1"
                                    >
                                        Sync Linked Cases
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={(e) => {
                                            form.handleSubmit((values) => onSubmit(values))(e);
                                        }}
                                        className="flex-1 bg-green-600 hover:bg-green-700"
                                        disabled={createMutation.isPending || updateMutation.isPending}
                                    >
                                        {isEdit ? `Save Changes` : `Add ${title}`}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog >

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />

            <SyncCaseDialog
                isOpen={isSyncDialogOpen}
                onClose={() => setIsSyncDialogOpen(false)}
                onSync={handleSync}
                isSyncing={isSyncing}
                caseId={params.caseId}
                syncType={type === 'attorney' ? 'attorney-liens' : 'misc-liens'}
            />
        </>
    );
} 