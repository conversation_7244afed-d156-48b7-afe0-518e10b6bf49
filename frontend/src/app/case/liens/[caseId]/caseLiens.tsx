"use client"

import { notFound, useRouter } from 'next/navigation'
import { ArrowLeft, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { AddEditAtorneyLiens } from '../components/AddEditAtorneyLiens'
import { AddEditMiscLiens } from '../components/AddEditMiscLiens'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AttorneyLiens } from '../components/AttorneyLiens'
import { MiscLiens } from '../components/MiscLiens'

interface CaseLienPageProps {
  caseId: string
}

export default function CaseLienPage({ caseId }: CaseLienPageProps) {
  const router = useRouter();

  if (!caseId) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="sticky top-[64px] z-40 -mx-8 bg-white shadow-md border-b border-gray-200 px-8 py-4">
        <div className="flex justify-between items-center">
          <Button
            variant="ghost"
            className="hover:bg-transparent p-0 justify-start w-fit"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back
          </Button>

          <div className="flex items-center gap-3">
            <AddEditAtorneyLiens isEdit={false} type="attorney" />
            <AddEditMiscLiens isEdit={false} />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <FileText className="w-4 h-4" />
                  Documents
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Document 1</DropdownMenuItem>
                <DropdownMenuItem>Document 2</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <Tabs defaultValue="Attorney Liens" className="w-full">
        <TabsList>
          <TabsTrigger value="Attorney Liens">
            Attorney Liens
          </TabsTrigger>
          <TabsTrigger value="Misc Liens">
            Misc Liens
          </TabsTrigger>
        </TabsList>
        <TabsContent value="Attorney Liens" className="mt-4">
          <AttorneyLiens caseId={caseId} />
        </TabsContent>
        <TabsContent value="Misc Liens" className="mt-4">
          <MiscLiens caseId={caseId} />
        </TabsContent>
      </Tabs>
    </div>
  )
}


