import React, { useState, use<PERSON>allback, useEffect, useMemo, useRef, Suspense } from "react";
import { But<PERSON> } from "@/components/ui/button";
import * as Dialog from "@radix-ui/react-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, X, Edit, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form
} from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useOrganizationUsers } from "@/services/organizationService";
import { useCases } from "@/services/caseService";
import { useCreateEvent, useUpdateEvent } from "@/services/emailCalenderIntegrationService";
import { UnsavedChanges<PERSON>lert } from "@/components/ui/unsaved-changes-alert";
import { MultiSelect } from "@/components/ui/multi-select";
import { EventFormData } from '@/type/emailCalender'
import dynamic from 'next/dynamic';
import CaseSelectionDialog from '@/components/CaseOverview/components/CaseSelectionDialog';
import { CaseResponse } from "@/type/dashboard";

// Dynamically import CKEditor with SSR disabled
const CustomCKEditor = dynamic(
  () => import('@/components/ckeditor/CKEditor'),
  { ssr: false }
);

console.log("CKEditor dynamic import")

interface NewEventDialogProps extends Record<string, unknown> {
  mode: 'create' | 'edit';
  eventId?: string;
  defaultValues?: EventFormData;
  onSuccess?: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface LocalParticipant {
  email: string;
  name?: string;
  status?: string;
}

interface EventData {
  title: string;
  description: string;
  location: string;
  start_time: number;
  end_time: number;
  case_id?: string;
  participants: LocalParticipant[];
}

const eventFormSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  location: z.string().optional(),
  start_time: z.number(),
  end_time: z.number(),
  case_id: z.string().optional(),
  participants: z.array(z.object({
    email: z.string().email(),
    name: z.string().optional(),
    status: z.string().optional()
  }))
});

interface FormValues extends z.infer<typeof eventFormSchema> {
  title: string;
  description?: string;
  location?: string;
  start_time: number;
  end_time: number;
  case_id?: string;
  participants: LocalParticipant[];
}

interface ParticipantOption {
  label: string;
  value: string;
}

export function NewEventDialog({
  mode = 'create',
  eventId,
  defaultValues,
  onSuccess,
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange
}: NewEventDialogProps) {
  // State management
  const [internalOpen, setInternalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [caseLoading, setCaseLoading] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [caseSelectionOpen, setCaseSelectionOpen] = useState(false);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [hasSetDefaultValues, setHasSetDefaultValues] = useState(false);
  const prevDefaultValuesRef = useRef<EventFormData | undefined>();

  // Dialog control
  const isControlled = controlledOpen !== undefined;
  const isOpen = isControlled ? controlledOpen : internalOpen;
  const setIsOpen = useCallback((value: boolean) => {
    if (isControlled) {
      controlledOnOpenChange?.(value);
    } else {
      setInternalOpen(value);
    }
  }, [isControlled, controlledOnOpenChange]);

  // Constants
  const PAGE_SIZE = 25;

  // State management
  const [newParticipant, setNewParticipant] = useState<LocalParticipant>({ email: '', name: '' });
  const [searchValue, setSearchValue] = useState('');
  const [showAddExternal, setShowAddExternal] = useState(false);
  const [dateError, setDateError] = useState<string>('');
  const [participantError, setParticipantError] = useState<string>('');
  // Current page for case pagination (handled by CaseSelectionDialog)
  const [currentPage] = useState<number>(1);
  const [caseSearchTerm, setCaseSearchTerm] = useState<string>("");

  // Form initialization with proper timezone handling
  const form = useForm<FormValues>({
    resolver: zodResolver(eventFormSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      // Use local time for default values to avoid timezone issues
      start_time: Math.floor(Date.now() / 1000),
      end_time: Math.floor(Date.now() / 1000) + 3600, // 1 hour later
      participants: [],
      case_id: undefined
    },
    mode: 'onChange'
  });

  // Data fetching
  const createEventMutation = useCreateEvent();
  const updateEventMutation = useUpdateEvent();
  const { data: organizationUsers = [] } = useOrganizationUsers();
  const { data: casesData } = useCases({
    search: caseSearchTerm,
    page: currentPage,
    pageSize: PAGE_SIZE
  });

  // Cases data for selection dialog
  const cases = casesData?.results || [];

  // Reset form and state when dialog closes
  const resetState = useCallback(() => {
    form.reset({
      title: "",
      description: "",
      location: "",
      start_time: Math.floor(Date.now() / 1000),
      end_time: Math.floor(Date.now() / 1000) + 3600,
      participants: [],
      case_id: undefined
    });
    setLoading(false);
    setShowUnsavedAlert(false);
    setIsEditorReady(false);
  }, [form]);

  // Handle dialog open/close
  const handleOpenChange = (newOpen: boolean) => {

    if (!newOpen) {
      if (form.formState.isDirty) {
        setShowUnsavedAlert(true);
      } else {
        handleClose(newOpen);
      }
    } else {
      setIsOpen(newOpen);
    }
  };

  // Handle dialog close
  const handleClose = useCallback((newOpen: boolean = false) => {
    setIsOpen(newOpen);
    if (!newOpen) {
      resetState();
    }
  }, [resetState, setIsOpen]);

  // Update effect for defaultValues - moved and improved
  useEffect(() => {
    // Only set default values if:
    // 1. We have defaultValues
    // 2. The dialog is open
    // 3. We haven't already set these specific defaultValues (prevents duplicate sets)
    if (defaultValues && isOpen &&
      (!hasSetDefaultValues ||
        JSON.stringify(prevDefaultValuesRef.current) !== JSON.stringify(defaultValues))) {

      try {
        console.log('[NewEventDialog] Setting default values');

        // Clone the defaultValues to avoid reference issues
        const formData = {
          title: defaultValues.title || "",
          description: defaultValues.description || "",
          location: defaultValues.location || "",
          start_time: defaultValues.start_time || Math.floor(Date.now() / 1000),
          end_time: defaultValues.end_time || Math.floor(Date.now() / 1000) + 3600,
          participants: Array.isArray(defaultValues.participants) ? [...defaultValues.participants] : [],
          case_id: defaultValues.case_id
        };

        // Use a more forceful reset approach for production compatibility
        form.reset(formData, {
          keepValues: false,
          keepDefaultValues: false
        });

        // Track that we've set these values
        prevDefaultValuesRef.current = defaultValues;
        setHasSetDefaultValues(true);

        // Force render cycle to ensure values are applied
        setTimeout(() => {
          console.log('[NewEventDialog] Form state after setting defaults:', {
            values: form.getValues(),
            isValid: form.formState.isValid,
            errors: form.formState.errors
          });
        }, 0);
      } catch (error) {
        console.error('[NewEventDialog] Error setting default values:', error);
      }
    }
  }, [defaultValues, isOpen, form, hasSetDefaultValues]);

  // Reset hasSetDefaultValues when the dialog closes
  useEffect(() => {
    if (!isOpen) {
      setHasSetDefaultValues(false);
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('[NewEventDialog] Component unmounting - cleaning up');
      resetState();
    };
  }, [resetState]);

  // Handle form submission
  const onSubmit = async (formData: FormValues) => {
    console.log('[NewEventDialog] Submitting form data===================================================', formData);
    try {

      setLoading(true);

      // Only validate time difference for regular events (not all-day events)
      if (formData.start_time !== undefined && formData.end_time !== undefined) {
        if (!validateDateDifference(formData.start_time, formData.end_time)) {
          setDateError('Event duration must be at least 30 minutes');
          setLoading(false);
          return;
        }
      }

      if (mode === 'create') {
        await createEventMutation.mutateAsync(formData);
      } else if (eventId) {
        await updateEventMutation.mutateAsync({
          event_id: eventId,
          ...formData
        });
      } else {
        throw new Error('Invalid state: Edit mode but no eventId');
      }

      onSuccess?.();
      handleClose(false);
    } catch (error) {
      console.error('[NewEventDialog] Operation failed:', {
        mode,
        eventId,
        error
      });
    } finally {
      setLoading(false);
    }
  };

  // Add error boundary effect
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined') return;

    const handleError = (event: ErrorEvent) => {
      console.error('[NewEventDialog] Global error caught:', {
        error: event.error,
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // Memoized values
  const participantOptions = useMemo(() => {
    const currentParticipants = form.getValues('participants') || [];
    return organizationUsers
      .filter(user => !currentParticipants.some((p: LocalParticipant) => p.email === user.email))
      .filter(user => {
        if (!searchValue) return true;
        const searchLower = searchValue.toLowerCase().trim();
        const displayName = (user.first_name
          ? `${user.first_name} ${user.last_name || ''}`
          : user.email).toLowerCase();
        const email = user.email.toLowerCase();
        const role = (user.role || '').toLowerCase();
        if (email === searchLower) return true;
        const searchTerms = searchLower.split(' ').filter(Boolean);
        if (searchTerms.length === 0) return true;
        return searchTerms.some(term => {
          const nameMatches = displayName.split(' ')
            .filter(Boolean)
            .some(namePart => namePart.toLowerCase().includes(term));
          const [emailUser, emailDomain] = email.split('@');
          const emailMatches =
            email.includes(term) ||
            (emailUser && emailUser.includes(term)) ||
            (emailDomain && emailDomain.includes(term)) ||
            (term.includes('@') && email.includes(term));
          const roleMatches = role.includes(term);
          return nameMatches || emailMatches || roleMatches;
        });
      })
      .map(user => {
        const displayName = user.first_name
          ? `${user.first_name} ${user.last_name || ''}`
          : user.email;
        return {
          label: `${displayName} (${user.email})${user.role ? ` - ${user.role}` : ''}`,
          value: `${user.email}|${displayName}`.trim()
        };
      })
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [organizationUsers, searchValue, form]);

  const formValues = form.watch();

  const handleDateChange = (field: 'start_time' | 'end_time', value: string) => {
    try {
      // Parse the input value as a local date
      const localDate = new Date(value);
      if (isNaN(localDate.getTime())) {
        return;
      }

      // Convert to Unix timestamp (seconds) without timezone adjustment
      // This preserves the exact time selected by the user
      const timestamp = Math.floor(localDate.getTime() / 1000);

      form.setValue(field, timestamp);

      // Validate date difference
      const otherField = field === 'start_time' ? 'end_time' : 'start_time';
      const otherValue = form.getValues(otherField);

      // Only validate if both values are defined
      if (otherValue !== undefined) {
        if (!validateDateDifference(
          field === 'start_time' ? timestamp : otherValue,
          field === 'end_time' ? timestamp : otherValue
        )) {
          setDateError('Event duration must be at least 30 minutes');
        } else {
          setDateError('');
        }
      }
    } catch (error) {
      console.error('[NewEventDialog] Error handling date change:', error);
    }
  };

  const handleAddParticipant = (emailToAdd?: string) => {
    const email = (emailToAdd || newParticipant.email).trim();
    setParticipantError('');

    if (!email || !validateCustomEmail(email)) {
      setParticipantError('Please enter a valid email address');
      return;
    }

    const currentParticipants = form.getValues('participants') || [];
    if (currentParticipants.some((p) => p.email.toLowerCase() === email.toLowerCase())) {
      setParticipantError('This email is already added to participants');
      return;
    }

    if (organizationUsers.some(user => user.email.toLowerCase() === email.toLowerCase())) {
      setParticipantError('This email belongs to an organization member. Please use the organization members dropdown above.');
      return;
    }

    form.setValue('participants', [
      ...currentParticipants,
      {
        email: email,
        name: emailToAdd ? '' : (newParticipant.name || '').trim(),
        status: 'pending'
      }
    ]);

    setNewParticipant({ email: '', name: '' });
    setSearchValue('');
    setShowAddExternal(false);
  };

  const handleRemoveParticipant = (email: string) => {
    const currentParticipants = form.getValues('participants') || [];
    form.setValue(
      'participants',
      currentParticipants.filter((p) => p.email !== email)
    );
  };

  const handleMultiSelectParticipantChange = (selectedValues: string[]) => {
    const currentParticipants = form.getValues('participants') || [];
    const customParticipants = currentParticipants.filter(
      (participant) => !organizationUsers.some(user => user.email === participant.email)
    );

    const selectedOrgParticipants = selectedValues
      .map(value => {
        const [email, name] = value.split('|');
        const orgUser = organizationUsers.find(user => user.email === email);
        return {
          email,
          name: orgUser ? `${orgUser.first_name || ''} ${orgUser.last_name || ''}`.trim() : name || '',
          status: 'pending'
        };
      });

    form.setValue('participants', [...selectedOrgParticipants, ...customParticipants]);
  };

  const validateCustomEmail = useCallback((email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }, []);

  const getParticipantOptions = () => {
    return form.getValues('participants')
      .filter((participant: LocalParticipant) => organizationUsers.some(user => user.email === participant.email))
      .map((participant: LocalParticipant) => {
        const orgUser = organizationUsers.find(user => user.email === participant.email);
        if (!orgUser) return { label: participant.email, value: participant.email };

        const displayName = orgUser.first_name
          ? `${orgUser.first_name} ${orgUser.last_name || ''}`
          : orgUser.email;

        return {
          label: `${displayName} (${orgUser.email})${orgUser.role ? ` - ${orgUser.role}` : ''}`,
          value: `${orgUser.email}|${displayName}`.trim()
        };
      });
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);

    const trimmedValue = value.trim();
    const isValidEmail = validateCustomEmail(trimmedValue);
    const userExists = organizationUsers.some(user =>
      user.email.toLowerCase() === trimmedValue.toLowerCase()
    );
    const isAlreadyParticipant = form.getValues('participants').some(p =>
      p.email.toLowerCase() === trimmedValue.toLowerCase()
    );

    setShowAddExternal(!!(trimmedValue && isValidEmail && !userExists && !isAlreadyParticipant));
  };

  // Pagination and search are handled by the CaseSelectionDialog component

  const validateDateDifference = (startTime: number | undefined, endTime: number | undefined): boolean => {
    // If either time is undefined, we can't validate
    if (startTime === undefined || endTime === undefined) {
      return true; // Skip validation for undefined values
    }

    const thirtyMinutesInSeconds = 30 * 60;
    const isValid = (endTime - startTime) >= thirtyMinutesInSeconds;

    // console.log('[NewEventDialog] Validating date difference:', {
    //   startTime,
    //   endTime,
    //   difference: endTime - startTime,
    //   isValid
    // });

    return isValid;
  };

  const formatDateForInput = (timestamp: number | undefined): string => {
    if (timestamp === undefined) {
      return '';
    }

    try {
      // Convert Unix timestamp to Date object
      const date = new Date(timestamp * 1000);
      if (isNaN(date.getTime())) {
        return '';
      }

      // Format in local timezone instead of UTC
      // This ensures the displayed time matches what the user selected
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      // Format as YYYY-MM-DDTHH:mm for datetime-local input
      const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;

      // console.log('[NewEventDialog] Formatting timestamp for input:', {
      //   timestamp,
      //   date: date.toString(),
      //   formattedDate
      // });

      return formattedDate;
    } catch (error) {
      console.error('[NewEventDialog] Error formatting date:', error);
      return '';
    }
  };

  // Monitor form state for debugging purposes
  useEffect(() => {
    // Uncomment for debugging form state issues
    // console.log('[NewEventDialog] Form state updated:', {
    //   isDirty: form.formState.isDirty,
    //   isSubmitting: form.formState.isSubmitting,
    //   isValid: form.formState.isValid,
    //   errors: form.formState.errors,
    //   values: form.getValues()
    // });
  }, [form.formState, form]);

  // Handle discard changes
  const handleDiscardChanges = useCallback(() => {
    console.log('[NewEventDialog] Discarding changes');
    setShowUnsavedAlert(false);
    handleClose(false);
  }, [handleClose]);

  // Handle continue editing
  const handleContinueEditing = useCallback(() => {
    console.log('[NewEventDialog] Continuing editing');
    setShowUnsavedAlert(false);
  }, []);

  // Add this handler for case selection
  const handleCaseSelect = async (selectedCase: CaseResponse) => {
    try {
      setCaseLoading(true);
      console.log('[NewEventDialog] Case selected:', selectedCase);

      // Update the form value and trigger validation
      form.setValue('case_id', selectedCase.id, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true
      });

      // Update the case search term to show the selected case name
      setCaseSearchTerm(selectedCase.case_name);

      // Force a validation and re-render cycle
      await form.trigger('case_id');
      console.log('[NewEventDialog] Case ID after setting:', form.getValues('case_id'));

      setCaseSelectionOpen(false);
    } catch (error) {
      console.error('[NewEventDialog] Error selecting case:', error);
    } finally {
      setCaseLoading(false);
    }
  };

  // Add effect to update case search when case_id changes
  useEffect(() => {
    if (formValues.case_id) {
      const selectedCase = cases.find(c => c.id === formValues.case_id);
      if (selectedCase) {
        setCaseSearchTerm(selectedCase.case_name);
      }
    } else {
      setCaseSearchTerm('');
    }
  }, [formValues.case_id, cases]);

  // Add ref for CKEditor container
  const editorContainerRef = useRef<HTMLDivElement>(null);

  // Editor state and handlers
  const handleEditorChange = useCallback((data: string) => {
    try {
      if (!isEditorReady) {
        return;
      }

      console.log('[NewEventDialog] CKEditor content changed:', {
        contentLength: data.length,
        eventId
      });
      form.setValue('description', data);
    } catch (error) {
      console.error('[NewEventDialog] Error in handleEditorChange:', error);
    }
  }, [form, eventId, isEditorReady]);

  // Editor initialization
  useEffect(() => {
    let mounted = true;

    if (isOpen) {
      // Wait for DOM to be ready
      requestAnimationFrame(() => {
        if (mounted && editorContainerRef.current) {
          setIsEditorReady(true);
          console.log('[NewEventDialog] CKEditor initialized');
        }
      });
    } else {
      setIsEditorReady(false);
    }

    return () => {
      mounted = false;
      setIsEditorReady(false);
    };
  }, [isOpen]);

  // Error handling is done within the CustomCKEditor component

  // Render CKEditor with error handling
  const renderEditor = () => {
    if (!isEditorReady) {
      return <div className="h-32 bg-gray-50 rounded-md animate-pulse" />;
    }

    return (
      <div ref={editorContainerRef}>
        <CustomCKEditor
          initialValue={form.getValues('description') || ''}
          onChange={handleEditorChange}
          className="min-h-[200px]"
          placeholder="Add event description..."
        />
      </div>
    );
  };

  return (
    <>
      <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
        {!isControlled && (
          <Dialog.Trigger asChild>
            {mode === 'edit' ? (
              <Button variant="link" size="sm">
                <Edit className="h-4 w-4 text-green-600" />
              </Button>
            ) : (
              <Button variant="link" className="text-green-600">
                <Plus className="h-4 w-4" />
                Event
              </Button>
            )}
          </Dialog.Trigger>
        )}
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 z-50 bg-black/80" />
          <Dialog.Content className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg">
            <Dialog.Title className="text-lg font-semibold leading-none tracking-tight">
              {mode === 'create' ? 'Create New Event' : 'Edit Event'}
            </Dialog.Title>
            <Form {...form}>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  console.log('[NewEventDialog] Form submit event triggered', form.getValues());
                  form.handleSubmit(onSubmit)(e);
                }}
                className="grid gap-6 max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
              >
                <div className="grid gap-4 py-4 overflow-y-auto">
                  {/* Event Title */}
                  <div className="grid gap-2">
                    <Label className="text-sm">
                      Event Title <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      {...form.register('title')}
                      placeholder="Enter event title"
                      className="bg-white"
                    />
                    {form.formState.errors.title && (
                      <p className="text-sm text-red-500">{form.formState.errors.title.message}</p>
                    )}
                  </div>

                  {/* Case Selection */}
                  <div className="grid gap-2">
                    <Label className="text-sm">Case</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Selected case will appear here..."
                        value={caseSearchTerm}
                        readOnly
                        className="bg-gray-50"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCaseSelectionOpen(true)}
                        disabled={caseLoading}
                      >
                        {caseLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {formValues.case_id ? 'Changing...' : 'Selecting...'}
                          </>
                        ) : (
                          formValues.case_id ? 'Change Case' : 'Select Case'
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Date and Time */}
                  <div className="grid gap-2">
                    <Label className="text-sm">
                      Date and Time <span className="text-red-500">*</span>
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="grid gap-2">
                        <Label className="text-sm" htmlFor="start_time">Start</Label>
                        <Input
                          id="start_time"
                          type="datetime-local"
                          value={formatDateForInput(formValues.start_time)}
                          onChange={(e) => handleDateChange('start_time', e.target.value)}
                          className="bg-white"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label className="text-sm" htmlFor="end_time">End</Label>
                        <Input
                          id="end_time"
                          type="datetime-local"
                          value={formatDateForInput(formValues.end_time)}
                          onChange={(e) => handleDateChange('end_time', e.target.value)}
                          className="bg-white"
                        />
                      </div>
                    </div>
                    {dateError && (
                      <p className="text-sm text-red-500 mt-1">{dateError}</p>
                    )}
                  </div>

                  {/* Location */}
                  <div className="grid gap-2">
                    <Label className="text-sm">Location</Label>
                    <Input
                      {...form.register('location')}
                      placeholder="Add location"
                      className="bg-white"
                    />
                  </div>

                  {/* Description */}
                  <div className="grid gap-2">
                    <Label className="text-sm">Description</Label>
                    {renderEditor()}
                  </div>

                  {/* Organization Members MultiSelect */}
                  <div className="grid gap-2">
                    <Label className="text-sm">Add Organization Members</Label>
                    <div className={`grid ${showAddExternal ? 'grid-cols-2' : 'grid-cols-1'} gap-2`}>
                      <MultiSelect
                        options={participantOptions}
                        onValueChange={handleMultiSelectParticipantChange}
                        value={getParticipantOptions()
                          .filter(option => form.getValues('participants')
                            .some(p => p.email === option.value.split('|')[0]))
                          .map(option => option.value)}
                        placeholder={participantOptions.length ? "Search..." : "No member available"}
                        className="w-full bg-white"
                        onSearch={handleSearchChange}
                        hideSelectedValues={true}
                      />

                      {showAddExternal && searchValue && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full h-full whitespace-nowrap"
                          onClick={() => handleAddParticipant(searchValue)}
                        >
                          Add {searchValue} as External
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Participants List */}
                  {form.getValues('participants').length > 0 && (
                    <div className="mt-2 space-y-2">
                      <Label>Selected Participants</Label>
                      <div className="space-y-2">
                        {form.getValues('participants').map((participant) => (
                          <div key={participant.email} className="flex items-center justify-between bg-secondary p-2 rounded-md">
                            <div>
                              <div className="font-medium flex items-center gap-2">
                                {participant.email}
                                {organizationUsers.some(user => {
                                  const orgUser = organizationUsers.find(u => u.email === participant.email);
                                  return user.email === participant.email && orgUser?.role && (
                                    <span className="text-xs px-2 py-0.5 bg-blue-100 rounded-full text-blue-700">
                                      {orgUser.role}
                                    </span>
                                  );
                                })}
                              </div>
                              {participant.name && (
                                <div className="text-sm text-muted-foreground">{participant.name}</div>
                              )}
                              {organizationUsers.some(user => user.email === participant.email) && (
                                <div className="text-xs text-blue-500">Organization Member</div>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveParticipant(participant.email)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="grid gap-2">
                    <Label className="text-sm">External Participants</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Email"
                        value={newParticipant.email}
                        onChange={(e) => {
                          setNewParticipant({ ...newParticipant, email: e.target.value });
                          setParticipantError('');
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && newParticipant.email) {
                            e.preventDefault();
                            handleAddParticipant();
                          }
                        }}
                        type="email"
                        className="bg-white"
                      />
                      <Input
                        placeholder="Name (optional)"
                        value={newParticipant.name}
                        onChange={(e) => setNewParticipant({ ...newParticipant, name: e.target.value })}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && newParticipant.email) {
                            e.preventDefault();
                            handleAddParticipant();
                          }
                        }}
                        className="bg-white"
                      />
                      <Button
                        type="button"
                        onClick={() => handleAddParticipant()}
                        variant="outline"
                        size="icon"
                        disabled={!newParticipant.email || !validateCustomEmail(newParticipant.email)}
                        title="Add Participant"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    {participantError && (
                      <p className="text-sm text-red-500">{participantError}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-6">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={handleDiscardChanges}
                  >
                    CANCEL
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : mode === 'edit' ? 'Update Event' : 'Add Event'}
                  </Button>
                </div>

                <Dialog.Close asChild>
                  <button className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close</span>
                  </button>
                </Dialog.Close>
              </form>
            </Form>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />

      <CaseSelectionDialog
        open={caseSelectionOpen}
        onOpenChange={setCaseSelectionOpen}
        onCaseSelect={handleCaseSelect}
        selectedCaseId={form.getValues('case_id')}
      />
    </>
  );
}

export default NewEventDialog;