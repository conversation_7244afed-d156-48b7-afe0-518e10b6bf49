import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Plus, Import } from 'lucide-react'
import { format } from 'date-fns'
import { NavigateAction, View } from 'react-big-calendar'
import { useOrganizationUsersQuery } from "@/services/orgAPIs"
import { MultiSelect } from "@/components/ui/multi-select"
import { User } from "@/type/case-management/orgTypes"
import { formatDateForDisplay, getDayName } from "@/utils/dateUtils"
import { AddEditEvent } from "./AddEditEvent"
import { EventFormData } from "@/type/emailCalender"
import { useState } from "react"

interface SelectedUser {
  id: string;
  color: string;
}

interface CustomToolbarProps {
  onNavigate: (action: NavigateAction) => void;
  onView: (view: View) => void;
  date: Date;
  view: View;
  onAddEvent: () => void;
  onUserChange?: (userIds: string[]) => void | Promise<void>;
  selectedUsers: SelectedUser[];
  eventColors: string[];
  onCsvUpload: () => void;
  selectedEventData: EventFormData;
  setSelectedEventData: (data: EventFormData) => void;
  handleEventSuccess: () => void;
}

export const CustomToolbar = ({
  onNavigate,
  onView,
  date,
  view,
  onAddEvent,
  onUserChange,
  selectedUsers,
  eventColors,
  onCsvUpload,
  selectedEventData,
  setSelectedEventData,
  handleEventSuccess
}: CustomToolbarProps) => {
  const [toolbarCreateDialogOpen, setToolbarCreateDialogOpen] = useState(false);

  const handleToolbarCreateSuccess = () => {
    setToolbarCreateDialogOpen(false);
    handleEventSuccess();
  };

  const getViewTitle = () => {
    switch (view) {
      case 'month':
        return format(date, 'MMMM yyyy')
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        const weekEnd = new Date(weekStart)
        weekEnd.setDate(weekStart.getDate() + 6)
        return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`
      case 'day':
        return format(date, 'EEEE, MMMM d, yyyy')
      case 'agenda':
        return `${getDayName(date.toISOString())} - ${formatDateForDisplay(date.toISOString(), 'MMMM d, yyyy')}`
      default:
        return formatDateForDisplay(date.toISOString(), 'MMMM yyyy')
    }
  }

  const { data: users } = useOrganizationUsersQuery();

  const userOptions = users?.map((user: User, index: number) => ({
    label: user.name,
    value: user.id.toString(),
    icon: () => (
      <div
        className="w-3 h-3 rounded-full"
        style={{ backgroundColor: eventColors[index % eventColors.length] }}
      />
    )
  }))
  .sort((a, b) => a.label.toLowerCase().localeCompare(b.label.toLowerCase())) || [];

  return (
    <div className="flex flex-col gap-4 mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => onNavigate('PREV')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => onNavigate('NEXT')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            onClick={() => onNavigate('TODAY')}
          >
            Today
          </Button>
          <h2 className="text-lg font-semibold ml-4">{getViewTitle()}</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => onView('month')}
            className={view === 'month' ? 'bg-primary text-primary-foreground' : ''}
          >
            Month
          </Button>
          <Button
            variant="outline"
            onClick={() => onView('week')}
            className={view === 'week' ? 'bg-primary text-primary-foreground' : ''}
          >
            Week
          </Button>
          <Button
            variant="outline"
            onClick={() => onView('day')}
            className={view === 'day' ? 'bg-primary text-primary-foreground' : ''}
          >
            Day
          </Button>
          <Button
            variant="outline"
            onClick={() => onView('agenda')}
            className={view === 'agenda' ? 'bg-primary text-primary-foreground' : ''}
          >
            Agenda
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={onCsvUpload} variant="outline">
            <Import className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
          <Button
            variant="link"
            className="text-green-600"
            onClick={() => setToolbarCreateDialogOpen(true)}
          >
            <Plus className="h-4 w-4" />
            Event
          </Button>
        </div>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <MultiSelect
            options={userOptions}
            onValueChange={(values) => onUserChange?.(values)}
            value={selectedUsers.map(u => u.id)}
            placeholder="Filter by team members"
          />
        </div>
      </div>
      <AddEditEvent
        mode="create"
        onSuccess={handleToolbarCreateSuccess}
        open={toolbarCreateDialogOpen}
        onOpenChange={setToolbarCreateDialogOpen}
        hideButton={true}
      />
    </div>
  )
} 