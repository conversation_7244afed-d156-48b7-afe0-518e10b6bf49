import { useMutation, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Common types for sync requests and responses
interface BaseSyncRequest {
    target_case_ids: string[];
}

interface BaseSyncResponse {
    success: boolean;
    message?: string;
    synced_cases?: string[];
    errors?: string[] | null;
}

// Specific sync request types
interface DefendantSyncRequest extends BaseSyncRequest {
    source_defendant_id: number;
}

interface NoteSyncRequest extends BaseSyncRequest {
    source_note_id: number;
}

interface TaskSyncRequest extends BaseSyncRequest {
    source_task_id: number;
}

interface UserCardSyncRequest extends BaseSyncRequest {
    source_card_id: number;
}

interface ClientSyncRequest extends BaseSyncRequest {
    source_client_id: number;
    sync_options?: {
        sync_insurances?: boolean;
        sync_property_damage?: boolean;
        sync_legal_representation?: boolean;
        sync_adjusters?: boolean;
        sync_basic_details?: boolean;
        sync_contact_details?: boolean;
    };
}

interface IncidentDetailsSyncRequest extends BaseSyncRequest {
    source_incident_detail_id: number;
}

interface PartySyncRequest extends BaseSyncRequest {
    party_id: number;
}

interface TreatmentProviderSyncRequest extends BaseSyncRequest {
    treatment_provider_id: number;
}

interface LienSyncRequest extends BaseSyncRequest {
    source_lien_id: number;
}

interface WorkerSyncRequest extends BaseSyncRequest {
    source_worker_id: number;
}

interface StatusSyncRequest extends BaseSyncRequest {
    source_case_status_id?: number;
}

// Additional sync request types
interface AttorneyLienSyncRequest extends BaseSyncRequest {
    attorney_lien_id: number;
}

interface MiscellaneousLienSyncRequest extends BaseSyncRequest {
    miscellaneous_lien_id: number;
}

interface ClientInsuranceSyncRequest extends BaseSyncRequest {
    source_insurance_id: number;
}

// Add expert witness sync request type
interface ExpertWitnessSyncRequest extends BaseSyncRequest {
    expert_witness_id: number;
}

interface HealthInsuranceSyncRequest extends BaseSyncRequest {
    source_insurance_id: number;
}

// Custom hook for defendant sync
export const useDefendantSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, DefendantSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['defendants'] });
            toast({
                title: "Success",
                description: data.message || "Defendant data synced successfully",
            });
        }
    });
};

// Custom hook for note sync
export const useNoteSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, NoteSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: NoteSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/notes/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['notes'] });
            toast({
                title: "Success",
                description: data.message || "Notes synced successfully",
            });
        }
    });
};

// Custom hook for task sync
export const useTaskSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, TaskSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: TaskSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/tasks/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['tasks'] });
            toast({
                title: "Success",
                description: data.message || "Tasks synced successfully",
            });
        }
    });
};

// Custom hook for user card sync
export const useUserCardSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, UserCardSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: UserCardSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/user-cards/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['user-cards'] });
            toast({
                title: "Success",
                description: data.message || "User cards synced successfully",
            });
        }
    });
};

// Custom hook for client sync
export const useClientSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, ClientSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: ClientSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/client/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['client'] });
            toast({
                title: "Success",
                description: data.message || "Client data synced successfully",
            });
        }
    });
};

// Custom hook for incident details sync
export const useIncidentDetailsSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, IncidentDetailsSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: IncidentDetailsSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/incident-details/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['incident-details'] });
            toast({
                title: "Success",
                description: data.message || "Incident details synced successfully",
            });
        }
    });
};

// Custom hook for party sync
export const usePartySyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, PartySyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: PartySyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/parties/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            toast({
                title: "Success",
                description: "Party synced successfully",
            });
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: ["caseParties", caseId] });
        },
        onError: (error) => {
            console.error("Failed to sync party:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to sync party",
            });
        }
    });
};

// Custom hook for treatment provider sync
export const useTreatmentProviderSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, TreatmentProviderSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: TreatmentProviderSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/treatment-providers/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['treatment-providers'] });
            toast({
                title: "Success",
                description: data.message || "Treatment providers synced successfully",
            });
        }
    });
};

// Custom hook for lien sync
export const useLienSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, LienSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: LienSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/liens/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['liens'] });
            toast({
                title: "Success",
                description: data.message || "Liens synced successfully",
            });
        }
    });
};

// Custom hook for worker sync
export const useWorkerSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, WorkerSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: WorkerSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/workers/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['workers'] });
            toast({
                title: "Success",
                description: data.message || "Workers synced successfully",
            });
        }
    });
};

// Custom hook for case status sync
export const useCaseStatusSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, StatusSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: StatusSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/case-status/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data, variables) => {
            // Invalidate the case queries for both source and target cases
            queryClient.invalidateQueries({ queryKey: ['case', caseId] });
            if (variables.target_case_ids) {
                variables.target_case_ids.forEach(targetId => {
                    queryClient.invalidateQueries({ queryKey: ['case', targetId] });
                });
            }
            toast({
                title: "Success",
                description: data.message || "Case status synced successfully",
            });
        }
    });
};

// Custom hook for attorney lien sync
export const useAttorneyLienSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, AttorneyLienSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: AttorneyLienSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/attorney-liens/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['attorney-liens'] });
            toast({
                title: "Success",
                description: data.message || "Attorney liens synced successfully",
            });
        }
    });
};

// Custom hook for miscellaneous lien sync
export const useMiscellaneousLienSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, MiscellaneousLienSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: MiscellaneousLienSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/miscellaneous-liens/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['miscellaneous-liens'] });
            toast({
                title: "Success",
                description: data.message || "Miscellaneous liens synced successfully",
            });
        }
    });
};

// Custom hook for client insurance sync
export const useClientInsuranceSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, ClientInsuranceSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: ClientInsuranceSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/client-insurance/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            toast({
                title: "Success",
                description: "Insurance synced successfully",
            });
            queryClient.invalidateQueries({ queryKey: ["clientInsurance", caseId] });
        },
        onError: (error) => {
            console.error("Failed to sync insurance:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to sync insurance",
            });
        },
    });
};

// Custom hook for expert witness sync
export const useExpertWitnessSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, ExpertWitnessSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: ExpertWitnessSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['expert-witnesses'] });
            toast({
                title: "Success",
                description: data.message || "Expert witness synced successfully",
            });
        }
    });
};

// Custom hook for health insurance sync
export const useHealthInsuranceSyncMutation = (
    caseId: string
): UseMutationResult<BaseSyncResponse, Error, HealthInsuranceSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: HealthInsuranceSyncRequest) => {
            const response = await axios.post<BaseSyncResponse>(
                `${BASE_URL}/cases/${caseId}/health-insurances/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            toast({
                title: "Success",
                description: "Health insurance synced successfully",
            });
            queryClient.invalidateQueries({ queryKey: ["healthInsurances", caseId] });
        },
        onError: (error) => {
            console.error("Failed to sync health insurance:", error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to sync health insurance",
            });
        },
    });
}; 