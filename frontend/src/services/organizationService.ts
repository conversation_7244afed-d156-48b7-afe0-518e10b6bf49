import { useQuery, useMutation, useQuery<PERSON>lient, UseQueryResult, UseMutationResult, UseQueryOptions } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { PlatformSection } from '@/type/platformSections';
import { AdminSubsection } from '@/type/platformSections';



export interface OrganizationProfile {
    address?: string;
    phone?: string;
    website?: string;
    description?: string;
}

export interface Organization {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    profile: OrganizationProfile;
}

export interface OrganizationUser {
    id: number;
    email: string;
    role: 'admin' | 'general' | 'attorney' | 'paralegal' | 'legal_assistant' | 'managing_partner' | 'case_manager' | 'intake_user' | 'marketing_user';
    first_name: string | null;
    last_name: string | null;
    username: string;
    verified: boolean;
    is_active: boolean;
    created_at: string;
    last_login: string | null;
    tags: UserTag[];
}

export interface OrganizationInvite {
    id: number;
    email: string;
    role: 'admin' | 'general';
    status: 'pending' | 'accepted' | 'revoked' | 'expired';
    expires_at: string;
    created_at: string;
    invited_by: {
        id: number;
        email: string;
    };
    organization: {
        id: number;
        name: string;
    };
}

export interface CreateInviteRequest {
    email: string;
    role: 'admin' | 'general';
    message?: string;
}

export interface InviteDetails {
    email: string;
    organization: string;
    role: 'admin' | 'general';
    needs_account: boolean;
    expires_at: string;
}

export interface AcceptInviteRequest {
    password?: string;
    name?: string;
}

export interface AcceptInviteResponse {
    refresh: string;
    access: string;
    user: {
        id: number;
        email: string;
        role: string;
        name: string;
    };
}

export interface RevokeInviteResponse {
    status: string;
    revoked_at: string;
    revoked_by: {
        id: number;
        email: string;
    };
}

export interface UpdateOrganizationRequest {
    name: string;
    profile?: OrganizationProfile;
}

export interface ResendInviteResponse {
    message: string;
    invite: OrganizationInvite;
}

// Organization Case Status and Checklist Types
export interface OrganizationCaseStatusChecklist {
    id?: number;
    status: number;
    item_name: string;
    description?: string;
    is_required: boolean;
    order: number;
    kpi_type?: KPIType; // Optional KPI type for checklist items
    created_at?: string;
    updated_at?: string;
}

export interface OrganizationCaseStatus {
    id?: number;
    organization?: number;
    name: string;
    display_name: string;
    description?: string;
    legacy_status?: string;
    legacy_status_display?: string;
    order: number;
    is_active: boolean;
    kpi_type?: KPIType; // Optional KPI type for case status
    checklist_items?: OrganizationCaseStatusChecklist[];
    created_at?: string;
    updated_at?: string;
}

// KPI Types enum based on the Django model
export enum KPIType {
   // Leads/Intake KPIs - All Implemented
   LEADS_COMPLETED = "leads_completed",
   LEADS_CONVERTED_TO_CASES = "leads_converted_to_cases",
   LEADS_LOST = "leads_lost",
   CALLS_WITHIN_FIRST_HOUR = "calls_within_first_hour",
   INTAKE_COMPLETED = "intake_completed",

    // Pre-lit KPIs
    CASE_DEMAND_READY = "case_demand_ready",
    CASE_DROPPED = "case_dropped",
    CASE_CLOSED = "case_closed",
    CASES_TOUCHED = "cases_touched",
    TREATMENT_PERIOD = "treatment_period",
    AVG_CASE_AGE = "avg_case_age",
    NOTES_ENTERED = "notes_entered",
    THREE_PLUS_PROVIDERS_COUNT = "three_plus_providers_count",

    // Negotiation KPIs
    TOTAL_CASES_IN_NEGOTIATION = "total_cases_in_negotiation",
    TOTAL_ATTORNEY_FEES = "total_attorney_fees",

    // Litigation KPIs
    DEPOSITIONS_TAKEN = "depositions_taken",
    MEDIATIONS_ARB_TRIALS = "mediations_arb_trials",
    HEARINGS = "hearings",

    // Settlement KPIs
    CASE_IN_SETTLEMENT = "case_in_settlement",
    LITIGATION_CASES_TOUCHED = "litigation_cases_touched",

    CASE_OPEN = "case_open",
    REFERRED_IN = "referred_in",
    SUBBED_OUT = "subbed_out",
    INTAKE_READY = "intake_ready",
    CASE_REJECTED = "case_rejected",
    PENDING_DROP = "pending_drop"
}

export interface OneDriveTokens {
    access_token: string;
    refresh_token: string;
    expires_at: string;
}

export interface OneDriveConnectionStatus {
    is_connected: boolean;
    tokens?: OneDriveTokens;
}

export interface UserTag {
    id: number;
    name: string;
    description: string;
    is_default: boolean;
    platform_sections: PlatformSection[];  // Using the enum
    platform_subsections: {                // Using the enum values
        [key in PlatformSection]?: string[];
    };
    admin_sections: AdminSubsection[];     // Using the enum
    created_at: string;
    updated_at: string;
}

/**
 * Create User Tag Input
 * Type for creating a new user tag
 */
export interface CreateUserTagInput {
    name: string;
    description: string;
    platform_sections: PlatformSection[];
    platform_subsections: {
        [key in PlatformSection]?: string[];
    };
    admin_sections: AdminSubsection[];
}

/**
 * Update User Tag Input
 * Type for updating an existing user tag
 */
export interface UpdateUserTagInput {
    name?: string;
    description?: string;
    platform_sections?: PlatformSection[];
    platform_subsections?: {
        [key in PlatformSection]?: string[];
    };
    admin_sections?: AdminSubsection[];
}

export interface CaseNoteTag {
    id: number;
    name: string;
    description?: string;
    is_default: boolean;
    created_at?: string;
}

export interface BulkCreateCaseNoteTagsRequest {
    tags: Omit<CaseNoteTag, 'id' | 'created_at'>[];
}

export interface UpdateUserTagsRequest {
    tag_ids: number[];
}

export interface UpdateOrganizationUserRequest {
    first_name?: string;
    last_name?: string;
    role?: 'admin' | 'general' | 'attorney' | 'paralegal' | 'legal_assistant' | 'managing_partner' | 'case_manager' | 'intake_user' | 'marketing_user';
    is_active?: boolean;
}

export interface BulkCreateChecklistRequest {
    status: number;
    checklists: Array<{
        item_name: string;
        description?: string;
        is_required: boolean;
        order: number;
    }>;
}

export interface CaseNoteTagResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CaseNoteTag[];
}

// Add this new interface for the request type
export interface UpdateStatusOrderRequest {
    orders: Array<{
        id: number;
        order: number;
    }>;
}

// Add these new interfaces
export interface SourceDetail {
    id: number;
    name: string;
    description?: string;
    created_at: string;
    updated_at: string;
}

export interface SourceTag {
    id: number;
    name: string;
    description?: string;
    created_at: string;
    updated_at: string;
}

// Add these new interfaces
export interface OrganizationFolder {
    id: number;
    name: string;
    description: string;
    is_enabled: boolean;
    sections_connected: string[] | null;
    created_at: string;
    updated_at: string;
}

export interface CreateFolderRequest {
    name: string;
    description: string;
    is_enabled: boolean;
    sections_connected: string[] | null;
}

// Define the required parameters interface
interface SectionRequiredParams {
    medical_treatment_id: boolean;
    employment_record_id: boolean;
}

// Define the section details interface
interface SectionDetails {
    name: string;
    required_params: SectionRequiredParams;
}

// Update the main interface
export interface SectionConnectedOptions {
    [key: string]: SectionDetails;
}

export interface DocumentTypeOptions {
    [key: string]: string;
}

export interface TerminateUserRequest {
    user_id: number;
    transfer_to_user_id: number;
}

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/users';
const CASE_MANAGEMENT_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/case-management';
const ORG_MANAGEMENT_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/organization-management';

/**
 * Get Organization Details
 *
 * Retrieves details of the current user's organization
 *
 * Endpoint: GET /organizations/current/
 * Authentication: Required
 */
export const useCurrentOrganization = (): UseQueryResult<Organization> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['organization'],
        queryFn: async () => {
            const { data } = await axios.get<Organization>(
                `${BASE_URL}/organizations/current/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Update Organization Details
 *
 * Updates organization details
 *
 * Endpoint: PUT /organizations/current/
 * Authentication: Required (Admin only)
 */
export const useUpdateOrganization = (): UseMutationResult<Organization, Error, UpdateOrganizationRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UpdateOrganizationRequest) => {
            const response = await axios.put<Organization>(
                `${BASE_URL}/organizations/current/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization'] });
            toast({
                title: "Success",
                description: "Organization updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * List Organization Users
 *
 * Lists all users in the organization
 *
 * Endpoint: GET /organizations/current/users/
 * Authentication: Required (Admin only)
 * @param {string} search - Optional search term to filter users
 * @param {Object} options - Optional React Query options
 */
export const useOrganizationUsers = (search?: string, options?: Partial<UseQueryOptions<OrganizationUser[], Error, OrganizationUser[]>>): UseQueryResult<OrganizationUser[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['organization-users', search],
        queryFn: async () => {
            const params = search ? { search } : undefined;
            const { data } = await axios.get<OrganizationUser[]>(
                `${BASE_URL}/organizations/current/users/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken,
        ...options
    });
};

/**
 * Remove Organization User
 *
 * Removes a user from the organization
 * Cannot remove admin users
 *
 * Endpoint: DELETE /organizations/current/users/{user_id}/
 * Authentication: Required (Admin only)
 */
export const useRemoveOrganizationUser = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (userId: number) => {
            await axios.delete(
                `${BASE_URL}/organizations/current/users/${userId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-users'] });
            toast({
                title: "Success",
                description: "User removed successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Create Invite
 *
 * Creates a new invitation to join the organization
 *
 * Endpoint: POST /organizations/current/invites/
 * Authentication: Required (Admin only)
 */
export const useCreateInvite = (): UseMutationResult<OrganizationInvite, Error, CreateInviteRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CreateInviteRequest) => {
            const response = await axios.post<OrganizationInvite>(
                `${BASE_URL}/organizations/current/invites/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['pending-invites'] });
            toast({
                title: "Success",
                description: "Invitation sent successfully",
                variant: "default"
            });
        }
    });
};

/**
 * List Pending Invites
 *
 * Lists all pending invitations
 *
 * Endpoint: GET /organizations/current/invites/
 * Authentication: Required (Admin only)
 */
export const usePendingInvites = (status?: string): UseQueryResult<OrganizationInvite[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['pending-invites', status],
        queryFn: async () => {
            const params = status ? { status } : undefined;
            const { data } = await axios.get<OrganizationInvite[]>(
                `${BASE_URL}/organizations/current/invites/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Revoke Invite
 *
 * Revokes a pending invitation
 *
 * Endpoint: POST /organizations/current/invites/{invite_id}/revoke/
 * Authentication: Required (Admin only)
 */
export const useRevokeInvite = (): UseMutationResult<RevokeInviteResponse, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (inviteId: number) => {
            const response = await axios.post<RevokeInviteResponse>(
                `${BASE_URL}/organizations/current/invites/${inviteId}/revoke/`,
                {},
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['pending-invites'] });
            toast({
                title: "Success",
                description: "Invitation revoked successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Get Invite Details
 *
 * Validates an invitation token and returns invite details
 * First step of the two-step invite acceptance process
 *
 * Endpoint: GET /organizations/invites/{token}/
 * Authentication: Not required
 */
export const useInviteDetails = (token: string): UseQueryResult<InviteDetails> => {
    return useQuery({
        queryKey: ['invite-details', token],
        queryFn: async () => {
            const { data } = await axios.get<InviteDetails>(
                `${BASE_URL}/organizations/invites/${token}/`
            );
            return data;
        },
        enabled: !!token,
        retry: false // Don't retry for invalid tokens
    });
};

/**
 * Accept Invite
 *
 * Accepts an invitation and joins the organization
 * Second step of the two-step invite acceptance process
 * For new users, requires a password in the request body
 *
 * Endpoint: POST /organizations/invites/{token}/
 * Authentication: Not required
 */
export const useAcceptInvite = (): UseMutationResult<AcceptInviteResponse, Error, { token: string; data?: AcceptInviteRequest }> => {
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ token, data }: { token: string; data?: AcceptInviteRequest }) => {
            const response = await axios.post<AcceptInviteResponse>(
                `${BASE_URL}/organizations/invites/${token}/`,
                data
            );
            return response.data;
        },
        onSuccess: (data) => {
            // Store the tokens
            localStorage.setItem('accessToken', data.access);
            localStorage.setItem('refreshToken', data.refresh);

            queryClient.invalidateQueries({ queryKey: ['organization'] });
            toast({
                title: "Success",
                description: "Welcome! You have successfully joined the organization.",
                variant: "default"
            });
        }
    });
};

/**
 * Resend Invite
 *
 * Resends the invitation email for an existing invite
 *
 * Endpoint: POST /organizations/current/invites/{invite_id}/resend/
 * Authentication: Required (Admin only)
 */
export const useResendInvite = (): UseMutationResult<ResendInviteResponse, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (inviteId: number) => {
            const response = await axios.post<ResendInviteResponse>(
                `${BASE_URL}/organizations/current/invites/${inviteId}/resend/`,
                {},
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['pending-invites'] });
            toast({
                title: "Success",
                description: data.message || "Invitation resent successfully",
                variant: "default"
            });
        }
    });
};

/**
 * List Organization Case Statuses
 *
 * Lists all case statuses for the organization
 *
 * Endpoint: GET /case-management/organization-case-statuses/
 * Authentication: Required
 */
export const useOrganizationCaseStatuses = (search?: string, ordering?: string): UseQueryResult<OrganizationCaseStatus[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['organization-case-statuses', search, ordering],
        queryFn: async () => {
            const params = { search, ordering };
            const { data } = await axios.get<OrganizationCaseStatus[]>(
                `${ORG_MANAGEMENT_URL}/organization-case-statuses/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Create Organization Case Status
 */
export const useCreateOrganizationCaseStatus = (): UseMutationResult<OrganizationCaseStatus, Error, Omit<OrganizationCaseStatus, 'id' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Omit<OrganizationCaseStatus, 'id' | 'created_at' | 'updated_at'>) => {
            const response = await axios.post<OrganizationCaseStatus>(
                `${ORG_MANAGEMENT_URL}/organization-case-statuses/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-statuses'] });
            toast({
                title: "Success",
                description: "Case status created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Organization Case Status
 */
export const useUpdateOrganizationCaseStatus = (): UseMutationResult<OrganizationCaseStatus, Error, { id: string; data: { organization_status: number } }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.patch<OrganizationCaseStatus>(
                `${ORG_MANAGEMENT_URL}/organization-case-statuses/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-statuses'] });
            toast({
                title: "Success",
                description: "Case status updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Delete Organization Case Status
 */
export const useDeleteOrganizationCaseStatus = (): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: string) => {
            await axios.delete(
                `${ORG_MANAGEMENT_URL}/organization-case-statuses/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-statuses'] });
            toast({
                title: "Success",
                description: "Case status deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * List Organization Case Checklists
 */
export const useOrganizationCaseChecklists = (): UseQueryResult<OrganizationCaseStatusChecklist[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['organization-case-checklists'],
        queryFn: async () => {
            const { data } = await axios.get<OrganizationCaseStatusChecklist[]>(
                `${ORG_MANAGEMENT_URL}/organization-case-checklists/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Create Organization Case Checklist Item
 */
export const useCreateOrganizationCaseChecklist = (): UseMutationResult<OrganizationCaseStatusChecklist, Error, Omit<OrganizationCaseStatusChecklist, 'id' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Omit<OrganizationCaseStatusChecklist, 'id' | 'created_at' | 'updated_at'>) => {
            const response = await axios.post<OrganizationCaseStatusChecklist>(
                `${ORG_MANAGEMENT_URL}/organization-case-checklists/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-checklists'] });
            toast({
                title: "Success",
                description: "Checklist item created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Organization Case Checklist Item
 */
export const useUpdateOrganizationCaseChecklist = (): UseMutationResult<OrganizationCaseStatusChecklist, Error, { id: string; data: Partial<OrganizationCaseStatusChecklist> }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.patch<OrganizationCaseStatusChecklist>(
                `${ORG_MANAGEMENT_URL}/organization-case-checklists/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-checklists'] });
            toast({
                title: "Success",
                description: "Checklist item updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Delete Organization Case Checklist Item
 */
export const useDeleteOrganizationCaseChecklist = (): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: string) => {
            await axios.delete(
                `${ORG_MANAGEMENT_URL}/organization-case-checklists/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-checklists'] });
            toast({
                title: "Success",
                description: "Checklist item deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Connect to OneDrive
 */
export const useConnectOneDrive = (): UseMutationResult<{ auth_url: string }, Error, void> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async () => {
            const response = await axios.get<{ auth_url: string }>(
                `${BASE_URL}/organizations/current/onedrive/connect/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            window.location.href = data.auth_url;
        }
    });
};

/**
 * Handle OneDrive OAuth Callback
 */
export const useHandleOneDriveCallback = (): UseMutationResult<OneDriveTokens, Error, { code: string }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ code }) => {
            const response = await axios.get<OneDriveTokens>(
                `${BASE_URL}/organizations/current/onedrive/callback/`,
                {
                    params: { code },
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['onedrive-status'] });
            toast({
                title: "Success",
                description: "Successfully connected to OneDrive",
                variant: "default"
            });
        }
    });
};

/**
 * Disconnect from OneDrive
 */
export const useDisconnectOneDrive = (): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.post(
                `${BASE_URL}/organizations/current/onedrive/disconnect/`,
                {},
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['onedrive-status'] });
            toast({
                title: "Success",
                description: "Successfully disconnected from OneDrive",
                variant: "default"
            });
        }
    });
};

/**
 * Get OneDrive Connection Status
 */
export const useOneDriveStatus = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['onedrive-status'],
        queryFn: async () => {
            const { data } = await axios.get<OneDriveConnectionStatus>(
                `${BASE_URL}/organizations/current/onedrive/status/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Connect to OneDrive Personal
 */
export const useConnectOneDrivePersonal = (): UseMutationResult<{ auth_url: string }, Error, void> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async () => {
            const response = await axios.get<{ auth_url: string }>(
                `${BASE_URL}/organizations/current/onedrive-personal/connect/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            window.location.href = data.auth_url;
        }
    });
};

/**
 * Handle OneDrive Personal OAuth Callback
 */
export const useHandleOneDrivePersonalCallback = (): UseMutationResult<OneDriveTokens, Error, { code: string }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ code }) => {
            const response = await axios.get<OneDriveTokens>(
                `${BASE_URL}/organizations/current/onedrive-personal/callback/`,
                {
                    params: { code },
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['onedrive-personal-status'] });
            toast({
                title: "Success",
                description: "Successfully connected to OneDrive Personal",
                variant: "default"
            });
        }
    });
};

/**
 * Disconnect from OneDrive Personal
 */
export const useDisconnectOneDrivePersonal = (): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.post(
                `${BASE_URL}/organizations/current/onedrive-personal/disconnect/`,
                {},
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['onedrive-personal-status'] });
            toast({
                title: "Success",
                description: "Successfully disconnected from OneDrive Personal",
                variant: "default"
            });
        }
    });
};

/**
 * Get OneDrive Personal Connection Status
 */
export const useOneDrivePersonalStatus = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['onedrive-personal-status'],
        queryFn: async () => {
            const { data } = await axios.get<OneDriveConnectionStatus>(
                `${BASE_URL}/organizations/current/onedrive-personal/status/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * List User Tags
 *
 * Lists all user tags in the organization
 *
 * Endpoint: GET /users/organizations/current/user-tags/
 * Authentication: Required
 */
export const useUserTags = (): UseQueryResult<UserTag[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['user-tags'],
        queryFn: async () => {
            const { data } = await axios.get<UserTag[]>(
                `${BASE_URL}/organizations/current/user-tags/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Create User Tag
 *
 * Creates a new user tag in the organization
 *
 * Endpoint: POST /users/organizations/current/user-tags/
 * Authentication: Required
 */
export const useCreateUserTag = (): UseMutationResult<UserTag, Error, CreateUserTagInput> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CreateUserTagInput) => {
            const response = await axios.post<UserTag>(
                `${BASE_URL}/organizations/current/user-tags/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-tags'] });
            toast({
                title: "Success",
                description: "User tag created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update User Tag
 *
 * Updates an existing user tag
 *
 * Endpoint: PUT /users/organizations/current/user-tags/{id}/
 * Authentication: Required
 */
export const useUpdateUserTag = (): UseMutationResult<UserTag, Error, { id: number; data: UpdateUserTagInput }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.put<UserTag>(
                `${BASE_URL}/organizations/current/user-tags/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-tags'] });
            toast({
                title: "Success",
                description: "User tag updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Delete User Tag
 *
 * Deletes a user tag
 *
 * Endpoint: DELETE /users/organizations/current/user-tags/{id}/
 * Authentication: Required
 */
export const useDeleteUserTag = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: number) => {
            await axios.delete(
                `${BASE_URL}/organizations/current/user-tags/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-tags'] });
            toast({
                title: "Success",
                description: "User tag deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * List Case Note Tags
 *
 * Lists all case note tags for the organization
 *
 * Endpoint: GET /case-management/tags/
 * Authentication: Required
 */
export const useCaseNoteTags = (): UseQueryResult<CaseNoteTag[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['case-note-tags'],
        queryFn: async () => {
            const { data } = await axios.get<CaseNoteTag[]>(
                `${ORG_MANAGEMENT_URL}/tags/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Create Case Note Tag
 *
 * Creates a new case note tag
 *
 * Endpoint: POST /case-management/tags/
 * Authentication: Required
 */
export const useCreateCaseNoteTag = (): UseMutationResult<CaseNoteTag, Error, Omit<CaseNoteTag, 'id' | 'created_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Omit<CaseNoteTag, 'id' | 'created_at'>) => {
            const response = await axios.post<CaseNoteTag>(
                `${ORG_MANAGEMENT_URL}/tags/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['case-note-tags'] });
            toast({
                title: "Success",
                description: "Case note tag created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Case Note Tag
 *
 * Updates an existing case note tag
 *
 * Endpoint: PUT /case-management/tags/{id}/
 * Authentication: Required
 */
export const useUpdateCaseNoteTag = (): UseMutationResult<CaseNoteTag, Error, { id: number; data: Partial<CaseNoteTag> }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.put<CaseNoteTag>(
                `${ORG_MANAGEMENT_URL}/tags/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['case-note-tags'] });
            toast({
                title: "Success",
                description: "Case note tag updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Delete Case Note Tag
 *
 * Deletes a case note tag
 *
 * Endpoint: DELETE /case-management/tags/{id}/
 * Authentication: Required
 */
export const useDeleteCaseNoteTag = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: number) => {
            await axios.delete(
                `${ORG_MANAGEMENT_URL}/tags/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['case-note-tags'] });
            toast({
                title: "Success",
                description: "Case note tag deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Bulk Create Case Note Tags
 *
 * Creates multiple case note tags at once
 *
 * Endpoint: POST /case-management/tags/bulk_create/
 * Authentication: Required
 */
export const useBulkCreateCaseNoteTags = (): UseMutationResult<CaseNoteTag[], Error, BulkCreateCaseNoteTagsRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: BulkCreateCaseNoteTagsRequest) => {
            const response = await axios.post<CaseNoteTag[]>(
                `${ORG_MANAGEMENT_URL}/tags/bulk_create/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['case-note-tags'] });
            toast({
                title: "Success",
                description: "Case note tags created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update User Tags
 *
 * Updates the tags for a specific user in the organization
 *
 * Endpoint: PUT /users/organizations/current/users/{userId}/tags/
 * Authentication: Required
 */
export const useUpdateUserTags = (): UseMutationResult<void, Error, { userId: number; data: UpdateUserTagsRequest }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ userId, data }) => {
            await axios.post(
                `${BASE_URL}/organizations/current/users/${userId}/tags/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-users'] });
            toast({
                title: "Success",
                description: "User tags updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Organization User
 *
 * Updates user details (name and role) for a specific user in the organization
 *
 * Endpoint: PATCH /users/organizations/current/users/{userId}/update/
 * Authentication: Required (Admin only)
 */
export const useUpdateOrganizationUser = (): UseMutationResult<OrganizationUser, Error, { userId: number; data: UpdateOrganizationUserRequest }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ userId, data }) => {
            const response = await axios.patch<OrganizationUser>(
                `${BASE_URL}/organizations/current/users/${userId}/update/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-users'] });
            toast({
                title: "Success",
                description: "User updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Bulk Create Checklist Items for a Status
 *
 * Creates multiple checklist items at once for a specific status
 *
 * Endpoint: POST /case-management/organization-case-checklists/{statusId}/bulk-create-checklist/
 * Authentication: Required
 */
export const useBulkCreateChecklist = (): UseMutationResult<OrganizationCaseStatusChecklist[], Error, BulkCreateChecklistRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: BulkCreateChecklistRequest) => {
            const response = await axios.post<OrganizationCaseStatusChecklist[]>(
                `${CASE_MANAGEMENT_URL}/organization-case-checklists/${data.status}/bulk-create-checklist/`,
                data.checklists,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-statuses'] });
            toast({
                title: "Success",
                description: "Checklist items created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Case Status Order
 *
 * Updates the order of multiple case statuses at once
 *
 * Endpoint: POST /case-management/organization-case-statuses/update-order/
 * Authentication: Required
 */
export const useUpdateCaseStatusOrder = (): UseMutationResult<void, Error, UpdateStatusOrderRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UpdateStatusOrderRequest) => {
            await axios.post(
                `${ORG_MANAGEMENT_URL}/organization-case-statuses/update_order/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-statuses'] });
            toast({
                title: "Success",
                description: "Status order updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Case Checklist Order
 *
 * Updates the order of multiple case checklist items at once
 *
 * Endpoint: POST /case-management/organization-case-checklists/update-order/
 * Authentication: Required
 */
export const useUpdateCaseChecklistOrder = (): UseMutationResult<void, Error, UpdateStatusOrderRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UpdateStatusOrderRequest) => {
            await axios.post(
                `${CASE_MANAGEMENT_URL}/organization-case-checklists/update_order/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-case-statuses'] });
            toast({
                title: "Success",
                description: "Status order updated successfully",
                variant: "default"
            });
        }
    });
};

export const useSourceDetails = (search?: string): UseQueryResult<SourceDetail[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['source-details', search],
        queryFn: async () => {
            const params = search ? { search } : undefined;
            const { data } = await axios.get<SourceDetail[]>(
                `${ORG_MANAGEMENT_URL}/source-details/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Create Source Detail
 */
export const useCreateSourceDetail = (): UseMutationResult<SourceDetail, Error, Omit<SourceDetail, 'id' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data) => {
            const response = await axios.post<SourceDetail>(
                `${ORG_MANAGEMENT_URL}/source-details/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['source-details'] });
            toast({
                title: "Success",
                description: "Source detail created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Source Detail
 */
export const useUpdateSourceDetail = (): UseMutationResult<SourceDetail, Error, { id: number; data: Partial<SourceDetail> }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.put<SourceDetail>(
                `${ORG_MANAGEMENT_URL}/source-details/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['source-details'] });
            toast({
                title: "Success",
                description: "Source detail updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Delete Source Detail
 */
export const useDeleteSourceDetail = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: number) => {
            await axios.delete(
                `${ORG_MANAGEMENT_URL}/source-details/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['source-details'] });
            toast({
                title: "Success",
                description: "Source detail deleted successfully",
                variant: "default"
            });
        }
    });
};

// Similar hooks for source tags
export const useCreateSourceTag = (): UseMutationResult<SourceTag, Error, Omit<SourceTag, 'id' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data) => {
            const response = await axios.post<SourceTag>(
                `${ORG_MANAGEMENT_URL}/source-tags/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['source-tags'] });
            toast({
                title: "Success",
                description: "Source tag created successfully",
                variant: "default"
            });
        }
    });
};

export const useUpdateSourceTag = (): UseMutationResult<SourceTag, Error, { id: number; data: Partial<SourceTag> }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.put<SourceTag>(
                `${ORG_MANAGEMENT_URL}/source-tags/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['source-tags'] });
            toast({
                title: "Success",
                description: "Source tag updated successfully",
                variant: "default"
            });
        }
    });
};

export const useDeleteSourceTag = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: number) => {
            await axios.delete(
                `${ORG_MANAGEMENT_URL}/source-tags/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['source-tags'] });
            toast({
                title: "Success",
                description: "Source tag deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * List Source Tags
 */
export const useSourceTags = (search?: string): UseQueryResult<SourceTag[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['source-tags', search],
        queryFn: async () => {
            const params = search ? { search } : undefined;
            const { data } = await axios.get<SourceTag[]>(
                `${ORG_MANAGEMENT_URL}/source-tags/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Get Single Source Detail
 */
export const useSourceDetail = (id: number): UseQueryResult<SourceDetail> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['source-detail', id],
        queryFn: async () => {
            const { data } = await axios.get<SourceDetail>(
                `${ORG_MANAGEMENT_URL}/source-details/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken && !!id
    });
};

/**
 * Get Single Source Tag
 */
export const useSourceTag = (id: number): UseQueryResult<SourceTag> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['source-tag', id],
        queryFn: async () => {
            const { data } = await axios.get<SourceTag>(
                `${ORG_MANAGEMENT_URL}/source-tags/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken && !!id
    });
};

/**
 * List Organization Folders
 */
export const useOrganizationFolders = (search?: string): UseQueryResult<OrganizationFolder[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['organization-folders', search],
        queryFn: async () => {
            const { data } = await axios.get<OrganizationFolder[]>(
                `${ORG_MANAGEMENT_URL}/folders/`,
                {
                    params: { search },
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Get Section Connected Options
 */
export const useSectionConnectedOptions = (): UseQueryResult<SectionConnectedOptions> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['section-connected-options'],
        queryFn: async () => {
            const { data } = await axios.get<SectionConnectedOptions>(
                `${ORG_MANAGEMENT_URL}/folders/section_connected_options/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Create Organization Folder
 */
export const useCreateFolder = (): UseMutationResult<OrganizationFolder, Error, CreateFolderRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CreateFolderRequest) => {
            const response = await axios.post<OrganizationFolder>(
                `${ORG_MANAGEMENT_URL}/folders/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-folders'] });
            toast({
                title: "Success",
                description: "Folder created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Update Organization Folder
 */
export const useUpdateFolder = (): UseMutationResult<OrganizationFolder, Error, { id: number; data: Partial<CreateFolderRequest> }> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ id, data }) => {
            const response = await axios.put<OrganizationFolder>(
                `${ORG_MANAGEMENT_URL}/folders/${id}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-folders'] });
            toast({
                title: "Success",
                description: "Folder updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Delete Organization Folder
 */
export const useDeleteFolder = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (id: number) => {
            await axios.delete(
                `${ORG_MANAGEMENT_URL}/folders/${id}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-folders'] });
            toast({
                title: "Success",
                description: "Folder deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Terminate Organization User
 *
 * Terminates a user and transfers their data to another user
 *
 * Endpoint: POST /api/v1/users/terminate-user/
 * Authentication: Required (Admin only)
 */
export const useTerminateOrganizationUser = (): UseMutationResult<void, Error, TerminateUserRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: TerminateUserRequest) => {
            await axios.post(
                `${BASE_URL}/terminate-user/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['organization-users'] });
            toast({
                title: "Success",
                description: "User terminated successfully",
                variant: "default"
            });
        }
    });
};



