{"name": "alpha-law", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 4200", "build": "next build", "start": "next start -p 4200", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-build-decoupled-document": "^41.4.2", "@ckeditor/ckeditor5-react": "^6.3.0", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.1", "@microsoft/clarity": "^1.0.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.5", "@react-google-maps/api": "^2.20.6", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf/renderer": "^4.1.6", "@reduxjs/toolkit": "^2.3.0", "@sentry/nextjs": "^9.5.0", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.59.20", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.13", "@types/papaparse": "^5.3.15", "@types/react-big-calendar": "^1.16.0", "@types/react-color": "^3.0.13", "@types/react-query": "^1.2.9", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^2.30.0", "docx": "^9.5.0", "dompurify": "^3.2.3", "file-saver": "^2.0.5", "framer-motion": "^12.5.0", "fs": "^0.0.1-security", "html2canvas": "^1.4.1", "is-hotkey": "^0.2.0", "js-cookie": "^3.0.5", "jsdom": "^26.0.0", "jspdf": "^2.5.2", "lodash": "^4.17.21", "lucide-react": "^0.456.0", "next": "15.0.3", "papaparse": "^5.5.2", "pdfjs-dist": "^3.4.120", "react": "^18.3.1", "react-big-calendar": "^1.17.0", "react-color": "^2.19.3", "react-confetti": "^6.2.2", "react-day-picker": "^8.10.1", "react-doc-viewer": "^0.1.14", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.1", "react-intersection-observer": "^9.14.0", "react-markdown": "^9.0.1", "react-pdf": "^9.2.1", "react-rating-stars-component": "^2.2.0", "react-redux": "^9.1.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.0", "shadcn-ui": "^0.9.3", "slate": "^0.112.0", "slate-history": "^0.110.3", "slate-react": "^0.112.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@ckeditor/ckeditor5-core": "^45.1.0", "@ckeditor/ckeditor5-engine": "^45.1.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/ckeditor__ckeditor5-build-classic": "^29.0.1", "@types/ckeditor__ckeditor5-core": "^33.0.3", "@types/file-saver": "^2.0.7", "@types/html-to-docx": "^1.8.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18.3.16", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "15.0.3", "husky": "^9.1.7", "postcss": "^8.4.49", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.14", "typescript": "^5"}}